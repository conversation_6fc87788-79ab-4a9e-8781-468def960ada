# JobbLogg - Professional Job Logging Application

A modern TypeScript React application for logging job progress with AI-powered image captioning, QR code sharing, and real-time collaboration.

## Features

- **Project Management**: Create and manage multiple job logging projects
- **Log Entries**: Add detailed log entries with text and images
- **AI Image Captioning**: Automatically generate captions for uploaded images using OpenAI Vision API
- **QR Code Sharing**: Generate QR codes for public project viewing
- **Public Viewer**: Share read-only project views with clients or stakeholders
- **Authentication**: Secure user authentication with magic link support via Clerk
- **Real-time Database**: Powered by Convex for real-time data synchronization
- **Mobile-First Design**: Responsive design optimized for mobile devices

## Tech Stack

- **Frontend**: React 18, TypeScript, Vite
- **Styling**: Tailwind CSS
- **Authentication**: Clerk
- **Backend/Database**: Convex
- **AI Integration**: OpenAI Vision API
- **QR Codes**: react-qr-code
- **Routing**: React Router

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Clerk account (for authentication)
- Convex account (for backend)
- OpenAI API key (for image captioning)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd JobbLogg
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
```

4. Configure your environment variables in `.env.local`:
```env
# Convex (automatically configured)
CONVEX_DEPLOYMENT=dev:your-deployment-name
VITE_CONVEX_URL=https://your-deployment.convex.cloud

# Clerk Authentication
VITE_CLERK_PUBLISHABLE_KEY=pk_test_your_clerk_publishable_key
CLERK_SECRET_KEY=sk_test_your_clerk_secret_key

# OpenAI API
OPENAI_API_KEY=sk-your_openai_api_key
```

5. Initialize Convex:
```bash
npx convex dev
```

6. Start the development server:
```bash
npm run dev
```

## Usage

### For Logged-in Users:

1. **Create a Project**: Click "New Project" to create a job logging project
2. **Add Log Entries**: Navigate to your project and add text/image log entries
3. **Generate Captions**: Upload images and use AI to generate professional captions
4. **Share Projects**: Generate QR codes or share public URLs for read-only access

### For Public Viewers:

1. **Scan QR Code**: Use any QR code scanner to access public project views
2. **View Progress**: See all project log entries in a clean, read-only interface

## Project Structure

```
src/
├── components/          # Reusable UI components
├── pages/              # Page components
├── types/              # TypeScript type definitions
├── utils/              # Utility functions
└── main.tsx           # Application entry point

convex/
├── schema.ts          # Database schema
├── projects.ts        # Project-related functions
├── logs.ts           # Log entry functions
└── ai.ts             # AI integration functions
```

## API Endpoints

The application uses Convex functions for all backend operations:

- `projects.create` - Create new project
- `projects.getByUser` - Get user's projects
- `projects.getById` - Get project by ID
- `projects.getBySharedId` - Get project by shared ID
- `logs.create` - Create log entry
- `logs.getByProject` - Get project logs
- `ai.generateImageCaption` - Generate AI captions

## Environment Setup

Make sure to configure the following services:

1. **Clerk**: Set up authentication with magic link support
2. **Convex**: Configure your backend deployment
3. **OpenAI**: Get API key for image captioning features

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.
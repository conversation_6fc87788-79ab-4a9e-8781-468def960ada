import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

export const create = mutation({
  args: {
    projectId: v.id("projects"),
    text: v.string(),
    imageUrl: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const logId = await ctx.db.insert("logs", {
      projectId: args.projectId,
      text: args.text,
      imageUrl: args.imageUrl,
      createdAt: Date.now(),
    });

    return logId;
  },
});

export const getByProject = query({
  args: {
    projectId: v.id("projects"),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("logs")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .order("desc")
      .collect();
  },
});

export const getByProjectSharedId = query({
  args: {
    sharedId: v.string(),
  },
  handler: async (ctx, args) => {
    // First get the project by shared ID
    const project = await ctx.db
      .query("projects")
      .withIndex("by_shared_id", (q) => q.eq("sharedId", args.sharedId))
      .first();
    
    if (!project) {
      return [];
    }

    // Then get all logs for that project
    return await ctx.db
      .query("logs")
      .withIndex("by_project", (q) => q.eq("projectId", project._id))
      .order("desc")
      .collect();
  },
});

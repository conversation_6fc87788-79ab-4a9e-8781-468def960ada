import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  projects: defineTable({
    name: v.string(),
    description: v.string(),
    userId: v.string(),
    sharedId: v.string(),
    createdAt: v.number(),
  })
    .index("by_user", ["userId"])
    .index("by_shared_id", ["sharedId"]),

  logs: defineTable({
    projectId: v.id("projects"),
    text: v.string(),
    imageUrl: v.optional(v.string()),
    createdAt: v.number(),
  })
    .index("by_project", ["projectId"])
    .index("by_created_at", ["createdAt"]),
});

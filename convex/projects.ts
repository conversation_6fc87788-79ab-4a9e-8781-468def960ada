import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { nanoid } from "nanoid";

export const create = mutation({
  args: {
    name: v.string(),
    description: v.string(),
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    const sharedId = nanoid(10);
    
    const projectId = await ctx.db.insert("projects", {
      name: args.name,
      description: args.description,
      userId: args.userId,
      sharedId,
      createdAt: Date.now(),
    });

    return { projectId, sharedId };
  },
});

export const getByUser = query({
  args: {
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("projects")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .order("desc")
      .collect();
  },
});

export const getById = query({
  args: {
    projectId: v.id("projects"),
  },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.projectId);
  },
});

export const getBySharedId = query({
  args: {
    sharedId: v.string(),
  },
  handler: async (ctx, args) => {
    const project = await ctx.db
      .query("projects")
      .withIndex("by_shared_id", (q) => q.eq("sharedId", args.sharedId))
      .first();
    
    return project;
  },
});

import { action } from "./_generated/server";
import { v } from "convex/values";

export const generateImageCaption = action({
  args: {
    imageBase64: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      // Import OpenAI dynamically to avoid issues with process.env
      const { default: OpenAI } = await import("openai");

      const openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
      });

      const response = await openai.chat.completions.create({
        model: "gpt-4-vision-preview",
        messages: [
          {
            role: "user",
            content: [
              {
                type: "text",
                text: "Please provide a detailed caption for this image that would be suitable for a job logging application. Focus on describing what work is being done, tools being used, or progress being made. Keep it professional and concise.",
              },
              {
                type: "image_url",
                image_url: {
                  url: args.imageBase64,
                },
              },
            ],
          },
        ],
        max_tokens: 300,
      });

      const caption = response.choices[0]?.message?.content;

      if (!caption) {
        throw new Error("No caption generated");
      }

      return { caption };
    } catch (error) {
      console.error("Error generating caption:", error);
      throw new Error("Failed to generate caption. Please try again.");
    }
  },
});

{"name": "jobblogg", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@clerk/clerk-react": "^5.32.1", "@convex-dev/auth": "^0.0.87", "@types/react-router-dom": "^5.3.3", "autoprefixer": "^10.4.21", "convex": "^1.25.0", "nanoid": "^5.1.5", "openai": "^5.7.0", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-qr-code": "^2.0.16", "react-router-dom": "^7.6.2", "tailwindcss": "^4.1.10"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0"}}
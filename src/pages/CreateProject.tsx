import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useUser } from '@clerk/clerk-react';
import { useMutation } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { Button, Input, Textarea, Card, CardHeader, CardTitle, CardContent } from '../components';
import { useLocale } from '../contexts/LocaleContext';

export function CreateProject() {
  const navigate = useNavigate();
  const { user } = useUser();
  const { texts } = useLocale();
  const createProject = useMutation(api.projects.create);
  
  const [formData, setFormData] = useState({
    name: '',
    description: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.name.trim()) {
      newErrors.name = texts.projectNameRequired;
    } else if (formData.name.trim().length < 3) {
      newErrors.name = texts.projectNameMinLength;
    }

    if (!formData.description.trim()) {
      newErrors.description = texts.projectDescriptionRequired;
    } else if (formData.description.trim().length < 10) {
      newErrors.description = texts.projectDescriptionMinLength;
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm() || !user) return;
    
    setIsSubmitting(true);
    
    try {
      const result = await createProject({
        name: formData.name.trim(),
        description: formData.description.trim(),
        userId: user.id,
      });
      
      navigate(`/project/${result.projectId}`);
    } catch (error) {
      console.error('Error creating project:', error);
      setErrors({ submit: texts.failedToCreateProject });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">{texts.createProject}</h1>
        <p className="mt-2 text-gray-600">
          {texts.startNewProject}
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{texts.projectDetails}</CardTitle>
        </CardHeader>
        
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <Input
              label={texts.projectName}
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              error={errors.name}
              placeholder={texts.enterProjectName}
              required
            />

            <Textarea
              label={texts.description}
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              error={errors.description}
              placeholder={texts.describeProject}
              required
            />

            {errors.submit && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <p className="text-sm text-red-600">{errors.submit}</p>
              </div>
            )}

            <div className="flex justify-end space-x-4">
              <Button
                type="button"
                variant="secondary"
                onClick={() => navigate('/')}
              >
                {texts.cancel}
              </Button>
              <Button
                type="submit"
                loading={isSubmitting}
                disabled={isSubmitting}
              >
                {texts.createProject}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}

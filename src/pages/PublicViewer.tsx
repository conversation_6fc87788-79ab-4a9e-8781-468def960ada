import { useParams } from 'react-router-dom';
import { useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { Card, CardContent, LoadingSpinner } from '../components';
import { useLocale } from '../contexts/LocaleContext';

export function PublicViewer() {
  const { sharedId } = useParams<{ sharedId: string }>();
  const { texts } = useLocale();
  
  const project = useQuery(api.projects.getBySharedId, 
    sharedId ? { sharedId } : "skip"
  );
  const logs = useQuery(api.logs.getByProjectSharedId, 
    sharedId ? { sharedId } : "skip"
  );

  if (project === undefined || logs === undefined) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!project) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
            <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">{texts.projectNotFound}</h1>
          <p className="text-gray-600">{texts.projectNotFoundMessage}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <span className="text-xl font-bold text-gray-900">{texts.appTitle}</span>
              <span className="ml-2 text-gray-400">•</span>
              <span className="ml-2 text-gray-600">{texts.publicView}</span>
            </div>
            <div className="text-sm text-gray-500">
              {texts.readOnlyView}
            </div>
          </div>
        </div>
      </header>

      {/* Content */}
      <main className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* Project Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">{project.name}</h1>
          <p className="text-gray-600 text-lg">{project.description}</p>
          <div className="mt-4 text-sm text-gray-500">
            {texts.createdOn} {new Date(project.createdAt).toLocaleDateString()}
          </div>
        </div>

        {/* Log Entries */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">{texts.projectLog}</h2>
            <span className="text-sm text-gray-500">
              {logs.length} {logs.length === 1 ? texts.entry : texts.entries}
            </span>
          </div>
          
          {logs.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                  <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">{texts.noLogEntries}</h3>
                <p className="text-gray-600">
                  {texts.noLogEntriesPublic}
                </p>
              </CardContent>
            </Card>
          ) : (
            logs.map((log) => (
              <Card key={log._id}>
                <CardContent>
                  <div className="flex justify-between items-start mb-4">
                    <span className="text-sm text-gray-500">
                      {new Date(log.createdAt).toLocaleString()}
                    </span>
                  </div>
                  
                  <div className="prose prose-sm max-w-none">
                    <p className="text-gray-900 whitespace-pre-wrap leading-relaxed">
                      {log.text}
                    </p>
                  </div>
                  
                  {log.imageUrl && (
                    <div className="mt-4">
                      <img
                        src={log.imageUrl}
                        alt={texts.logAttachment}
                        className="max-w-full h-auto rounded-lg border shadow-sm"
                      />
                    </div>
                  )}
                </CardContent>
              </Card>
            ))
          )}
        </div>

        {/* Footer */}
        <div className="mt-12 pt-8 border-t border-gray-200 text-center">
          <p className="text-sm text-gray-500">
            {texts.poweredBy} <span className="font-medium">{texts.appTitle}</span> - {texts.professionalJobLogging}
          </p>
        </div>
      </main>
    </div>
  );
}

import { SignIn as ClerkSignIn } from '@clerk/clerk-react';
import { useLocale } from '../contexts/LocaleContext';

export function SignIn() {
  const { texts } = useLocale();

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">{texts.appTitle}</h1>
          <h2 className="text-xl text-gray-600">{texts.signInToAccount}</h2>
        </div>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <ClerkSignIn 
            routing="path" 
            path="/sign-in"
            signUpUrl="/sign-up"
            redirectUrl="/"
          />
        </div>
      </div>
    </div>
  );
}

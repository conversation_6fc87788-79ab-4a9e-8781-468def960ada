import { useState, useRef } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { useQuery, useMutation, useAction } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { Button, Input, Textarea, Card, CardHeader, CardTitle, CardContent, LoadingSpinner } from '../components';
import QRCode from 'react-qr-code';

export function ProjectLog() {
  const { projectId } = useParams<{ projectId: string }>();
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const project = useQuery(api.projects.getById,
    projectId ? { projectId: projectId as any } : "skip"
  );
  const logs = useQuery(api.logs.getByProject,
    projectId ? { projectId: projectId as any } : "skip"
  );
  const createLog = useMutation(api.logs.create);
  const generateCaption = useAction(api.ai.generateImageCaption);

  const [formData, setFormData] = useState({
    text: '',
    imageUrl: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isGeneratingCaption, setIsGeneratingCaption] = useState(false);
  const [showQR, setShowQR] = useState(false);
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string>('');

  const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedImage(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleGenerateCaption = async () => {
    if (!imagePreview) return;

    setIsGeneratingCaption(true);

    try {
      const result = await generateCaption({
        imageBase64: imagePreview,
      });

      setFormData(prev => ({
        ...prev,
        text: prev.text ? `${prev.text}\n\n${result.caption}` : result.caption
      }));
    } catch (error) {
      console.error('Error generating caption:', error);
      alert('Failed to generate caption. Please try again.');
    } finally {
      setIsGeneratingCaption(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.text.trim() || !projectId) return;
    
    setIsSubmitting(true);
    
    try {
      // For now, we'll just use the image preview as imageUrl
      // In a real app, you'd upload to a service like Convex file storage
      await createLog({
        projectId: projectId as any,
        text: formData.text.trim(),
        imageUrl: imagePreview || undefined,
      });
      
      // Reset form
      setFormData({ text: '', imageUrl: '' });
      setSelectedImage(null);
      setImagePreview('');
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error) {
      console.error('Error creating log:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (project === undefined || logs === undefined) {
    return (
      <div className="flex justify-center items-center min-h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!project) {
    return (
      <div className="text-center py-12">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Project Not Found</h1>
        <Link to="/">
          <Button>Back to Dashboard</Button>
        </Link>
      </div>
    );
  }

  const publicUrl = `${window.location.origin}/v/${project.sharedId}`;

  return (
    <div className="max-w-4xl mx-auto">
      {/* Project Header */}
      <div className="mb-8">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{project.name}</h1>
            <p className="mt-2 text-gray-600">{project.description}</p>
          </div>
          <div className="flex space-x-2">
            <Button
              variant="secondary"
              onClick={() => setShowQR(!showQR)}
            >
              {showQR ? 'Hide QR' : 'Show QR'}
            </Button>
            <Link to={publicUrl} target="_blank">
              <Button variant="secondary">Public View</Button>
            </Link>
          </div>
        </div>

        {showQR && (
          <Card className="mb-6">
            <CardContent className="text-center">
              <div className="inline-block p-4 bg-white">
                <QRCode value={publicUrl} size={200} />
              </div>
              <p className="mt-2 text-sm text-gray-600">
                Scan to view project publicly
              </p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Add Log Form */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Add New Log Entry</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <Textarea
              label="Log Entry"
              value={formData.text}
              onChange={(e) => setFormData(prev => ({ ...prev, text: e.target.value }))}
              placeholder="Describe what you worked on..."
              required
            />

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Attach Image (Optional)
              </label>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleImageSelect}
                className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
              />
            </div>

            {imagePreview && (
              <div className="mt-4">
                <img
                  src={imagePreview}
                  alt="Preview"
                  className="max-w-xs h-auto rounded-md border"
                />
              </div>
            )}

            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="secondary"
                onClick={handleGenerateCaption}
                loading={isGeneratingCaption}
                disabled={!selectedImage || isGeneratingCaption}
              >
                Generate Caption
              </Button>
              <Button
                type="submit"
                loading={isSubmitting}
                disabled={isSubmitting || !formData.text.trim()}
              >
                Add Log Entry
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Log Entries */}
      <div className="space-y-6">
        <h2 className="text-xl font-semibold text-gray-900">Log Entries</h2>
        
        {logs.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <p className="text-gray-600">No log entries yet. Add your first entry above!</p>
            </CardContent>
          </Card>
        ) : (
          logs.map((log) => (
            <Card key={log._id}>
              <CardContent>
                <div className="flex justify-between items-start mb-3">
                  <span className="text-sm text-gray-500">
                    {new Date(log.createdAt).toLocaleString()}
                  </span>
                </div>
                
                <p className="text-gray-900 mb-4 whitespace-pre-wrap">{log.text}</p>
                
                {log.imageUrl && (
                  <img
                    src={log.imageUrl}
                    alt="Log attachment"
                    className="max-w-md h-auto rounded-md border"
                  />
                )}
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}

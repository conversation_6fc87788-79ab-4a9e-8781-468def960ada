import { ConvexProvider as BaseConvexProvider, ConvexReactClient } from "convex/react";
import { useAuth } from "@clerk/clerk-react";
import { ReactNode } from "react";

const convex = new ConvexReactClient(import.meta.env.VITE_CONVEX_URL as string);

interface ConvexProviderProps {
  children: ReactNode;
}

export function ConvexProvider({ children }: ConvexProviderProps) {
  const { getToken } = useAuth();

  return (
    <BaseConvexProvider 
      client={convex}
      auth={{
        getToken: () => getToken({ template: "convex" }),
        isLoading: false,
        isAuthenticated: false,
      }}
    >
      {children}
    </BaseConvexProvider>
  );
}

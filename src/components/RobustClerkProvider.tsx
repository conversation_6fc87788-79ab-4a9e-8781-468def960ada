import React, { useState, useEffect, ReactNode } from 'react';
import { <PERSON><PERSON>rovider } from '@clerk/clerk-react';
import { validateClerkConfiguration, testClerkConnectivity, logClerkDiagnostics } from '../utils/clerkValidator';

interface RobustClerkProviderProps {
  children: ReactNode;
  publishableKey: string;
  afterSignOutUrl?: string;
}

interface ClerkStatus {
  isHealthy: boolean;
  lastError?: string;
  retryCount: number;
  lastRetry?: Date;
}

const RobustClerkProvider: React.FC<RobustClerkProviderProps> = ({
  children,
  publishableKey,
  afterSignOutUrl = "/"
}) => {
  const [clerkStatus, setClerkStatus] = useState<ClerkStatus>({
    isHealthy: true,
    retryCount: 0
  });
  const [showFallback, setShowFallback] = useState(false);

  // Health check for Clerk API
  const checkClerkHealth = async (): Promise<boolean> => {
    try {
      // Extract domain from publishable key
      const keyParts = publishableKey.split('_');
      if (keyParts.length < 3) return false;
      
      const domain = atob(keyParts[2]).replace('$', '');
      const healthUrl = `https://${domain}/v1/health`;
      
      const response = await fetch(healthUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
        signal: AbortSignal.timeout(5000) // 5 second timeout
      });
      
      return response.ok;
    } catch (error) {
      console.warn('Clerk health check failed:', error);
      return false;
    }
  };

  // Retry logic with exponential backoff
  const retryClerkConnection = async () => {
    const maxRetries = 3;
    const baseDelay = 1000; // 1 second
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      const isHealthy = await checkClerkHealth();
      
      if (isHealthy) {
        setClerkStatus({
          isHealthy: true,
          retryCount: attempt,
          lastRetry: new Date()
        });
        setShowFallback(false);
        return true;
      }
      
      if (attempt < maxRetries) {
        const delay = baseDelay * Math.pow(2, attempt - 1);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    setClerkStatus({
      isHealthy: false,
      retryCount: maxRetries,
      lastError: 'Clerk API unavailable after retries',
      lastRetry: new Date()
    });
    setShowFallback(true);
    return false;
  };

  // Monitor for Clerk errors
  useEffect(() => {
    const originalConsoleError = console.error;
    
    console.error = (...args) => {
      const errorMessage = args.join(' ');
      
      // Detect Clerk-specific errors
      if (errorMessage.includes('clerk.accounts.dev') || 
          errorMessage.includes('dev_browser') ||
          errorMessage.includes('SyntaxError: Unexpected token')) {
        
        setClerkStatus(prev => ({
          ...prev,
          isHealthy: false,
          lastError: errorMessage,
          retryCount: prev.retryCount + 1
        }));
        
        // Trigger retry after a delay
        setTimeout(() => {
          retryClerkConnection();
        }, 2000);
      }
      
      originalConsoleError(...args);
    };
    
    return () => {
      console.error = originalConsoleError;
    };
  }, []);

  // Initial health check and validation
  useEffect(() => {
    const initialCheck = async () => {
      // Run diagnostics in development
      if (import.meta.env.DEV) {
        await logClerkDiagnostics(publishableKey);
      }

      // Validate configuration
      const validation = validateClerkConfiguration(publishableKey);
      if (!validation.isValid) {
        console.error('Clerk configuration issues:', validation.issues);
        setClerkStatus({
          isHealthy: false,
          retryCount: 0,
          lastError: validation.issues.join(', ')
        });
        setShowFallback(true);
        return;
      }

      // Test connectivity
      const connectivity = await testClerkConnectivity(publishableKey);
      if (!connectivity.isReachable) {
        console.warn('Clerk connectivity issues:', connectivity.error);
        await retryClerkConnection();
      }
    };

    initialCheck();
  }, [publishableKey]);

  // Fallback UI when Clerk is unavailable
  if (showFallback) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100">
                <svg className="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h3 className="mt-4 text-lg font-medium text-gray-900">
                Autentiseringstjeneste midlertidig utilgjengelig
              </h3>
              <p className="mt-2 text-sm text-gray-500">
                Vi opplever tekniske problemer med påloggingssystemet. Dette er vanligvis midlertidig.
              </p>
              <div className="mt-4 text-xs text-gray-400">
                <p>Forsøk: {clerkStatus.retryCount}</p>
                {clerkStatus.lastRetry && (
                  <p>Siste forsøk: {clerkStatus.lastRetry.toLocaleTimeString('nb-NO')}</p>
                )}
              </div>
              <div className="mt-6 space-y-3">
                <button
                  onClick={() => retryClerkConnection()}
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Prøv igjen
                </button>
                <button
                  onClick={() => window.location.reload()}
                  className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Last siden på nytt
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Normal Clerk provider with enhanced error handling
  return (
    <ClerkProvider 
      publishableKey={publishableKey}
      afterSignOutUrl={afterSignOutUrl}
      navigate={(to) => window.location.href = to}
      appearance={{
        baseTheme: undefined,
        variables: {
          colorPrimary: '#3b82f6'
        }
      }}
      localization={{
        signIn: {
          start: {
            title: 'Logg inn på JobbLogg.no',
            subtitle: 'Velkommen tilbake!'
          }
        },
        signUp: {
          start: {
            title: 'Opprett konto på JobbLogg.no',
            subtitle: 'Kom i gang med profesjonell jobblogging'
          }
        }
      }}
    >
      {children}
    </ClerkProvider>
  );
};

export default RobustClerkProvider;

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface FallbackUser {
  id: string;
  email: string;
  name: string;
  createdAt: Date;
}

interface FallbackAuthContextType {
  user: FallbackUser | null;
  isLoaded: boolean;
  isSignedIn: boolean;
  signIn: (email: string) => Promise<void>;
  signOut: () => void;
}

const FallbackAuthContext = createContext<FallbackAuthContextType | null>(null);

interface FallbackAuthProviderProps {
  children: ReactNode;
}

export const FallbackAuthProvider: React.FC<FallbackAuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<FallbackUser | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    // Check for existing session in localStorage
    const savedUser = localStorage.getItem('fallback_auth_user');
    if (savedUser) {
      try {
        const userData = JSON.parse(savedUser);
        setUser({
          ...userData,
          createdAt: new Date(userData.createdAt)
        });
      } catch (error) {
        console.warn('Failed to parse saved user data:', error);
        localStorage.removeItem('fallback_auth_user');
      }
    }
    setIsLoaded(true);
  }, []);

  const signIn = async (email: string) => {
    // Simple fallback authentication - in production, this would connect to your backend
    const fallbackUser: FallbackUser = {
      id: `fallback_${Date.now()}`,
      email,
      name: email.split('@')[0],
      createdAt: new Date()
    };
    
    setUser(fallbackUser);
    localStorage.setItem('fallback_auth_user', JSON.stringify(fallbackUser));
  };

  const signOut = () => {
    setUser(null);
    localStorage.removeItem('fallback_auth_user');
  };

  const value: FallbackAuthContextType = {
    user,
    isLoaded,
    isSignedIn: !!user,
    signIn,
    signOut
  };

  return (
    <FallbackAuthContext.Provider value={value}>
      {children}
    </FallbackAuthContext.Provider>
  );
};

export const useFallbackAuth = () => {
  const context = useContext(FallbackAuthContext);
  if (!context) {
    throw new Error('useFallbackAuth must be used within FallbackAuthProvider');
  }
  return context;
};

// Fallback sign-in component
export const FallbackSignIn: React.FC = () => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { signIn } = useFallbackAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;

    setIsLoading(true);
    try {
      await signIn(email);
    } catch (error) {
      console.error('Fallback sign-in failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            JobbLogg.no
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Midlertidig pålogging (Clerk utilgjengelig)
          </p>
        </div>
        
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Midlertidig pålogging
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>
                  Vårt hovedsystem for pålogging er midlertidig utilgjengelig. 
                  Du kan fortsatt bruke applikasjonen med denne midlertidige påloggingen.
                </p>
              </div>
            </div>
          </div>
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div>
            <label htmlFor="email" className="sr-only">
              E-postadresse
            </label>
            <input
              id="email"
              name="email"
              type="email"
              autoComplete="email"
              required
              className="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
              placeholder="E-postadresse"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
          </div>

          <div>
            <button
              type="submit"
              disabled={isLoading || !email}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Logger inn...' : 'Logg inn midlertidig'}
            </button>
          </div>

          <div className="text-center">
            <p className="text-xs text-gray-500">
              Denne påloggingen lagres kun lokalt på din enhet og er midlertidig.
            </p>
          </div>
        </form>
      </div>
    </div>
  );
};

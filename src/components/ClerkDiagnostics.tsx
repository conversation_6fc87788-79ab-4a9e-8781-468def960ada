import React, { useEffect, useState } from 'react';
import { useAuth } from '@clerk/clerk-react';

const ClerkDiagnostics: React.FC = () => {
  const { isLoaded, isSignedIn, userId } = useAuth();
  const [showDiagnostics, setShowDiagnostics] = useState(false);
  const [clerkStatus, setClerkStatus] = useState<'loading' | 'ready' | 'error'>('loading');

  useEffect(() => {
    // Show diagnostics in development mode if there are issues
    if (import.meta.env.DEV) {
      const timer = setTimeout(() => {
        if (!isLoaded) {
          setClerkStatus('error');
          setShowDiagnostics(true);
        } else {
          setClerkStatus('ready');
        }
      }, 10000); // Show error after 10 seconds if not loaded

      return () => clearTimeout(timer);
    }
  }, [isLoaded]);

  useEffect(() => {
    if (isLoaded) {
      setClerkStatus('ready');
      setShowDiagnostics(false);
    }
  }, [isLoaded]);

  // Only show in development mode
  if (!import.meta.env.DEV || !showDiagnostics) return null;

  return (
    <div className="fixed bottom-4 left-4 z-50 bg-yellow-100 border border-yellow-400 text-yellow-800 px-4 py-3 rounded-lg shadow-lg max-w-sm">
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium">Clerk Diagnostikk</h3>
          <div className="mt-2 text-sm">
            <ul className="list-disc list-inside space-y-1">
              <li>Status: {clerkStatus}</li>
              <li>Lastet: {isLoaded ? 'Ja' : 'Nei'}</li>
              <li>Innlogget: {isSignedIn ? 'Ja' : 'Nei'}</li>
              {userId && <li>Bruker ID: {userId.substring(0, 8)}...</li>}
            </ul>
          </div>
          <div className="mt-3">
            <button
              onClick={() => setShowDiagnostics(false)}
              className="text-xs bg-yellow-200 hover:bg-yellow-300 px-2 py-1 rounded"
            >
              Skjul
            </button>
            <button
              onClick={() => window.location.reload()}
              className="ml-2 text-xs bg-yellow-200 hover:bg-yellow-300 px-2 py-1 rounded"
            >
              Last på nytt
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClerkDiagnostics;

import React, { useState, useEffect } from 'react';
import { useAuth, useClerk } from '@clerk/clerk-react';

interface HealthStatus {
  status: 'healthy' | 'degraded' | 'down';
  lastCheck: Date;
  responseTime?: number;
  errors: string[];
}

const ClerkHealthMonitor: React.FC = () => {
  const { isLoaded, isSignedIn } = useAuth();
  const clerk = useClerk();
  const [healthStatus, setHealthStatus] = useState<HealthStatus>({
    status: 'healthy',
    lastCheck: new Date(),
    errors: []
  });
  const [showDetails, setShowDetails] = useState(false);

  // Monitor Clerk API health
  const checkHealth = async () => {
    const startTime = Date.now();
    const errors: string[] = [];
    
    try {
      // Test basic Clerk functionality
      if (!isLoaded) {
        errors.push('Clerk not loaded after timeout');
      }
      
      // Check if we can access Clerk client
      if (!clerk) {
        errors.push('Clerk client not available');
      }
      
      const responseTime = Date.now() - startTime;
      
      setHealthStatus({
        status: errors.length === 0 ? 'healthy' : errors.length < 2 ? 'degraded' : 'down',
        lastCheck: new Date(),
        responseTime,
        errors
      });
      
    } catch (error) {
      errors.push(`Health check failed: ${error}`);
      setHealthStatus({
        status: 'down',
        lastCheck: new Date(),
        responseTime: Date.now() - startTime,
        errors
      });
    }
  };

  // Periodic health checks
  useEffect(() => {
    const interval = setInterval(checkHealth, 30000); // Check every 30 seconds
    checkHealth(); // Initial check
    
    return () => clearInterval(interval);
  }, [isLoaded, clerk]);

  // Monitor console errors for Clerk-specific issues
  useEffect(() => {
    const originalError = console.error;
    
    console.error = (...args) => {
      const message = args.join(' ');
      
      if (message.includes('clerk') || 
          message.includes('dev_browser') || 
          message.includes('loved-dory-86')) {
        
        setHealthStatus(prev => ({
          ...prev,
          status: 'degraded',
          errors: [...prev.errors.slice(-4), message] // Keep last 5 errors
        }));
      }
      
      originalError(...args);
    };
    
    return () => {
      console.error = originalError;
    };
  }, []);

  // Only show in development mode
  if (!import.meta.env.DEV) return null;

  const getStatusColor = () => {
    switch (healthStatus.status) {
      case 'healthy': return 'bg-green-500';
      case 'degraded': return 'bg-yellow-500';
      case 'down': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusText = () => {
    switch (healthStatus.status) {
      case 'healthy': return 'Sunn';
      case 'degraded': return 'Redusert';
      case 'down': return 'Nede';
      default: return 'Ukjent';
    }
  };

  return (
    <div className="fixed top-4 left-4 z-50">
      <div 
        className={`px-3 py-2 rounded-lg shadow-lg text-white cursor-pointer transition-all duration-200 ${getStatusColor()}`}
        onClick={() => setShowDetails(!showDetails)}
      >
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full bg-white opacity-75`} />
          <span className="text-sm font-medium">
            Clerk: {getStatusText()}
          </span>
          {healthStatus.responseTime && (
            <span className="text-xs opacity-75">
              ({healthStatus.responseTime}ms)
            </span>
          )}
        </div>
      </div>
      
      {showDetails && (
        <div className="mt-2 bg-white rounded-lg shadow-lg border p-4 max-w-sm">
          <h4 className="font-medium text-gray-900 mb-2">Clerk Status Detaljer</h4>
          
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Status:</span>
              <span className={`font-medium ${
                healthStatus.status === 'healthy' ? 'text-green-600' :
                healthStatus.status === 'degraded' ? 'text-yellow-600' : 'text-red-600'
              }`}>
                {getStatusText()}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">Lastet:</span>
              <span className={isLoaded ? 'text-green-600' : 'text-red-600'}>
                {isLoaded ? 'Ja' : 'Nei'}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">Innlogget:</span>
              <span className={isSignedIn ? 'text-green-600' : 'text-gray-600'}>
                {isSignedIn ? 'Ja' : 'Nei'}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">Siste sjekk:</span>
              <span className="text-gray-900">
                {healthStatus.lastCheck.toLocaleTimeString('nb-NO')}
              </span>
            </div>
            
            {healthStatus.responseTime && (
              <div className="flex justify-between">
                <span className="text-gray-600">Responstid:</span>
                <span className="text-gray-900">
                  {healthStatus.responseTime}ms
                </span>
              </div>
            )}
          </div>
          
          {healthStatus.errors.length > 0 && (
            <div className="mt-3 pt-3 border-t">
              <h5 className="text-xs font-medium text-gray-700 mb-1">Nylige feil:</h5>
              <div className="space-y-1 max-h-32 overflow-y-auto">
                {healthStatus.errors.slice(-3).map((error, index) => (
                  <div key={index} className="text-xs text-red-600 bg-red-50 p-1 rounded">
                    {error.length > 60 ? `${error.substring(0, 60)}...` : error}
                  </div>
                ))}
              </div>
            </div>
          )}
          
          <div className="mt-3 pt-3 border-t flex space-x-2">
            <button
              onClick={checkHealth}
              className="text-xs bg-blue-100 hover:bg-blue-200 text-blue-800 px-2 py-1 rounded"
            >
              Sjekk nå
            </button>
            <button
              onClick={() => window.location.reload()}
              className="text-xs bg-gray-100 hover:bg-gray-200 text-gray-800 px-2 py-1 rounded"
            >
              Last på nytt
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ClerkHealthMonitor;

import { useAuth, User<PERSON><PERSON><PERSON> } from "@clerk/clerk-react";
import { ReactNode } from "react";
import { <PERSON> } from "react-router-dom";
import { useLocale } from "../contexts/LocaleContext";

interface LayoutProps {
  children: ReactNode;
}

export function Layout({ children }: LayoutProps) {
  const { isSignedIn } = useAuth();
  const { texts } = useLocale();

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link to="/" className="text-xl font-bold text-gray-900">
                {texts.appTitle}
              </Link>
            </div>
            
            <div className="flex items-center space-x-4">
              {isSignedIn ? (
                <>
                  <Link
                    to="/create-project"
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                  >
                    {texts.createProject}
                  </Link>
                  <UserButton afterSignOutUrl="/" />
                </>
              ) : (
                <Link
                  to="/sign-in"
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  {texts.signIn}
                </Link>
              )}
            </div>
          </div>
        </div>
      </header>
      
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {children}
        </div>
      </main>
    </div>
  );
}

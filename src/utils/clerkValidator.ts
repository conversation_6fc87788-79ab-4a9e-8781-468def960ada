// Clerk Configuration Validator and Diagnostic Tool

export interface ClerkValidationResult {
  isValid: boolean;
  issues: string[];
  suggestions: string[];
  publishableKeyInfo?: {
    environment: 'development' | 'production';
    domain: string;
    instanceId: string;
  };
}

export const validateClerkConfiguration = (publishableKey: string): ClerkValidationResult => {
  const result: ClerkValidationResult = {
    isValid: true,
    issues: [],
    suggestions: []
  };

  // Check if publishable key exists
  if (!publishableKey) {
    result.isValid = false;
    result.issues.push('Clerk publishable key is missing');
    result.suggestions.push('Add VITE_CLERK_PUBLISHABLE_KEY to your .env.local file');
    return result;
  }

  // Validate publishable key format
  const keyPattern = /^pk_(test|live)_([A-Za-z0-9+/=]+)$/;
  if (!keyPattern.test(publishableKey)) {
    result.isValid = false;
    result.issues.push('Clerk publishable key format is invalid');
    result.suggestions.push('Ensure the key starts with pk_test_ or pk_live_ and contains valid base64 characters');
    return result;
  }

  // Extract key information
  try {
    const keyParts = publishableKey.split('_');
    const environment = keyParts[1] as 'test' | 'live';
    const encodedData = keyParts[2];
    
    // Decode the domain information
    let domain = '';
    try {
      domain = atob(encodedData).replace('$', '');
    } catch (error) {
      result.issues.push('Failed to decode publishable key data');
      result.suggestions.push('The publishable key may be corrupted. Try regenerating it from Clerk dashboard');
    }

    result.publishableKeyInfo = {
      environment: environment === 'test' ? 'development' : 'production',
      domain,
      instanceId: domain.split('.')[0] || 'unknown'
    };

    // Validate environment
    if (import.meta.env.DEV && environment === 'live') {
      result.issues.push('Using production Clerk key in development environment');
      result.suggestions.push('Use a development key (pk_test_) for local development');
    }

    if (!import.meta.env.DEV && environment === 'test') {
      result.issues.push('Using development Clerk key in production environment');
      result.suggestions.push('Use a production key (pk_live_) for production deployment');
    }

    // Check domain accessibility
    if (domain && domain.includes('clerk.accounts.dev')) {
      result.suggestions.push('Using Clerk development instance - ensure it\'s accessible and not rate-limited');
    }

  } catch (error) {
    result.isValid = false;
    result.issues.push(`Failed to parse publishable key: ${error}`);
    result.suggestions.push('Verify the publishable key is correctly copied from Clerk dashboard');
  }

  return result;
};

export const testClerkConnectivity = async (publishableKey: string): Promise<{
  isReachable: boolean;
  responseTime?: number;
  error?: string;
}> => {
  try {
    const keyParts = publishableKey.split('_');
    if (keyParts.length < 3) {
      return { isReachable: false, error: 'Invalid key format' };
    }

    const domain = atob(keyParts[2]).replace('$', '');
    const testUrl = `https://${domain}/v1/health`;
    
    const startTime = Date.now();
    const response = await fetch(testUrl, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
      signal: AbortSignal.timeout(10000) // 10 second timeout
    });
    
    const responseTime = Date.now() - startTime;
    
    if (response.ok) {
      return { isReachable: true, responseTime };
    } else {
      return { 
        isReachable: false, 
        responseTime, 
        error: `HTTP ${response.status}: ${response.statusText}` 
      };
    }
  } catch (error) {
    return { 
      isReachable: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
};

export const generateClerkDiagnosticReport = async (publishableKey: string) => {
  const validation = validateClerkConfiguration(publishableKey);
  const connectivity = await testClerkConnectivity(publishableKey);
  
  const report = {
    timestamp: new Date().toISOString(),
    validation,
    connectivity,
    environment: {
      isDevelopment: import.meta.env.DEV,
      userAgent: navigator.userAgent,
      url: window.location.href
    },
    recommendations: [] as string[]
  };

  // Generate recommendations
  if (!validation.isValid) {
    report.recommendations.push('Fix Clerk configuration issues before proceeding');
  }
  
  if (!connectivity.isReachable) {
    report.recommendations.push('Check network connectivity to Clerk servers');
    report.recommendations.push('Consider implementing fallback authentication');
  }
  
  if (connectivity.responseTime && connectivity.responseTime > 5000) {
    report.recommendations.push('Clerk API response time is slow - consider retry mechanisms');
  }

  return report;
};

// Console logging helper for diagnostics
export const logClerkDiagnostics = async (publishableKey: string) => {
  const report = await generateClerkDiagnosticReport(publishableKey);
  
  console.group('🔍 Clerk Diagnostics Report');
  console.log('Timestamp:', report.timestamp);
  
  console.group('📋 Validation');
  console.log('Valid:', report.validation.isValid);
  if (report.validation.issues.length > 0) {
    console.warn('Issues:', report.validation.issues);
  }
  if (report.validation.suggestions.length > 0) {
    console.info('Suggestions:', report.validation.suggestions);
  }
  if (report.validation.publishableKeyInfo) {
    console.log('Key Info:', report.validation.publishableKeyInfo);
  }
  console.groupEnd();
  
  console.group('🌐 Connectivity');
  console.log('Reachable:', report.connectivity.isReachable);
  if (report.connectivity.responseTime) {
    console.log('Response Time:', `${report.connectivity.responseTime}ms`);
  }
  if (report.connectivity.error) {
    console.error('Error:', report.connectivity.error);
  }
  console.groupEnd();
  
  if (report.recommendations.length > 0) {
    console.group('💡 Recommendations');
    report.recommendations.forEach(rec => console.info('•', rec));
    console.groupEnd();
  }
  
  console.groupEnd();
  
  return report;
};

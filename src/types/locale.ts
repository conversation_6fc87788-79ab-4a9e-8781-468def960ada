// TypeScript interface for translation keys based on nb.json structure
export interface LocaleKeys {
  appTitle: string;
  dashboard: string;
  createProject: string;
  projectName: string;
  description: string;
  save: string;
  cancel: string;
  addLog: string;
  logText: string;
  uploadImage: string;
  generateCaption: string;
  viewProject: string;
  shareProject: string;
  scanQRCode: string;
  publicView: string;
  loading: string;
  error: string;
  signIn: string;
  signUp: string;
  signOut: string;
  myProjects: string;
  noProjects: string;
  createFirstProject: string;
  projectCreated: string;
  logAdded: string;
  imageUploaded: string;
  generatingCaption: string;
  enterLogText: string;
  enterProjectName: string;
  enterDescription: string;
  manageProjects: string;
  getStartedMessage: string;
  startNewProject: string;
  projectDetails: string;
  projectNameRequired: string;
  projectNameMinLength: string;
  projectDescriptionRequired: string;
  projectDescriptionMinLength: string;
  failedToCreateProject: string;
  describeProject: string;
}

export type SupportedLocale = 'nb' | 'sv' | 'da';

export interface LocaleContextType {
  locale: SupportedLocale;
  texts: LocaleKeys;
  setLocale: (locale: SupportedLocale) => void;
}

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { LocaleContextType, LocaleKeys, SupportedLocale } from '../types/locale';

const LocaleContext = createContext<LocaleContextType | undefined>(undefined);

interface LocaleProviderProps {
  children: ReactNode;
  defaultLocale?: SupportedLocale;
}

export const LocaleProvider: React.FC<LocaleProviderProps> = ({ 
  children, 
  defaultLocale = 'nb' 
}) => {
  const [locale, setLocale] = useState<SupportedLocale>(defaultLocale);
  const [texts, setTexts] = useState<LocaleKeys>({} as LocaleKeys);
  const [nbTexts, setNbTexts] = useState<LocaleKeys>({} as LocaleKeys);

  // Load Norwegian texts as fallback
  useEffect(() => {
    const loadNorwegianTexts = async () => {
      try {
        const nbModule = await import('../locales/nb.json');
        setNbTexts(nbModule.default);
      } catch (error) {
        console.error('Failed to load Norwegian texts:', error);
      }
    };
    loadNorwegianTexts();
  }, []);

  // Load current locale texts
  useEffect(() => {
    const loadTexts = async () => {
      try {
        const localeModule = await import(`../locales/${locale}.json`);
        const localeTexts = localeModule.default;
        
        // Create merged texts with fallback to Norwegian
        const mergedTexts: LocaleKeys = {} as LocaleKeys;
        
        // Use Norwegian as base and override with locale-specific texts
        Object.keys(nbTexts).forEach((key) => {
          const typedKey = key as keyof LocaleKeys;
          mergedTexts[typedKey] = localeTexts[typedKey] || nbTexts[typedKey] || '';
        });
        
        setTexts(mergedTexts);
      } catch (error) {
        console.error(`Failed to load locale ${locale}:`, error);
        // Fallback to Norwegian if locale loading fails
        setTexts(nbTexts);
      }
    };

    if (Object.keys(nbTexts).length > 0) {
      loadTexts();
    }
  }, [locale, nbTexts]);

  const contextValue: LocaleContextType = {
    locale,
    texts,
    setLocale,
  };

  return (
    <LocaleContext.Provider value={contextValue}>
      {children}
    </LocaleContext.Provider>
  );
};

export const useLocale = (): LocaleContextType => {
  const context = useContext(LocaleContext);
  if (context === undefined) {
    throw new Error('useLocale must be used within a LocaleProvider');
  }
  return context;
};

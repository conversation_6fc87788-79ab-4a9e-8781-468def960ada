import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ClerkProvider, ConvexProvider, Layout, ProtectedRoute } from './components';
import {
  Dashboard,
  CreateProject,
  ProjectLog,
  PublicViewer,
  SignIn,
  SignUp
} from './pages';

function App() {
  return (
    <ClerkProvider>
      <ConvexProvider>
        <Router>
          <Routes>
            {/* Public routes */}
            <Route path="/sign-in/*" element={<SignIn />} />
            <Route path="/sign-up/*" element={<SignUp />} />
            <Route path="/v/:sharedId" element={<PublicViewer />} />

            {/* Protected routes */}
            <Route path="/" element={
              <ProtectedRoute>
                <Layout>
                  <Dashboard />
                </Layout>
              </ProtectedRoute>
            } />

            <Route path="/create-project" element={
              <ProtectedRoute>
                <Layout>
                  <CreateProject />
                </Layout>
              </ProtectedRoute>
            } />

            <Route path="/project/:projectId" element={
              <ProtectedRoute>
                <Layout>
                  <ProjectLog />
                </Layout>
              </ProtectedRoute>
            } />
          </Routes>
        </Router>
      </ConvexProvider>
    </ClerkProvider>
  );
}

export default App;

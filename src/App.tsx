import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConvexProvider, Layout, ProtectedRoute } from './components';
import { LocaleProvider } from './contexts/LocaleContext';
import ConnectionStatus from './components/ConnectionStatus';
import ClerkDiagnostics from './components/ClerkDiagnostics';
import {
  Dashboard,
  CreateProject,
  ProjectLog,
  PublicViewer,
  SignIn,
  SignUp
} from './pages';

function App() {
  return (
    <ConvexProvider>
      <LocaleProvider defaultLocale="nb">
        <ConnectionStatus />
        <ClerkDiagnostics />
        <Router>
          <Routes>
            {/* Public routes */}
            <Route path="/sign-in/*" element={<SignIn />} />
            <Route path="/sign-up/*" element={<SignUp />} />
            <Route path="/v/:sharedId" element={<PublicViewer />} />

            {/* Protected routes */}
            <Route path="/" element={
              <ProtectedRoute>
                <Layout>
                  <Dashboard />
                </Layout>
              </ProtectedRoute>
            } />

            <Route path="/create-project" element={
              <ProtectedRoute>
                <Layout>
                  <CreateProject />
                </Layout>
              </ProtectedRoute>
            } />

            <Route path="/project/:projectId" element={
              <ProtectedRoute>
                <Layout>
                  <ProjectLog />
                </Layout>
              </ProtectedRoute>
            } />
          </Routes>
        </Router>
      </LocaleProvider>
    </ConvexProvider>
  );
}

export default App;

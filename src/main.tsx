import React from 'react'
import { createRoot } from 'react-dom/client'
import { Clerk<PERSON>rovider } from '@clerk/clerk-react'
import './index.css'
import App from './App.tsx'
import ErrorBoundary from './components/ErrorBoundary.tsx'

const PUBLISHABLE_KEY = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY

if (!PUBLISHABLE_KEY) {
  throw new Error("Missing Clerk Publishable Key")
}

createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <ErrorBoundary>
      <ClerkProvider
        publishableKey={PUBLISHABLE_KEY}
        afterSignOutUrl="/"
        navigate={(to) => window.location.href = to}
        appearance={{
          baseTheme: undefined,
          variables: {
            colorPrimary: '#3b82f6'
          }
        }}
        localization={{
          signIn: {
            start: {
              title: 'Logg inn på JobbLogg.no',
              subtitle: '<PERSON><PERSON><PERSON><PERSON><PERSON> tilbake!'
            }
          },
          signUp: {
            start: {
              title: '<PERSON>p<PERSON>t konto på JobbLogg.no',
              subtitle: 'Kom i gang med profesjonell jobblogging'
            }
          }
        }}
      >
        <App />
      </ClerkProvider>
    </ErrorBoundary>
  </React.StrictMode>
)

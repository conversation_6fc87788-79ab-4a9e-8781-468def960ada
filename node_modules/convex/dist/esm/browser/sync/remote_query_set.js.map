{"version": 3, "sources": ["../../../../src/browser/sync/remote_query_set.ts"], "sourcesContent": ["import { jsonToConvex } from \"../../values/index.js\";\nimport { Long } from \"../long.js\";\nimport { logForFunction, Logger } from \"../logging.js\";\nimport { QueryId, StateVersion, Transition } from \"./protocol.js\";\nimport { FunctionResult } from \"./function_result.js\";\n\n/**\n * A represention of the query results we've received on the current WebSocket\n * connection.\n *\n * Queries you won't find here include:\n * - queries which have been requested, but no query transition has been received yet for\n * - queries which are populated only though active optimistic updates, but are not subscribed to\n */\nexport class RemoteQuerySet {\n  private version: StateVersion;\n  private readonly remoteQuerySet: Map<QueryId, FunctionResult>;\n  private readonly queryPath: (queryId: QueryId) => string | null;\n  private readonly logger: Logger;\n\n  constructor(queryPath: (queryId: QueryId) => string | null, logger: Logger) {\n    this.version = { querySet: 0, ts: Long.fromNumber(0), identity: 0 };\n    this.remoteQuerySet = new Map();\n    this.queryPath = queryPath;\n    this.logger = logger;\n  }\n\n  transition(transition: Transition): void {\n    const start = transition.startVersion;\n    if (\n      this.version.querySet !== start.querySet ||\n      this.version.ts.notEquals(start.ts) ||\n      this.version.identity !== start.identity\n    ) {\n      throw new Error(\n        `Invalid start version: ${start.ts.toString()}:${start.querySet}`,\n      );\n    }\n    for (const modification of transition.modifications) {\n      switch (modification.type) {\n        case \"QueryUpdated\": {\n          const queryPath = this.queryPath(modification.queryId);\n          if (queryPath) {\n            for (const line of modification.logLines) {\n              logForFunction(this.logger, \"info\", \"query\", queryPath, line);\n            }\n          }\n          const value = jsonToConvex(modification.value ?? null);\n          this.remoteQuerySet.set(modification.queryId, {\n            success: true,\n            value,\n            logLines: modification.logLines,\n          });\n          break;\n        }\n        case \"QueryFailed\": {\n          const queryPath = this.queryPath(modification.queryId);\n          if (queryPath) {\n            for (const line of modification.logLines) {\n              logForFunction(this.logger, \"info\", \"query\", queryPath, line);\n            }\n          }\n          const { errorData } = modification;\n          this.remoteQuerySet.set(modification.queryId, {\n            success: false,\n            errorMessage: modification.errorMessage,\n            errorData:\n              errorData !== undefined ? jsonToConvex(errorData) : undefined,\n            logLines: modification.logLines,\n          });\n          break;\n        }\n        case \"QueryRemoved\": {\n          this.remoteQuerySet.delete(modification.queryId);\n          break;\n        }\n        default: {\n          // Enforce that the switch-case is exhaustive.\n          const _: never = modification;\n          throw new Error(`Invalid modification ${(modification as any).type}`);\n        }\n      }\n    }\n    this.version = transition.endVersion;\n  }\n\n  remoteQueryResults(): Map<QueryId, FunctionResult> {\n    return this.remoteQuerySet;\n  }\n\n  timestamp(): Long {\n    return this.version.ts;\n  }\n}\n"], "mappings": ";;;;AAAA,SAAS,oBAAoB;AAC7B,SAAS,YAAY;AACrB,SAAS,sBAA8B;AAYhC,aAAM,eAAe;AAAA,EAM1B,YAAY,WAAgD,QAAgB;AAL5E,wBAAQ;AACR,wBAAiB;AACjB,wBAAiB;AACjB,wBAAiB;AAGf,SAAK,UAAU,EAAE,UAAU,GAAG,IAAI,KAAK,WAAW,CAAC,GAAG,UAAU,EAAE;AAClE,SAAK,iBAAiB,oBAAI,IAAI;AAC9B,SAAK,YAAY;AACjB,SAAK,SAAS;AAAA,EAChB;AAAA,EAEA,WAAW,YAA8B;AACvC,UAAM,QAAQ,WAAW;AACzB,QACE,KAAK,QAAQ,aAAa,MAAM,YAChC,KAAK,QAAQ,GAAG,UAAU,MAAM,EAAE,KAClC,KAAK,QAAQ,aAAa,MAAM,UAChC;AACA,YAAM,IAAI;AAAA,QACR,0BAA0B,MAAM,GAAG,SAAS,CAAC,IAAI,MAAM,QAAQ;AAAA,MACjE;AAAA,IACF;AACA,eAAW,gBAAgB,WAAW,eAAe;AACnD,cAAQ,aAAa,MAAM;AAAA,QACzB,KAAK,gBAAgB;AACnB,gBAAM,YAAY,KAAK,UAAU,aAAa,OAAO;AACrD,cAAI,WAAW;AACb,uBAAW,QAAQ,aAAa,UAAU;AACxC,6BAAe,KAAK,QAAQ,QAAQ,SAAS,WAAW,IAAI;AAAA,YAC9D;AAAA,UACF;AACA,gBAAM,QAAQ,aAAa,aAAa,SAAS,IAAI;AACrD,eAAK,eAAe,IAAI,aAAa,SAAS;AAAA,YAC5C,SAAS;AAAA,YACT;AAAA,YACA,UAAU,aAAa;AAAA,UACzB,CAAC;AACD;AAAA,QACF;AAAA,QACA,KAAK,eAAe;AAClB,gBAAM,YAAY,KAAK,UAAU,aAAa,OAAO;AACrD,cAAI,WAAW;AACb,uBAAW,QAAQ,aAAa,UAAU;AACxC,6BAAe,KAAK,QAAQ,QAAQ,SAAS,WAAW,IAAI;AAAA,YAC9D;AAAA,UACF;AACA,gBAAM,EAAE,UAAU,IAAI;AACtB,eAAK,eAAe,IAAI,aAAa,SAAS;AAAA,YAC5C,SAAS;AAAA,YACT,cAAc,aAAa;AAAA,YAC3B,WACE,cAAc,SAAY,aAAa,SAAS,IAAI;AAAA,YACtD,UAAU,aAAa;AAAA,UACzB,CAAC;AACD;AAAA,QACF;AAAA,QACA,KAAK,gBAAgB;AACnB,eAAK,eAAe,OAAO,aAAa,OAAO;AAC/C;AAAA,QACF;AAAA,QACA,SAAS;AAEP,gBAAM,IAAW;AACjB,gBAAM,IAAI,MAAM,wBAAyB,aAAqB,IAAI,EAAE;AAAA,QACtE;AAAA,MACF;AAAA,IACF;AACA,SAAK,UAAU,WAAW;AAAA,EAC5B;AAAA,EAEA,qBAAmD;AACjD,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,YAAkB;AAChB,WAAO,KAAK,QAAQ;AAAA,EACtB;AACF;", "names": []}
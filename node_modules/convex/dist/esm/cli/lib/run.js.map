{"version": 3, "sources": ["../../../../src/cli/lib/run.ts"], "sourcesContent": ["import chalk from \"chalk\";\nimport util from \"util\";\nimport ws from \"ws\";\nimport { ConvexHttpClient } from \"../../browser/http_client.js\";\nimport { BaseConvexClient } from \"../../browser/index.js\";\nimport {\n  PaginationResult,\n  UserIdentityAttributes,\n  makeFunctionReference,\n} from \"../../server/index.js\";\nimport { Value, convexToJson, jsonToConvex } from \"../../values/value.js\";\nimport {\n  Context,\n  logFinishedStep,\n  logMessage,\n  logOutput,\n  OneoffCtx,\n} from \"../../bundler/context.js\";\nimport { waitForever, waitUntilCalled } from \"./utils/utils.js\";\nimport JSON5 from \"json5\";\nimport path from \"path\";\nimport { readProjectConfig } from \"./config.js\";\nimport { watchAndPush } from \"./dev.js\";\n\nexport async function runFunctionAndLog(\n  ctx: Context,\n  args: {\n    deploymentUrl: string;\n    adminKey: string;\n    functionName: string;\n    argsString: string;\n    identityString?: string;\n    componentPath?: string;\n    callbacks?: {\n      onSuccess?: () => void;\n    };\n  },\n) {\n  const client = new ConvexHttpClient(args.deploymentUrl);\n  const identity = args.identityString\n    ? await getFakeIdentity(ctx, args.identityString)\n    : undefined;\n  client.setAdminAuth(args.adminKey, identity);\n\n  const functionArgs = await parseArgs(ctx, args.argsString);\n  const { projectConfig } = await readProjectConfig(ctx);\n  const parsedFunctionName = await parseFunctionName(\n    ctx,\n    args.functionName,\n    projectConfig.functions,\n  );\n  let result: Value;\n  try {\n    result = await client.function(\n      makeFunctionReference(parsedFunctionName),\n      args.componentPath,\n      functionArgs,\n    );\n  } catch (err) {\n    const errorMessage = (err as Error).toString().trim();\n\n    if (errorMessage.includes(\"Could not find function\")) {\n      const functions = (await runSystemQuery(ctx, {\n        deploymentUrl: args.deploymentUrl,\n        adminKey: args.adminKey,\n        functionName: \"_system/cli/modules:apiSpec\",\n        componentPath: args.componentPath,\n        args: {},\n      })) as (\n        | {\n            functionType: \"Query\" | \"Mutation\" | \"Action\";\n            identifier: string;\n          }\n        | {\n            functionType: \"HttpAction\";\n          }\n      )[];\n\n      const functionNames = functions\n        .filter(\n          (\n            fn,\n          ): fn is {\n            functionType: \"Query\" | \"Mutation\" | \"Action\";\n            identifier: string;\n          } => fn.functionType !== \"HttpAction\",\n        )\n        .map(({ identifier }) => {\n          const separatorPos = identifier.indexOf(\":\");\n\n          const path =\n            separatorPos === -1\n              ? \"\"\n              : identifier.substring(0, separatorPos + 1);\n          const name =\n            separatorPos === -1\n              ? identifier\n              : identifier.substring(separatorPos + 1);\n\n          return `• ${chalk.gray(path)}${name}`;\n        });\n\n      const availableFunctionsMessage =\n        functionNames.length > 0\n          ? `Available functions:\\n${functionNames.join(\"\\n\")}`\n          : \"No functions found.\";\n\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"invalid filesystem data\",\n        printedMessage: `Failed to run function \"${args.functionName}\":\\n${chalk.red(errorMessage)}\\n\\n${availableFunctionsMessage}`,\n      });\n    }\n\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem or env vars\",\n      printedMessage: `Failed to run function \"${args.functionName}\":\\n${chalk.red(errorMessage)}`,\n    });\n  }\n\n  args.callbacks?.onSuccess?.();\n\n  // `null` is the default return type\n  if (result !== null) {\n    logOutput(ctx, formatValue(result));\n  }\n}\n\nasync function getFakeIdentity(ctx: Context, identityString: string) {\n  let identity: UserIdentityAttributes;\n  try {\n    identity = JSON5.parse(identityString);\n  } catch (err) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: `Failed to parse identity as JSON: \"${identityString}\"\\n${chalk.red((err as Error).toString().trim())}`,\n    });\n  }\n  const subject = identity.subject ?? \"\" + simpleHash(JSON.stringify(identity));\n  const issuer = identity.issuer ?? \"https://convex.test\";\n  const tokenIdentifier =\n    identity.tokenIdentifier ?? `${issuer.toString()}|${subject.toString()}`;\n  return {\n    ...identity,\n    subject,\n    issuer,\n    tokenIdentifier,\n  };\n}\n\nexport async function parseArgs(ctx: Context, argsString: string) {\n  try {\n    const argsJson = JSON5.parse(argsString);\n    return jsonToConvex(argsJson) as Record<string, Value>;\n  } catch (err) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem or env vars\",\n      printedMessage: `Failed to parse arguments as JSON: \"${argsString}\"\\n${chalk.red((err as Error).toString().trim())}`,\n    });\n  }\n}\n\nexport async function parseFunctionName(\n  ctx: Context,\n  functionName: string,\n  // Usually `convex/` -- should contain trailing slash\n  functionDirName: string,\n) {\n  // api.foo.bar -> foo:bar\n  // foo/bar -> foo/bar:default\n  // foo/bar:baz -> foo/bar:baz\n  // convex/foo/bar -> foo/bar:default\n\n  // This is the `api.foo.bar` format\n  if (functionName.startsWith(\"api.\") || functionName.startsWith(\"internal.\")) {\n    const parts = functionName.split(\".\");\n    if (parts.length < 3) {\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage: `Function name has too few parts: \"${functionName}\"`,\n      });\n    }\n    const exportName = parts.pop();\n    const parsedName = `${parts.slice(1).join(\"/\")}:${exportName}`;\n    return parsedName;\n  }\n\n  // This is the `foo/bar:baz` format\n\n  // This is something like `convex/foo/bar`, which could either be addressing `foo/bar:default` or `convex/foo/bar:default`\n  // if there's a directory with the same name as the functions directory nested directly underneath.\n  // We'll prefer the `convex/foo/bar:default` version, and check if the file exists, and otherwise treat this as a relative path from the project root.\n  const filePath = functionName.split(\":\")[0];\n  const possibleExtensions = [\n    \".ts\",\n    \".js\",\n    \".tsx\",\n    \".jsx\",\n    \".mts\",\n    \".mjs\",\n    \".cts\",\n    \".cjs\",\n  ];\n  let hasExtension = false;\n  let normalizedFilePath: string = filePath;\n  for (const extension of possibleExtensions) {\n    if (filePath.endsWith(extension)) {\n      normalizedFilePath = filePath.slice(0, -extension.length);\n      hasExtension = true;\n      break;\n    }\n  }\n\n  const exportName = functionName.split(\":\")[1] ?? \"default\";\n  const normalizedName = `${normalizedFilePath}:${exportName}`;\n\n  // This isn't a relative path from the project root\n  if (!filePath.startsWith(functionDirName)) {\n    return normalizedName;\n  }\n\n  const filePathWithoutPrefix = normalizedFilePath.slice(\n    functionDirName.length,\n  );\n  const functionNameWithoutPrefix = `${filePathWithoutPrefix}:${exportName}`;\n\n  if (hasExtension) {\n    if (ctx.fs.exists(path.join(functionDirName, filePath))) {\n      return normalizedName;\n    } else {\n      return functionNameWithoutPrefix;\n    }\n  } else {\n    const exists = possibleExtensions.some((extension) =>\n      ctx.fs.exists(path.join(functionDirName, filePath + extension)),\n    );\n    if (exists) {\n      return normalizedName;\n    } else {\n      return functionNameWithoutPrefix;\n    }\n  }\n}\n\nfunction simpleHash(string: string) {\n  let hash = 0;\n  for (let i = 0; i < string.length; i++) {\n    const char = string.charCodeAt(i);\n    hash = (hash << 5) - hash + char;\n    hash = hash & hash; // Convert to 32bit integer\n  }\n  return hash;\n}\n\nexport async function runSystemPaginatedQuery(\n  ctx: Context,\n  args: {\n    deploymentUrl: string;\n    adminKey: string;\n    functionName: string;\n    componentPath: string | undefined;\n    args: Record<string, Value>;\n    limit?: number;\n  },\n) {\n  const results = [];\n  let cursor = null;\n  let isDone = false;\n  while (!isDone && (args.limit === undefined || results.length < args.limit)) {\n    const paginationResult = (await runSystemQuery(ctx, {\n      ...args,\n      args: {\n        ...args.args,\n        paginationOpts: {\n          cursor,\n          numItems:\n            args.limit === undefined ? 10000 : args.limit - results.length,\n        },\n      },\n    })) as unknown as PaginationResult<Record<string, Value>>;\n    isDone = paginationResult.isDone;\n    cursor = paginationResult.continueCursor;\n    results.push(...paginationResult.page);\n  }\n  return results;\n}\n\nexport async function runSystemQuery(\n  ctx: Context,\n  args: {\n    deploymentUrl: string;\n    adminKey: string;\n    functionName: string;\n    componentPath: string | undefined;\n    args: Record<string, Value>;\n  },\n): Promise<Value> {\n  let onResult: (result: Value) => void;\n  const resultPromise = new Promise<Value>((resolve) => {\n    onResult = resolve;\n  });\n  const [donePromise, onDone] = waitUntilCalled();\n  await subscribe(ctx, {\n    ...args,\n    parsedFunctionName: args.functionName,\n    parsedFunctionArgs: args.args,\n    until: donePromise,\n    callbacks: {\n      onChange: (result) => {\n        onDone();\n        onResult(result);\n      },\n    },\n  });\n  return resultPromise;\n}\n\nexport function formatValue(value: Value) {\n  const json = convexToJson(value);\n  if (process.stdout.isTTY) {\n    // TODO (Tom) add JSON syntax highlighting like https://stackoverflow.com/a/51319962/398212\n    // until then, just spit out something that isn't quite JSON because it's easy\n    return util.inspect(value, { colors: true, depth: null });\n  } else {\n    return JSON.stringify(json, null, 2);\n  }\n}\n\nexport async function subscribeAndLog(\n  ctx: Context,\n  args: {\n    deploymentUrl: string;\n    adminKey: string;\n    functionName: string;\n    argsString: string;\n    identityString?: string;\n    componentPath: string | undefined;\n  },\n) {\n  const { projectConfig } = await readProjectConfig(ctx);\n\n  const parsedFunctionName = await parseFunctionName(\n    ctx,\n    args.functionName,\n    projectConfig.functions,\n  );\n  const identity = args.identityString\n    ? await getFakeIdentity(ctx, args.identityString)\n    : undefined;\n  const functionArgs = await parseArgs(ctx, args.argsString);\n  return subscribe(ctx, {\n    deploymentUrl: args.deploymentUrl,\n    adminKey: args.adminKey,\n    identity,\n    parsedFunctionName,\n    parsedFunctionArgs: functionArgs,\n    componentPath: args.componentPath,\n    until: waitForever(),\n    callbacks: {\n      onStart() {\n        logFinishedStep(\n          ctx,\n          `Watching query ${args.functionName} on ${args.deploymentUrl}...`,\n        );\n      },\n      onChange(result) {\n        logOutput(ctx, formatValue(result));\n      },\n      onStop() {\n        logMessage(ctx, `Closing connection to ${args.deploymentUrl}...`);\n      },\n    },\n  });\n}\n\nexport async function subscribe(\n  ctx: Context,\n  args: {\n    deploymentUrl: string;\n    adminKey: string;\n    identity?: UserIdentityAttributes;\n    parsedFunctionName: string;\n    parsedFunctionArgs: Record<string, Value>;\n    componentPath: string | undefined;\n    until: Promise<unknown>;\n    callbacks?: {\n      onStart?: () => void;\n      onChange?: (result: Value) => void;\n      onStop?: () => void;\n    };\n  },\n) {\n  const client = new BaseConvexClient(\n    args.deploymentUrl,\n    (updatedQueries) => {\n      for (const queryToken of updatedQueries) {\n        args.callbacks?.onChange?.(client.localQueryResultByToken(queryToken)!);\n      }\n    },\n    {\n      // pretend that a Node.js 'ws' library WebSocket is a browser WebSocket\n      webSocketConstructor: ws as unknown as typeof WebSocket,\n      unsavedChangesWarning: false,\n    },\n  );\n  client.setAdminAuth(args.adminKey, args.identity);\n  const { unsubscribe } = client.subscribe(\n    args.parsedFunctionName,\n    args.parsedFunctionArgs,\n    {\n      componentPath: args.componentPath,\n    },\n  );\n\n  args.callbacks?.onStart?.();\n\n  let done = false;\n  const [donePromise, onDone] = waitUntilCalled();\n  const stopWatching = () => {\n    if (done) {\n      return;\n    }\n    done = true;\n    unsubscribe();\n    void client.close();\n    process.off(\"SIGINT\", sigintListener);\n    onDone();\n    args.callbacks?.onStop?.();\n  };\n  function sigintListener() {\n    stopWatching();\n  }\n  process.on(\"SIGINT\", sigintListener);\n  void args.until.finally(stopWatching);\n  while (!done) {\n    // loops once per day (any large value < 2**31 would work)\n    const oneDay = 24 * 60 * 60 * 1000;\n    await Promise.race([\n      donePromise,\n      new Promise((resolve) => setTimeout(resolve, oneDay)),\n    ]);\n  }\n}\n\nexport async function runInDeployment(\n  ctx: OneoffCtx,\n  args: {\n    deploymentUrl: string;\n    adminKey: string;\n    deploymentName: string | null;\n    functionName: string;\n    argsString: string;\n    identityString?: string;\n    push: boolean;\n    watch: boolean;\n    typecheck: \"enable\" | \"try\" | \"disable\";\n    typecheckComponents: boolean;\n    codegen: boolean;\n    componentPath: string | undefined;\n    liveComponentSources: boolean;\n  },\n) {\n  if (args.push) {\n    await watchAndPush(\n      ctx,\n      {\n        url: args.deploymentUrl,\n        adminKey: args.adminKey,\n        deploymentName: args.deploymentName,\n        verbose: false,\n        dryRun: false,\n        typecheck: args.typecheck,\n        typecheckComponents: args.typecheckComponents,\n        debug: false,\n        codegen: args.codegen,\n        liveComponentSources: args.liveComponentSources,\n      },\n      {\n        once: true,\n        traceEvents: false,\n        untilSuccess: true,\n      },\n    );\n  }\n\n  if (args.watch) {\n    return await subscribeAndLog(ctx, args);\n  }\n  return await runFunctionAndLog(ctx, args);\n}\n"], "mappings": ";AAAA,OAAO,WAAW;AAClB,OAAO,UAAU;AACjB,OAAO,QAAQ;AACf,SAAS,wBAAwB;AACjC,SAAS,wBAAwB;AACjC;AAAA,EAGE;AAAA,OACK;AACP,SAAgB,cAAc,oBAAoB;AAClD;AAAA,EAEE;AAAA,EACA;AAAA,EACA;AAAA,OAEK;AACP,SAAS,aAAa,uBAAuB;AAC7C,OAAO,WAAW;AAClB,OAAO,UAAU;AACjB,SAAS,yBAAyB;AAClC,SAAS,oBAAoB;AAE7B,sBAAsB,kBACpB,KACA,MAWA;AACA,QAAM,SAAS,IAAI,iBAAiB,KAAK,aAAa;AACtD,QAAM,WAAW,KAAK,iBAClB,MAAM,gBAAgB,KAAK,KAAK,cAAc,IAC9C;AACJ,SAAO,aAAa,KAAK,UAAU,QAAQ;AAE3C,QAAM,eAAe,MAAM,UAAU,KAAK,KAAK,UAAU;AACzD,QAAM,EAAE,cAAc,IAAI,MAAM,kBAAkB,GAAG;AACrD,QAAM,qBAAqB,MAAM;AAAA,IAC/B;AAAA,IACA,KAAK;AAAA,IACL,cAAc;AAAA,EAChB;AACA,MAAI;AACJ,MAAI;AACF,aAAS,MAAM,OAAO;AAAA,MACpB,sBAAsB,kBAAkB;AAAA,MACxC,KAAK;AAAA,MACL;AAAA,IACF;AAAA,EACF,SAAS,KAAK;AACZ,UAAM,eAAgB,IAAc,SAAS,EAAE,KAAK;AAEpD,QAAI,aAAa,SAAS,yBAAyB,GAAG;AACpD,YAAM,YAAa,MAAM,eAAe,KAAK;AAAA,QAC3C,eAAe,KAAK;AAAA,QACpB,UAAU,KAAK;AAAA,QACf,cAAc;AAAA,QACd,eAAe,KAAK;AAAA,QACpB,MAAM,CAAC;AAAA,MACT,CAAC;AAUD,YAAM,gBAAgB,UACnB;AAAA,QACC,CACE,OAIG,GAAG,iBAAiB;AAAA,MAC3B,EACC,IAAI,CAAC,EAAE,WAAW,MAAM;AACvB,cAAM,eAAe,WAAW,QAAQ,GAAG;AAE3C,cAAMA,QACJ,iBAAiB,KACb,KACA,WAAW,UAAU,GAAG,eAAe,CAAC;AAC9C,cAAM,OACJ,iBAAiB,KACb,aACA,WAAW,UAAU,eAAe,CAAC;AAE3C,eAAO,UAAK,MAAM,KAAKA,KAAI,CAAC,GAAG,IAAI;AAAA,MACrC,CAAC;AAEH,YAAM,4BACJ,cAAc,SAAS,IACnB;AAAA,EAAyB,cAAc,KAAK,IAAI,CAAC,KACjD;AAEN,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB,2BAA2B,KAAK,YAAY;AAAA,EAAO,MAAM,IAAI,YAAY,CAAC;AAAA;AAAA,EAAO,yBAAyB;AAAA,MAC5H,CAAC;AAAA,IACH;AAEA,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,2BAA2B,KAAK,YAAY;AAAA,EAAO,MAAM,IAAI,YAAY,CAAC;AAAA,IAC5F,CAAC;AAAA,EACH;AAEA,OAAK,WAAW,YAAY;AAG5B,MAAI,WAAW,MAAM;AACnB,cAAU,KAAK,YAAY,MAAM,CAAC;AAAA,EACpC;AACF;AAEA,eAAe,gBAAgB,KAAc,gBAAwB;AACnE,MAAI;AACJ,MAAI;AACF,eAAW,MAAM,MAAM,cAAc;AAAA,EACvC,SAAS,KAAK;AACZ,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,sCAAsC,cAAc;AAAA,EAAM,MAAM,IAAK,IAAc,SAAS,EAAE,KAAK,CAAC,CAAC;AAAA,IACvH,CAAC;AAAA,EACH;AACA,QAAM,UAAU,SAAS,WAAW,KAAK,WAAW,KAAK,UAAU,QAAQ,CAAC;AAC5E,QAAM,SAAS,SAAS,UAAU;AAClC,QAAM,kBACJ,SAAS,mBAAmB,GAAG,OAAO,SAAS,CAAC,IAAI,QAAQ,SAAS,CAAC;AACxE,SAAO;AAAA,IACL,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,sBAAsB,UAAU,KAAc,YAAoB;AAChE,MAAI;AACF,UAAM,WAAW,MAAM,MAAM,UAAU;AACvC,WAAO,aAAa,QAAQ;AAAA,EAC9B,SAAS,KAAK;AACZ,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,uCAAuC,UAAU;AAAA,EAAM,MAAM,IAAK,IAAc,SAAS,EAAE,KAAK,CAAC,CAAC;AAAA,IACpH,CAAC;AAAA,EACH;AACF;AAEA,sBAAsB,kBACpB,KACA,cAEA,iBACA;AAOA,MAAI,aAAa,WAAW,MAAM,KAAK,aAAa,WAAW,WAAW,GAAG;AAC3E,UAAM,QAAQ,aAAa,MAAM,GAAG;AACpC,QAAI,MAAM,SAAS,GAAG;AACpB,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB,qCAAqC,YAAY;AAAA,MACnE,CAAC;AAAA,IACH;AACA,UAAMC,cAAa,MAAM,IAAI;AAC7B,UAAM,aAAa,GAAG,MAAM,MAAM,CAAC,EAAE,KAAK,GAAG,CAAC,IAAIA,WAAU;AAC5D,WAAO;AAAA,EACT;AAOA,QAAM,WAAW,aAAa,MAAM,GAAG,EAAE,CAAC;AAC1C,QAAM,qBAAqB;AAAA,IACzB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,MAAI,eAAe;AACnB,MAAI,qBAA6B;AACjC,aAAW,aAAa,oBAAoB;AAC1C,QAAI,SAAS,SAAS,SAAS,GAAG;AAChC,2BAAqB,SAAS,MAAM,GAAG,CAAC,UAAU,MAAM;AACxD,qBAAe;AACf;AAAA,IACF;AAAA,EACF;AAEA,QAAM,aAAa,aAAa,MAAM,GAAG,EAAE,CAAC,KAAK;AACjD,QAAM,iBAAiB,GAAG,kBAAkB,IAAI,UAAU;AAG1D,MAAI,CAAC,SAAS,WAAW,eAAe,GAAG;AACzC,WAAO;AAAA,EACT;AAEA,QAAM,wBAAwB,mBAAmB;AAAA,IAC/C,gBAAgB;AAAA,EAClB;AACA,QAAM,4BAA4B,GAAG,qBAAqB,IAAI,UAAU;AAExE,MAAI,cAAc;AAChB,QAAI,IAAI,GAAG,OAAO,KAAK,KAAK,iBAAiB,QAAQ,CAAC,GAAG;AACvD,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF,OAAO;AACL,UAAM,SAAS,mBAAmB;AAAA,MAAK,CAAC,cACtC,IAAI,GAAG,OAAO,KAAK,KAAK,iBAAiB,WAAW,SAAS,CAAC;AAAA,IAChE;AACA,QAAI,QAAQ;AACV,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAEA,SAAS,WAAW,QAAgB;AAClC,MAAI,OAAO;AACX,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,UAAM,OAAO,OAAO,WAAW,CAAC;AAChC,YAAQ,QAAQ,KAAK,OAAO;AAC5B,WAAO,OAAO;AAAA,EAChB;AACA,SAAO;AACT;AAEA,sBAAsB,wBACpB,KACA,MAQA;AACA,QAAM,UAAU,CAAC;AACjB,MAAI,SAAS;AACb,MAAI,SAAS;AACb,SAAO,CAAC,WAAW,KAAK,UAAU,UAAa,QAAQ,SAAS,KAAK,QAAQ;AAC3E,UAAM,mBAAoB,MAAM,eAAe,KAAK;AAAA,MAClD,GAAG;AAAA,MACH,MAAM;AAAA,QACJ,GAAG,KAAK;AAAA,QACR,gBAAgB;AAAA,UACd;AAAA,UACA,UACE,KAAK,UAAU,SAAY,MAAQ,KAAK,QAAQ,QAAQ;AAAA,QAC5D;AAAA,MACF;AAAA,IACF,CAAC;AACD,aAAS,iBAAiB;AAC1B,aAAS,iBAAiB;AAC1B,YAAQ,KAAK,GAAG,iBAAiB,IAAI;AAAA,EACvC;AACA,SAAO;AACT;AAEA,sBAAsB,eACpB,KACA,MAOgB;AAChB,MAAI;AACJ,QAAM,gBAAgB,IAAI,QAAe,CAAC,YAAY;AACpD,eAAW;AAAA,EACb,CAAC;AACD,QAAM,CAAC,aAAa,MAAM,IAAI,gBAAgB;AAC9C,QAAM,UAAU,KAAK;AAAA,IACnB,GAAG;AAAA,IACH,oBAAoB,KAAK;AAAA,IACzB,oBAAoB,KAAK;AAAA,IACzB,OAAO;AAAA,IACP,WAAW;AAAA,MACT,UAAU,CAAC,WAAW;AACpB,eAAO;AACP,iBAAS,MAAM;AAAA,MACjB;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEO,gBAAS,YAAY,OAAc;AACxC,QAAM,OAAO,aAAa,KAAK;AAC/B,MAAI,QAAQ,OAAO,OAAO;AAGxB,WAAO,KAAK,QAAQ,OAAO,EAAE,QAAQ,MAAM,OAAO,KAAK,CAAC;AAAA,EAC1D,OAAO;AACL,WAAO,KAAK,UAAU,MAAM,MAAM,CAAC;AAAA,EACrC;AACF;AAEA,sBAAsB,gBACpB,KACA,MAQA;AACA,QAAM,EAAE,cAAc,IAAI,MAAM,kBAAkB,GAAG;AAErD,QAAM,qBAAqB,MAAM;AAAA,IAC/B;AAAA,IACA,KAAK;AAAA,IACL,cAAc;AAAA,EAChB;AACA,QAAM,WAAW,KAAK,iBAClB,MAAM,gBAAgB,KAAK,KAAK,cAAc,IAC9C;AACJ,QAAM,eAAe,MAAM,UAAU,KAAK,KAAK,UAAU;AACzD,SAAO,UAAU,KAAK;AAAA,IACpB,eAAe,KAAK;AAAA,IACpB,UAAU,KAAK;AAAA,IACf;AAAA,IACA;AAAA,IACA,oBAAoB;AAAA,IACpB,eAAe,KAAK;AAAA,IACpB,OAAO,YAAY;AAAA,IACnB,WAAW;AAAA,MACT,UAAU;AACR;AAAA,UACE;AAAA,UACA,kBAAkB,KAAK,YAAY,OAAO,KAAK,aAAa;AAAA,QAC9D;AAAA,MACF;AAAA,MACA,SAAS,QAAQ;AACf,kBAAU,KAAK,YAAY,MAAM,CAAC;AAAA,MACpC;AAAA,MACA,SAAS;AACP,mBAAW,KAAK,yBAAyB,KAAK,aAAa,KAAK;AAAA,MAClE;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAEA,sBAAsB,UACpB,KACA,MAcA;AACA,QAAM,SAAS,IAAI;AAAA,IACjB,KAAK;AAAA,IACL,CAAC,mBAAmB;AAClB,iBAAW,cAAc,gBAAgB;AACvC,aAAK,WAAW,WAAW,OAAO,wBAAwB,UAAU,CAAE;AAAA,MACxE;AAAA,IACF;AAAA,IACA;AAAA;AAAA,MAEE,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,IACzB;AAAA,EACF;AACA,SAAO,aAAa,KAAK,UAAU,KAAK,QAAQ;AAChD,QAAM,EAAE,YAAY,IAAI,OAAO;AAAA,IAC7B,KAAK;AAAA,IACL,KAAK;AAAA,IACL;AAAA,MACE,eAAe,KAAK;AAAA,IACtB;AAAA,EACF;AAEA,OAAK,WAAW,UAAU;AAE1B,MAAI,OAAO;AACX,QAAM,CAAC,aAAa,MAAM,IAAI,gBAAgB;AAC9C,QAAM,eAAe,MAAM;AACzB,QAAI,MAAM;AACR;AAAA,IACF;AACA,WAAO;AACP,gBAAY;AACZ,SAAK,OAAO,MAAM;AAClB,YAAQ,IAAI,UAAU,cAAc;AACpC,WAAO;AACP,SAAK,WAAW,SAAS;AAAA,EAC3B;AACA,WAAS,iBAAiB;AACxB,iBAAa;AAAA,EACf;AACA,UAAQ,GAAG,UAAU,cAAc;AACnC,OAAK,KAAK,MAAM,QAAQ,YAAY;AACpC,SAAO,CAAC,MAAM;AAEZ,UAAM,SAAS,KAAK,KAAK,KAAK;AAC9B,UAAM,QAAQ,KAAK;AAAA,MACjB;AAAA,MACA,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,MAAM,CAAC;AAAA,IACtD,CAAC;AAAA,EACH;AACF;AAEA,sBAAsB,gBACpB,KACA,MAeA;AACA,MAAI,KAAK,MAAM;AACb,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,QACE,KAAK,KAAK;AAAA,QACV,UAAU,KAAK;AAAA,QACf,gBAAgB,KAAK;AAAA,QACrB,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,WAAW,KAAK;AAAA,QAChB,qBAAqB,KAAK;AAAA,QAC1B,OAAO;AAAA,QACP,SAAS,KAAK;AAAA,QACd,sBAAsB,KAAK;AAAA,MAC7B;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAEA,MAAI,KAAK,OAAO;AACd,WAAO,MAAM,gBAAgB,KAAK,IAAI;AAAA,EACxC;AACA,SAAO,MAAM,kBAAkB,KAAK,IAAI;AAC1C;", "names": ["path", "exportName"]}
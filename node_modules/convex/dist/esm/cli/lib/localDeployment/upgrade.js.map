{"version": 3, "sources": ["../../../../../src/cli/lib/localDeployment/upgrade.ts"], "sourcesContent": ["import path from \"path\";\nimport {\n  Context,\n  logFailure,\n  logFinishedStep,\n  logVerbose,\n} from \"../../../bundler/context.js\";\nimport { runSystemQuery } from \"../run.js\";\nimport {\n  LocalDeploymentKind,\n  deploymentStateDir,\n  loadDeploymentConfig,\n  saveDeploymentConfig,\n} from \"./filePaths.js\";\nimport {\n  ensureBackendStopped,\n  localDeploymentUrl,\n  runLocalBackend,\n} from \"./run.js\";\nimport {\n  downloadSnapshotExport,\n  startSnapshotExport,\n} from \"../convexExport.js\";\nimport { deploymentFetch, logAndHandleFetchError } from \"../utils/utils.js\";\nimport {\n  confirmImport,\n  uploadForImport,\n  waitForStableImportState,\n} from \"../convexImport.js\";\nimport { promptOptions, promptYesNo } from \"../utils/prompts.js\";\nimport { recursivelyDelete } from \"../fsUtils.js\";\nimport { LocalDeploymentError } from \"./errors.js\";\nimport { ensureBackendBinaryDownloaded } from \"./download.js\";\nexport async function handlePotentialUpgrade(\n  ctx: Context,\n  args: {\n    deploymentKind: LocalDeploymentKind;\n    deploymentName: string;\n    oldVersion: string | null;\n    newBinaryPath: string;\n    newVersion: string;\n    ports: {\n      cloud: number;\n      site: number;\n    };\n    adminKey: string;\n    instanceSecret: string;\n    forceUpgrade: boolean;\n  },\n): Promise<{ cleanupHandle: string }> {\n  const newConfig = {\n    ports: args.ports,\n    backendVersion: args.newVersion,\n    adminKey: args.adminKey,\n    instanceSecret: args.instanceSecret,\n  };\n  if (args.oldVersion === null || args.oldVersion === args.newVersion) {\n    // No upgrade needed. Save the current config and start running the backend.\n    saveDeploymentConfig(\n      ctx,\n      args.deploymentKind,\n      args.deploymentName,\n      newConfig,\n    );\n    return runLocalBackend(ctx, {\n      binaryPath: args.newBinaryPath,\n      deploymentKind: args.deploymentKind,\n      deploymentName: args.deploymentName,\n      ports: args.ports,\n      instanceSecret: args.instanceSecret,\n      isLatestVersion: true,\n    });\n  }\n  logVerbose(\n    ctx,\n    `Considering upgrade from ${args.oldVersion} to ${args.newVersion}`,\n  );\n  const confirmed =\n    args.forceUpgrade ||\n    (await promptYesNo(ctx, {\n      message: `This deployment is using an older version of the Convex backend. Upgrade now?`,\n      default: true,\n    }));\n  if (!confirmed) {\n    const { binaryPath: oldBinaryPath } = await ensureBackendBinaryDownloaded(\n      ctx,\n      {\n        kind: \"version\",\n        version: args.oldVersion,\n      },\n    );\n    // Skipping upgrade, save the config with the old version and run.\n    saveDeploymentConfig(ctx, args.deploymentKind, args.deploymentName, {\n      ...newConfig,\n      backendVersion: args.oldVersion,\n    });\n    return runLocalBackend(ctx, {\n      binaryPath: oldBinaryPath,\n      ports: args.ports,\n      deploymentKind: args.deploymentKind,\n      deploymentName: args.deploymentName,\n      instanceSecret: args.instanceSecret,\n      isLatestVersion: false,\n    });\n  }\n  const choice = args.forceUpgrade\n    ? \"transfer\"\n    : await promptOptions(ctx, {\n        message: \"Transfer data from existing deployment?\",\n        default: \"transfer\",\n        choices: [\n          { name: \"transfer data\", value: \"transfer\" },\n          { name: \"start fresh\", value: \"reset\" },\n        ],\n      });\n  const deploymentStatePath = deploymentStateDir(\n    args.deploymentKind,\n    args.deploymentName,\n  );\n  if (choice === \"reset\") {\n    recursivelyDelete(ctx, deploymentStatePath, { force: true });\n    saveDeploymentConfig(\n      ctx,\n      args.deploymentKind,\n      args.deploymentName,\n      newConfig,\n    );\n    return runLocalBackend(ctx, {\n      binaryPath: args.newBinaryPath,\n      deploymentKind: args.deploymentKind,\n      deploymentName: args.deploymentName,\n      ports: args.ports,\n      instanceSecret: args.instanceSecret,\n      isLatestVersion: true,\n    });\n  }\n  const newAdminKey = args.adminKey;\n  const oldAdminKey =\n    loadDeploymentConfig(ctx, args.deploymentKind, args.deploymentName)\n      ?.adminKey ?? args.adminKey;\n  return handleUpgrade(ctx, {\n    deploymentKind: args.deploymentKind,\n    deploymentName: args.deploymentName,\n    oldVersion: args.oldVersion!,\n    newBinaryPath: args.newBinaryPath,\n    newVersion: args.newVersion,\n    ports: args.ports,\n    oldAdminKey,\n    newAdminKey,\n    instanceSecret: args.instanceSecret,\n  });\n}\n\nasync function handleUpgrade(\n  ctx: Context,\n  args: {\n    deploymentName: string;\n    deploymentKind: LocalDeploymentKind;\n    oldVersion: string;\n    newBinaryPath: string;\n    newVersion: string;\n    ports: {\n      cloud: number;\n      site: number;\n    };\n    // In most of the cases the admin key is the same for the old and new version.\n    // This is helpful when we start generating new admin key formats that might\n    // be incompatible with older backend versions.\n    oldAdminKey: string;\n    newAdminKey: string;\n    instanceSecret: string;\n  },\n): Promise<{ cleanupHandle: string }> {\n  const { binaryPath: oldBinaryPath } = await ensureBackendBinaryDownloaded(\n    ctx,\n    {\n      kind: \"version\",\n      version: args.oldVersion,\n    },\n  );\n\n  logVerbose(ctx, \"Running backend on old version\");\n  const { cleanupHandle: oldCleanupHandle } = await runLocalBackend(ctx, {\n    binaryPath: oldBinaryPath,\n    ports: args.ports,\n    deploymentKind: args.deploymentKind,\n    deploymentName: args.deploymentName,\n    instanceSecret: args.instanceSecret,\n    isLatestVersion: false,\n  });\n\n  logVerbose(ctx, \"Downloading env vars\");\n  const deploymentUrl = localDeploymentUrl(args.ports.cloud);\n  const envs = (await runSystemQuery(ctx, {\n    deploymentUrl,\n    adminKey: args.oldAdminKey,\n    functionName: \"_system/cli/queryEnvironmentVariables\",\n    componentPath: undefined,\n    args: {},\n  })) as Array<{\n    name: string;\n    value: string;\n  }>;\n\n  logVerbose(ctx, \"Doing a snapshot export\");\n  const exportPath = path.join(\n    deploymentStateDir(args.deploymentKind, args.deploymentName),\n    \"export.zip\",\n  );\n  if (ctx.fs.exists(exportPath)) {\n    ctx.fs.unlink(exportPath);\n  }\n  const snaphsotExportState = await startSnapshotExport(ctx, {\n    deploymentUrl,\n    adminKey: args.oldAdminKey,\n    includeStorage: true,\n    inputPath: exportPath,\n  });\n  if (snaphsotExportState.state !== \"completed\") {\n    return ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: \"Failed to export snapshot\",\n    });\n  }\n  await downloadSnapshotExport(ctx, {\n    snapshotExportTs: snaphsotExportState.start_ts,\n    inputPath: exportPath,\n    adminKey: args.oldAdminKey,\n    deploymentUrl,\n  });\n\n  logVerbose(ctx, \"Stopping the backend on the old version\");\n  const oldCleanupFunc = ctx.removeCleanup(oldCleanupHandle);\n  if (oldCleanupFunc) {\n    await oldCleanupFunc(0);\n  }\n  await ensureBackendStopped(ctx, {\n    ports: args.ports,\n    maxTimeSecs: 5,\n    deploymentName: args.deploymentName,\n    allowOtherDeployments: false,\n  });\n\n  // TODO(ENG-7078) save old artifacts to backup files\n  logVerbose(ctx, \"Running backend on new version\");\n  const { cleanupHandle } = await runLocalBackend(ctx, {\n    binaryPath: args.newBinaryPath,\n    ports: args.ports,\n    deploymentKind: args.deploymentKind,\n    deploymentName: args.deploymentName,\n    instanceSecret: args.instanceSecret,\n    isLatestVersion: true,\n  });\n\n  logVerbose(ctx, \"Importing the env vars\");\n  if (envs.length > 0) {\n    const fetch = deploymentFetch(ctx, {\n      deploymentUrl,\n      adminKey: args.newAdminKey,\n    });\n    try {\n      await fetch(\"/api/update_environment_variables\", {\n        body: JSON.stringify({ changes: envs }),\n        method: \"POST\",\n      });\n    } catch (e) {\n      // TODO: this should ideally have a `LocalDeploymentError`\n      return await logAndHandleFetchError(ctx, e);\n    }\n  }\n\n  logVerbose(ctx, \"Doing a snapshot import\");\n  const importId = await uploadForImport(ctx, {\n    deploymentUrl,\n    adminKey: args.newAdminKey,\n    filePath: exportPath,\n    importArgs: { format: \"zip\", mode: \"replace\", tableName: undefined },\n    onImportFailed: async (e) => {\n      logFailure(ctx, `Failed to import snapshot: ${e}`);\n    },\n  });\n  logVerbose(ctx, `Snapshot import started`);\n  let status = await waitForStableImportState(ctx, {\n    importId,\n    deploymentUrl,\n    adminKey: args.newAdminKey,\n    onProgress: () => {\n      // do nothing for now\n      return 0;\n    },\n  });\n  if (status.state !== \"waiting_for_confirmation\") {\n    const message = \"Error while transferring data: Failed to upload snapshot\";\n    return ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: message,\n      errForSentry: new LocalDeploymentError(message),\n    });\n  }\n\n  await confirmImport(ctx, {\n    importId,\n    adminKey: args.newAdminKey,\n    deploymentUrl,\n    onError: async (e) => {\n      logFailure(ctx, `Failed to confirm import: ${e}`);\n    },\n  });\n  logVerbose(ctx, `Snapshot import confirmed`);\n  status = await waitForStableImportState(ctx, {\n    importId,\n    deploymentUrl,\n    adminKey: args.newAdminKey,\n    onProgress: () => {\n      // do nothing for now\n      return 0;\n    },\n  });\n  logVerbose(ctx, `Snapshot import status: ${status.state}`);\n  if (status.state !== \"completed\") {\n    const message = \"Error while transferring data: Failed to import snapshot\";\n    return ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: message,\n      errForSentry: new LocalDeploymentError(message),\n    });\n  }\n\n  logFinishedStep(ctx, \"Successfully upgraded to a new backend version\");\n  saveDeploymentConfig(ctx, args.deploymentKind, args.deploymentName, {\n    ports: args.ports,\n    backendVersion: args.newVersion,\n    adminKey: args.newAdminKey,\n    instanceSecret: args.instanceSecret,\n  });\n\n  return { cleanupHandle };\n}\n"], "mappings": ";AAAA,OAAO,UAAU;AACjB;AAAA,EAEE;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP,SAAS,sBAAsB;AAC/B;AAAA,EAEE;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP;AAAA,EACE;AAAA,EACA;AAAA,OACK;AACP,SAAS,iBAAiB,8BAA8B;AACxD;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP,SAAS,eAAe,mBAAmB;AAC3C,SAAS,yBAAyB;AAClC,SAAS,4BAA4B;AACrC,SAAS,qCAAqC;AAC9C,sBAAsB,uBACpB,KACA,MAcoC;AACpC,QAAM,YAAY;AAAA,IAChB,OAAO,KAAK;AAAA,IACZ,gBAAgB,KAAK;AAAA,IACrB,UAAU,KAAK;AAAA,IACf,gBAAgB,KAAK;AAAA,EACvB;AACA,MAAI,KAAK,eAAe,QAAQ,KAAK,eAAe,KAAK,YAAY;AAEnE;AAAA,MACE;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,IACF;AACA,WAAO,gBAAgB,KAAK;AAAA,MAC1B,YAAY,KAAK;AAAA,MACjB,gBAAgB,KAAK;AAAA,MACrB,gBAAgB,KAAK;AAAA,MACrB,OAAO,KAAK;AAAA,MACZ,gBAAgB,KAAK;AAAA,MACrB,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACA;AAAA,IACE;AAAA,IACA,4BAA4B,KAAK,UAAU,OAAO,KAAK,UAAU;AAAA,EACnE;AACA,QAAM,YACJ,KAAK,gBACJ,MAAM,YAAY,KAAK;AAAA,IACtB,SAAS;AAAA,IACT,SAAS;AAAA,EACX,CAAC;AACH,MAAI,CAAC,WAAW;AACd,UAAM,EAAE,YAAY,cAAc,IAAI,MAAM;AAAA,MAC1C;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,SAAS,KAAK;AAAA,MAChB;AAAA,IACF;AAEA,yBAAqB,KAAK,KAAK,gBAAgB,KAAK,gBAAgB;AAAA,MAClE,GAAG;AAAA,MACH,gBAAgB,KAAK;AAAA,IACvB,CAAC;AACD,WAAO,gBAAgB,KAAK;AAAA,MAC1B,YAAY;AAAA,MACZ,OAAO,KAAK;AAAA,MACZ,gBAAgB,KAAK;AAAA,MACrB,gBAAgB,KAAK;AAAA,MACrB,gBAAgB,KAAK;AAAA,MACrB,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACA,QAAM,SAAS,KAAK,eAChB,aACA,MAAM,cAAc,KAAK;AAAA,IACvB,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,MACP,EAAE,MAAM,iBAAiB,OAAO,WAAW;AAAA,MAC3C,EAAE,MAAM,eAAe,OAAO,QAAQ;AAAA,IACxC;AAAA,EACF,CAAC;AACL,QAAM,sBAAsB;AAAA,IAC1B,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AACA,MAAI,WAAW,SAAS;AACtB,sBAAkB,KAAK,qBAAqB,EAAE,OAAO,KAAK,CAAC;AAC3D;AAAA,MACE;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,IACF;AACA,WAAO,gBAAgB,KAAK;AAAA,MAC1B,YAAY,KAAK;AAAA,MACjB,gBAAgB,KAAK;AAAA,MACrB,gBAAgB,KAAK;AAAA,MACrB,OAAO,KAAK;AAAA,MACZ,gBAAgB,KAAK;AAAA,MACrB,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACA,QAAM,cAAc,KAAK;AACzB,QAAM,cACJ,qBAAqB,KAAK,KAAK,gBAAgB,KAAK,cAAc,GAC9D,YAAY,KAAK;AACvB,SAAO,cAAc,KAAK;AAAA,IACxB,gBAAgB,KAAK;AAAA,IACrB,gBAAgB,KAAK;AAAA,IACrB,YAAY,KAAK;AAAA,IACjB,eAAe,KAAK;AAAA,IACpB,YAAY,KAAK;AAAA,IACjB,OAAO,KAAK;AAAA,IACZ;AAAA,IACA;AAAA,IACA,gBAAgB,KAAK;AAAA,EACvB,CAAC;AACH;AAEA,eAAe,cACb,KACA,MAiBoC;AACpC,QAAM,EAAE,YAAY,cAAc,IAAI,MAAM;AAAA,IAC1C;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,SAAS,KAAK;AAAA,IAChB;AAAA,EACF;AAEA,aAAW,KAAK,gCAAgC;AAChD,QAAM,EAAE,eAAe,iBAAiB,IAAI,MAAM,gBAAgB,KAAK;AAAA,IACrE,YAAY;AAAA,IACZ,OAAO,KAAK;AAAA,IACZ,gBAAgB,KAAK;AAAA,IACrB,gBAAgB,KAAK;AAAA,IACrB,gBAAgB,KAAK;AAAA,IACrB,iBAAiB;AAAA,EACnB,CAAC;AAED,aAAW,KAAK,sBAAsB;AACtC,QAAM,gBAAgB,mBAAmB,KAAK,MAAM,KAAK;AACzD,QAAM,OAAQ,MAAM,eAAe,KAAK;AAAA,IACtC;AAAA,IACA,UAAU,KAAK;AAAA,IACf,cAAc;AAAA,IACd,eAAe;AAAA,IACf,MAAM,CAAC;AAAA,EACT,CAAC;AAKD,aAAW,KAAK,yBAAyB;AACzC,QAAM,aAAa,KAAK;AAAA,IACtB,mBAAmB,KAAK,gBAAgB,KAAK,cAAc;AAAA,IAC3D;AAAA,EACF;AACA,MAAI,IAAI,GAAG,OAAO,UAAU,GAAG;AAC7B,QAAI,GAAG,OAAO,UAAU;AAAA,EAC1B;AACA,QAAM,sBAAsB,MAAM,oBAAoB,KAAK;AAAA,IACzD;AAAA,IACA,UAAU,KAAK;AAAA,IACf,gBAAgB;AAAA,IAChB,WAAW;AAAA,EACb,CAAC;AACD,MAAI,oBAAoB,UAAU,aAAa;AAC7C,WAAO,IAAI,MAAM;AAAA,MACf,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AACA,QAAM,uBAAuB,KAAK;AAAA,IAChC,kBAAkB,oBAAoB;AAAA,IACtC,WAAW;AAAA,IACX,UAAU,KAAK;AAAA,IACf;AAAA,EACF,CAAC;AAED,aAAW,KAAK,yCAAyC;AACzD,QAAM,iBAAiB,IAAI,cAAc,gBAAgB;AACzD,MAAI,gBAAgB;AAClB,UAAM,eAAe,CAAC;AAAA,EACxB;AACA,QAAM,qBAAqB,KAAK;AAAA,IAC9B,OAAO,KAAK;AAAA,IACZ,aAAa;AAAA,IACb,gBAAgB,KAAK;AAAA,IACrB,uBAAuB;AAAA,EACzB,CAAC;AAGD,aAAW,KAAK,gCAAgC;AAChD,QAAM,EAAE,cAAc,IAAI,MAAM,gBAAgB,KAAK;AAAA,IACnD,YAAY,KAAK;AAAA,IACjB,OAAO,KAAK;AAAA,IACZ,gBAAgB,KAAK;AAAA,IACrB,gBAAgB,KAAK;AAAA,IACrB,gBAAgB,KAAK;AAAA,IACrB,iBAAiB;AAAA,EACnB,CAAC;AAED,aAAW,KAAK,wBAAwB;AACxC,MAAI,KAAK,SAAS,GAAG;AACnB,UAAM,QAAQ,gBAAgB,KAAK;AAAA,MACjC;AAAA,MACA,UAAU,KAAK;AAAA,IACjB,CAAC;AACD,QAAI;AACF,YAAM,MAAM,qCAAqC;AAAA,QAC/C,MAAM,KAAK,UAAU,EAAE,SAAS,KAAK,CAAC;AAAA,QACtC,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,SAAS,GAAG;AAEV,aAAO,MAAM,uBAAuB,KAAK,CAAC;AAAA,IAC5C;AAAA,EACF;AAEA,aAAW,KAAK,yBAAyB;AACzC,QAAM,WAAW,MAAM,gBAAgB,KAAK;AAAA,IAC1C;AAAA,IACA,UAAU,KAAK;AAAA,IACf,UAAU;AAAA,IACV,YAAY,EAAE,QAAQ,OAAO,MAAM,WAAW,WAAW,OAAU;AAAA,IACnE,gBAAgB,OAAO,MAAM;AAC3B,iBAAW,KAAK,8BAA8B,CAAC,EAAE;AAAA,IACnD;AAAA,EACF,CAAC;AACD,aAAW,KAAK,yBAAyB;AACzC,MAAI,SAAS,MAAM,yBAAyB,KAAK;AAAA,IAC/C;AAAA,IACA;AAAA,IACA,UAAU,KAAK;AAAA,IACf,YAAY,MAAM;AAEhB,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACD,MAAI,OAAO,UAAU,4BAA4B;AAC/C,UAAM,UAAU;AAChB,WAAO,IAAI,MAAM;AAAA,MACf,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,cAAc,IAAI,qBAAqB,OAAO;AAAA,IAChD,CAAC;AAAA,EACH;AAEA,QAAM,cAAc,KAAK;AAAA,IACvB;AAAA,IACA,UAAU,KAAK;AAAA,IACf;AAAA,IACA,SAAS,OAAO,MAAM;AACpB,iBAAW,KAAK,6BAA6B,CAAC,EAAE;AAAA,IAClD;AAAA,EACF,CAAC;AACD,aAAW,KAAK,2BAA2B;AAC3C,WAAS,MAAM,yBAAyB,KAAK;AAAA,IAC3C;AAAA,IACA;AAAA,IACA,UAAU,KAAK;AAAA,IACf,YAAY,MAAM;AAEhB,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACD,aAAW,KAAK,2BAA2B,OAAO,KAAK,EAAE;AACzD,MAAI,OAAO,UAAU,aAAa;AAChC,UAAM,UAAU;AAChB,WAAO,IAAI,MAAM;AAAA,MACf,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,cAAc,IAAI,qBAAqB,OAAO;AAAA,IAChD,CAAC;AAAA,EACH;AAEA,kBAAgB,KAAK,gDAAgD;AACrE,uBAAqB,KAAK,KAAK,gBAAgB,KAAK,gBAAgB;AAAA,IAClE,OAAO,KAAK;AAAA,IACZ,gBAAgB,KAAK;AAAA,IACrB,UAAU,KAAK;AAAA,IACf,gBAAgB,KAAK;AAAA,EACvB,CAAC;AAED,SAAO,EAAE,cAAc;AACzB;", "names": []}
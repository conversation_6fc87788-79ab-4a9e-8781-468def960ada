{"version": 3, "sources": ["../../../src/server/schema.ts"], "sourcesContent": ["/**\n * Utilities for defining the schema of your Convex project.\n *\n * ## Usage\n *\n * Schemas should be placed in a `schema.ts` file in your `convex/` directory.\n *\n * Schema definitions should be built using {@link defineSchema},\n * {@link defineTable}, and {@link values.v}. Make sure to export the schema as the\n * default export.\n *\n * ```ts\n * import { defineSchema, defineTable } from \"convex/server\";\n * import { v } from \"convex/values\";\n *\n *  export default defineSchema({\n *    messages: defineTable({\n *      body: v.string(),\n *      user: v.id(\"users\"),\n *    }),\n *    users: defineTable({\n *      name: v.string(),\n *    }),\n *  });\n * ```\n *\n * To learn more about schemas, see [Defining a Schema](https://docs.convex.dev/using/schemas).\n * @module\n */\nimport {\n  AnyDataModel,\n  GenericDataModel,\n  GenericTableIndexes,\n  GenericTableSearchIndexes,\n  GenericTableVectorIndexes,\n  TableNamesInDataModel,\n} from \"../server/data_model.js\";\nimport {\n  <PERSON><PERSON><PERSON><PERSON>,\n  IndexTiebreakerField,\n  SystemFields,\n  SystemIndexes,\n} from \"../server/system_fields.js\";\nimport { Expand } from \"../type_utils.js\";\nimport {\n  GenericValidator,\n  ObjectType,\n  isValidator,\n  v,\n} from \"../values/validator.js\";\nimport { VObject, Validator } from \"../values/validators.js\";\n\n/**\n * Extract all of the index field paths within a {@link Validator}.\n *\n * This is used within {@link defineTable}.\n * @public\n */\ntype ExtractFieldPaths<T extends Validator<any, any, any>> =\n  // Add in the system fields available in index definitions.\n  // This should be everything except for `_id` because thats added to indexes\n  // automatically.\n  T[\"fieldPaths\"] | keyof SystemFields;\n\n/**\n * Extract the {@link GenericDocument} within a {@link Validator} and\n * add on the system fields.\n *\n * This is used within {@link defineTable}.\n * @public\n */\ntype ExtractDocument<T extends Validator<any, any, any>> =\n  // Add the system fields to `Value` (except `_id` because it depends on\n  //the table name) and trick TypeScript into expanding them.\n  Expand<SystemFields & T[\"type\"]>;\n\n/**\n * The configuration for a full text search index.\n *\n * @public\n */\nexport interface SearchIndexConfig<\n  SearchField extends string,\n  FilterFields extends string,\n> {\n  /**\n   * The field to index for full text search.\n   *\n   * This must be a field of type `string`.\n   */\n  searchField: SearchField;\n\n  /**\n   * Additional fields to index for fast filtering when running search queries.\n   */\n  filterFields?: FilterFields[];\n}\n\n/**\n * The configuration for a vector index.\n *\n * @public\n */\nexport interface VectorIndexConfig<\n  VectorField extends string,\n  FilterFields extends string,\n> {\n  /**\n   * The field to index for vector search.\n   *\n   * This must be a field of type `v.array(v.float64())` (or a union)\n   */\n  vectorField: VectorField;\n  /**\n   * The length of the vectors indexed. This must be between 2 and 2048 inclusive.\n   */\n  dimensions: number;\n  /**\n   * Additional fields to index for fast filtering when running vector searches.\n   */\n  filterFields?: FilterFields[];\n}\n\n/**\n * @internal\n */\nexport type VectorIndex = {\n  indexDescriptor: string;\n  vectorField: string;\n  dimensions: number;\n  filterFields: string[];\n};\n\n/**\n * @internal\n */\nexport type Index = {\n  indexDescriptor: string;\n  fields: string[];\n};\n\n/**\n * @internal\n */\nexport type SearchIndex = {\n  indexDescriptor: string;\n  searchField: string;\n  filterFields: string[];\n};\n/**\n * The definition of a table within a schema.\n *\n * This should be produced by using {@link defineTable}.\n * @public\n */\nexport class TableDefinition<\n  DocumentType extends Validator<any, any, any> = Validator<any, any, any>,\n  Indexes extends GenericTableIndexes = {},\n  SearchIndexes extends GenericTableSearchIndexes = {},\n  VectorIndexes extends GenericTableVectorIndexes = {},\n> {\n  private indexes: Index[];\n  private searchIndexes: SearchIndex[];\n  private vectorIndexes: VectorIndex[];\n  // The type of documents stored in this table.\n  validator: DocumentType;\n\n  /**\n   * @internal\n   */\n  constructor(documentType: DocumentType) {\n    this.indexes = [];\n    this.searchIndexes = [];\n    this.vectorIndexes = [];\n    this.validator = documentType;\n  }\n\n  /**\n   * This API is experimental: it may change or disappear.\n   *\n   * Returns indexes defined on this table.\n   * Intended for the advanced use cases of dynamically deciding which index to use for a query.\n   * If you think you need this, please chime in on ths issue in the Convex JS GitHub repo.\n   * https://github.com/get-convex/convex-js/issues/49\n   */\n  \" indexes\"(): { indexDescriptor: string; fields: string[] }[] {\n    return this.indexes;\n  }\n\n  /**\n   * Define an index on this table.\n   *\n   * To learn about indexes, see [Defining Indexes](https://docs.convex.dev/using/indexes).\n   *\n   * @param name - The name of the index.\n   * @param fields - The fields to index, in order. Must specify at least one\n   * field.\n   * @returns A {@link TableDefinition} with this index included.\n   */\n  index<\n    IndexName extends string,\n    FirstFieldPath extends ExtractFieldPaths<DocumentType>,\n    RestFieldPaths extends ExtractFieldPaths<DocumentType>[],\n  >(\n    name: IndexName,\n    fields: [FirstFieldPath, ...RestFieldPaths],\n  ): TableDefinition<\n    DocumentType,\n    // Update `Indexes` to include the new index and use `Expand` to make the\n    // types look pretty in editors.\n    Expand<\n      Indexes &\n        Record<\n          IndexName,\n          [FirstFieldPath, ...RestFieldPaths, IndexTiebreakerField]\n        >\n    >,\n    SearchIndexes,\n    VectorIndexes\n  > {\n    this.indexes.push({ indexDescriptor: name, fields });\n    return this;\n  }\n\n  /**\n   * Define a search index on this table.\n   *\n   * To learn about search indexes, see [Search](https://docs.convex.dev/text-search).\n   *\n   * @param name - The name of the index.\n   * @param indexConfig - The search index configuration object.\n   * @returns A {@link TableDefinition} with this search index included.\n   */\n  searchIndex<\n    IndexName extends string,\n    SearchField extends ExtractFieldPaths<DocumentType>,\n    FilterFields extends ExtractFieldPaths<DocumentType> = never,\n  >(\n    name: IndexName,\n    indexConfig: Expand<SearchIndexConfig<SearchField, FilterFields>>,\n  ): TableDefinition<\n    DocumentType,\n    Indexes,\n    // Update `SearchIndexes` to include the new index and use `Expand` to make\n    // the types look pretty in editors.\n    Expand<\n      SearchIndexes &\n        Record<\n          IndexName,\n          {\n            searchField: SearchField;\n            filterFields: FilterFields;\n          }\n        >\n    >,\n    VectorIndexes\n  > {\n    this.searchIndexes.push({\n      indexDescriptor: name,\n      searchField: indexConfig.searchField,\n      filterFields: indexConfig.filterFields || [],\n    });\n    return this;\n  }\n\n  /**\n   * Define a vector index on this table.\n   *\n   * To learn about vector indexes, see [Vector Search](https://docs.convex.dev/vector-search).\n   *\n   * @param name - The name of the index.\n   * @param indexConfig - The vector index configuration object.\n   * @returns A {@link TableDefinition} with this vector index included.\n   */\n  vectorIndex<\n    IndexName extends string,\n    VectorField extends ExtractFieldPaths<DocumentType>,\n    FilterFields extends ExtractFieldPaths<DocumentType> = never,\n  >(\n    name: IndexName,\n    indexConfig: Expand<VectorIndexConfig<VectorField, FilterFields>>,\n  ): TableDefinition<\n    DocumentType,\n    Indexes,\n    SearchIndexes,\n    Expand<\n      VectorIndexes &\n        Record<\n          IndexName,\n          {\n            vectorField: VectorField;\n            dimensions: number;\n            filterFields: FilterFields;\n          }\n        >\n    >\n  > {\n    this.vectorIndexes.push({\n      indexDescriptor: name,\n      vectorField: indexConfig.vectorField,\n      dimensions: indexConfig.dimensions,\n      filterFields: indexConfig.filterFields || [],\n    });\n    return this;\n  }\n\n  /**\n   * Work around for https://github.com/microsoft/TypeScript/issues/57035\n   */\n  protected self(): TableDefinition<\n    DocumentType,\n    Indexes,\n    SearchIndexes,\n    VectorIndexes\n  > {\n    return this;\n  }\n  /**\n   * Export the contents of this definition.\n   *\n   * This is called internally by the Convex framework.\n   * @internal\n   */\n  export() {\n    const documentType = this.validator.json;\n    if (typeof documentType !== \"object\") {\n      throw new Error(\n        \"Invalid validator: please make sure that the parameter of `defineTable` is valid (see https://docs.convex.dev/database/schemas)\",\n      );\n    }\n\n    return {\n      indexes: this.indexes,\n      searchIndexes: this.searchIndexes,\n      vectorIndexes: this.vectorIndexes,\n      documentType,\n    };\n  }\n}\n\n/**\n * Define a table in a schema.\n *\n * You can either specify the schema of your documents as an object like\n * ```ts\n * defineTable({\n *   field: v.string()\n * });\n * ```\n *\n * or as a schema type like\n * ```ts\n * defineTable(\n *  v.union(\n *    v.object({...}),\n *    v.object({...})\n *  )\n * );\n * ```\n *\n * @param documentSchema - The type of documents stored in this table.\n * @returns A {@link TableDefinition} for the table.\n *\n * @public\n */\nexport function defineTable<\n  DocumentSchema extends Validator<Record<string, any>, \"required\", any>,\n>(documentSchema: DocumentSchema): TableDefinition<DocumentSchema>;\n/**\n * Define a table in a schema.\n *\n * You can either specify the schema of your documents as an object like\n * ```ts\n * defineTable({\n *   field: v.string()\n * });\n * ```\n *\n * or as a schema type like\n * ```ts\n * defineTable(\n *  v.union(\n *    v.object({...}),\n *    v.object({...})\n *  )\n * );\n * ```\n *\n * @param documentSchema - The type of documents stored in this table.\n * @returns A {@link TableDefinition} for the table.\n *\n * @public\n */\nexport function defineTable<\n  DocumentSchema extends Record<string, GenericValidator>,\n>(\n  documentSchema: DocumentSchema,\n): TableDefinition<VObject<ObjectType<DocumentSchema>, DocumentSchema>>;\nexport function defineTable<\n  DocumentSchema extends\n    | Validator<Record<string, any>, \"required\", any>\n    | Record<string, GenericValidator>,\n>(documentSchema: DocumentSchema): TableDefinition<any, any, any> {\n  if (isValidator(documentSchema)) {\n    return new TableDefinition(documentSchema);\n  } else {\n    return new TableDefinition(v.object(documentSchema));\n  }\n}\n\n/**\n * A type describing the schema of a Convex project.\n *\n * This should be constructed using {@link defineSchema}, {@link defineTable},\n * and {@link v}.\n * @public\n */\nexport type GenericSchema = Record<string, TableDefinition>;\n\n/**\n *\n * The definition of a Convex project schema.\n *\n * This should be produced by using {@link defineSchema}.\n * @public\n */\nexport class SchemaDefinition<\n  Schema extends GenericSchema,\n  StrictTableTypes extends boolean,\n> {\n  public tables: Schema;\n  public strictTableNameTypes!: StrictTableTypes;\n  private readonly schemaValidation: boolean;\n\n  /**\n   * @internal\n   */\n  constructor(tables: Schema, options?: DefineSchemaOptions<StrictTableTypes>) {\n    this.tables = tables;\n    this.schemaValidation =\n      options?.schemaValidation === undefined ? true : options.schemaValidation;\n  }\n\n  /**\n   * Export the contents of this definition.\n   *\n   * This is called internally by the Convex framework.\n   * @internal\n   */\n  export(): string {\n    return JSON.stringify({\n      tables: Object.entries(this.tables).map(([tableName, definition]) => {\n        const { indexes, searchIndexes, vectorIndexes, documentType } =\n          definition.export();\n        return {\n          tableName,\n          indexes,\n          searchIndexes,\n          vectorIndexes,\n          documentType,\n        };\n      }),\n      schemaValidation: this.schemaValidation,\n    });\n  }\n}\n\n/**\n * Options for {@link defineSchema}.\n *\n * @public\n */\nexport interface DefineSchemaOptions<StrictTableNameTypes extends boolean> {\n  /**\n   * Whether Convex should validate at runtime that all documents match\n   * your schema.\n   *\n   * If `schemaValidation` is `true`, Convex will:\n   * 1. Check that all existing documents match your schema when your schema\n   * is pushed.\n   * 2. Check that all insertions and updates match your schema during mutations.\n   *\n   * If `schemaValidation` is `false`, Convex will not validate that new or\n   * existing documents match your schema. You'll still get schema-specific\n   * TypeScript types, but there will be no validation at runtime that your\n   * documents match those types.\n   *\n   * By default, `schemaValidation` is `true`.\n   */\n  schemaValidation?: boolean;\n\n  /**\n   * Whether the TypeScript types should allow accessing tables not in the schema.\n   *\n   * If `strictTableNameTypes` is `true`, using tables not listed in the schema\n   * will generate a TypeScript compilation error.\n   *\n   * If `strictTableNameTypes` is `false`, you'll be able to access tables not\n   * listed in the schema and their document type will be `any`.\n   *\n   * `strictTableNameTypes: false` is useful for rapid prototyping.\n   *\n   * Regardless of the value of `strictTableNameTypes`, your schema will only\n   * validate documents in the tables listed in the schema. You can still create\n   * and modify other tables on the dashboard or in JavaScript mutations.\n   *\n   * By default, `strictTableNameTypes` is `true`.\n   */\n  strictTableNameTypes?: StrictTableNameTypes;\n}\n\n/**\n * Define the schema of this Convex project.\n *\n * This should be exported from a `schema.ts` file in your `convex/` directory\n * like:\n *\n * ```ts\n * export default defineSchema({\n *   ...\n * });\n * ```\n *\n * @param schema - A map from table name to {@link TableDefinition} for all of\n * the tables in this project.\n * @param options - Optional configuration. See {@link DefineSchemaOptions} for\n * a full description.\n * @returns The schema.\n *\n * @public\n */\nexport function defineSchema<\n  Schema extends GenericSchema,\n  StrictTableNameTypes extends boolean = true,\n>(\n  schema: Schema,\n  options?: DefineSchemaOptions<StrictTableNameTypes>,\n): SchemaDefinition<Schema, StrictTableNameTypes> {\n  return new SchemaDefinition(schema, options);\n}\n\n/**\n * Internal type used in Convex code generation!\n *\n * Convert a {@link SchemaDefinition} into a {@link server.GenericDataModel}.\n *\n * @public\n */\nexport type DataModelFromSchemaDefinition<\n  SchemaDef extends SchemaDefinition<any, boolean>,\n> = MaybeMakeLooseDataModel<\n  {\n    [TableName in keyof SchemaDef[\"tables\"] &\n      string]: SchemaDef[\"tables\"][TableName] extends TableDefinition<\n      infer DocumentType,\n      infer Indexes,\n      infer SearchIndexes,\n      infer VectorIndexes\n    >\n      ? {\n          // We've already added all of the system fields except for `_id`.\n          // Add that here.\n          document: Expand<IdField<TableName> & ExtractDocument<DocumentType>>;\n          fieldPaths:\n            | keyof IdField<TableName>\n            | ExtractFieldPaths<DocumentType>;\n          indexes: Expand<Indexes & SystemIndexes>;\n          searchIndexes: SearchIndexes;\n          vectorIndexes: VectorIndexes;\n        }\n      : never;\n  },\n  SchemaDef[\"strictTableNameTypes\"]\n>;\n\ntype MaybeMakeLooseDataModel<\n  DataModel extends GenericDataModel,\n  StrictTableNameTypes extends boolean,\n> = StrictTableNameTypes extends true\n  ? DataModel\n  : Expand<DataModel & AnyDataModel>;\n\nconst _systemSchema = defineSchema({\n  _scheduled_functions: defineTable({\n    name: v.string(),\n    args: v.array(v.any()),\n    scheduledTime: v.float64(),\n    completedTime: v.optional(v.float64()),\n    state: v.union(\n      v.object({ kind: v.literal(\"pending\") }),\n      v.object({ kind: v.literal(\"inProgress\") }),\n      v.object({ kind: v.literal(\"success\") }),\n      v.object({ kind: v.literal(\"failed\"), error: v.string() }),\n      v.object({ kind: v.literal(\"canceled\") }),\n    ),\n  }),\n  _storage: defineTable({\n    sha256: v.string(),\n    size: v.float64(),\n    contentType: v.optional(v.string()),\n  }),\n});\n\nexport interface SystemDataModel\n  extends DataModelFromSchemaDefinition<typeof _systemSchema> {}\n\nexport type SystemTableNames = TableNamesInDataModel<SystemDataModel>;\n"], "mappings": ";;;;AA4CA;AAAA,EAGE;AAAA,EACA;AAAA,OACK;AA0GA,aAAM,gBAKX;AAAA;AAAA;AAAA;AAAA,EAUA,YAAY,cAA4B;AATxC,wBAAQ;AACR,wBAAQ;AACR,wBAAQ;AAER;AAAA;AAME,SAAK,UAAU,CAAC;AAChB,SAAK,gBAAgB,CAAC;AACtB,SAAK,gBAAgB,CAAC;AACtB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,aAA8D;AAC5D,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAKE,MACA,QAcA;AACA,SAAK,QAAQ,KAAK,EAAE,iBAAiB,MAAM,OAAO,CAAC;AACnD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,YAKE,MACA,aAiBA;AACA,SAAK,cAAc,KAAK;AAAA,MACtB,iBAAiB;AAAA,MACjB,aAAa,YAAY;AAAA,MACzB,cAAc,YAAY,gBAAgB,CAAC;AAAA,IAC7C,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,YAKE,MACA,aAgBA;AACA,SAAK,cAAc,KAAK;AAAA,MACtB,iBAAiB;AAAA,MACjB,aAAa,YAAY;AAAA,MACzB,YAAY,YAAY;AAAA,MACxB,cAAc,YAAY,gBAAgB,CAAC;AAAA,IAC7C,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKU,OAKR;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS;AACP,UAAM,eAAe,KAAK,UAAU;AACpC,QAAI,OAAO,iBAAiB,UAAU;AACpC,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,MACL,SAAS,KAAK;AAAA,MACd,eAAe,KAAK;AAAA,MACpB,eAAe,KAAK;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AACF;AA4DO,gBAAS,YAId,gBAAgE;AAChE,MAAI,YAAY,cAAc,GAAG;AAC/B,WAAO,IAAI,gBAAgB,cAAc;AAAA,EAC3C,OAAO;AACL,WAAO,IAAI,gBAAgB,EAAE,OAAO,cAAc,CAAC;AAAA,EACrD;AACF;AAkBO,aAAM,iBAGX;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,QAAgB,SAAiD;AAP7E,wBAAO;AACP,wBAAO;AACP,wBAAiB;AAMf,SAAK,SAAS;AACd,SAAK,mBACH,SAAS,qBAAqB,SAAY,OAAO,QAAQ;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAiB;AACf,WAAO,KAAK,UAAU;AAAA,MACpB,QAAQ,OAAO,QAAQ,KAAK,MAAM,EAAE,IAAI,CAAC,CAAC,WAAW,UAAU,MAAM;AACnE,cAAM,EAAE,SAAS,eAAe,eAAe,aAAa,IAC1D,WAAW,OAAO;AACpB,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF,CAAC;AAAA,MACD,kBAAkB,KAAK;AAAA,IACzB,CAAC;AAAA,EACH;AACF;AAkEO,gBAAS,aAId,QACA,SACgD;AAChD,SAAO,IAAI,iBAAiB,QAAQ,OAAO;AAC7C;AA2CA,MAAM,gBAAgB,aAAa;AAAA,EACjC,sBAAsB,YAAY;AAAA,IAChC,MAAM,EAAE,OAAO;AAAA,IACf,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC;AAAA,IACrB,eAAe,EAAE,QAAQ;AAAA,IACzB,eAAe,EAAE,SAAS,EAAE,QAAQ,CAAC;AAAA,IACrC,OAAO,EAAE;AAAA,MACP,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,SAAS,EAAE,CAAC;AAAA,MACvC,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,YAAY,EAAE,CAAC;AAAA,MAC1C,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,SAAS,EAAE,CAAC;AAAA,MACvC,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,QAAQ,GAAG,OAAO,EAAE,OAAO,EAAE,CAAC;AAAA,MACzD,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,UAAU,EAAE,CAAC;AAAA,IAC1C;AAAA,EACF,CAAC;AAAA,EACD,UAAU,YAAY;AAAA,IACpB,QAAQ,EAAE,OAAO;AAAA,IACjB,MAAM,EAAE,QAAQ;AAAA,IAChB,aAAa,EAAE,SAAS,EAAE,OAAO,CAAC;AAAA,EACpC,CAAC;AACH,CAAC;", "names": []}
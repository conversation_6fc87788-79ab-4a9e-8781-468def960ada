{"version": 3, "file": "client_node_test_helpers.d.ts", "sourceRoot": "", "sources": ["../../../../src/browser/sync/client_node_test_helpers.ts"], "names": [], "mappings": "AAIA,OAAO,SAA8B,MAAM,IAAI,CAAC;AAGhD,eAAO,MAAM,aAAa;;;;;;;CAAkD,CAAC;AAE7E,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,MAAM,eAAe,CAAC;AAC7D,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AACjD,OAAO,EAAE,gBAAgB,EAAE,MAAM,aAAa,CAAC;AAE/C,MAAM,MAAM,qBAAqB,GAAG,CAAC,IAAI,EAAE;IACzC,OAAO,EAAE,MAAM,CAAC;IAChB,MAAM,EAAE,MAAM,SAAS,CAAC;IACxB,OAAO,EAAE,MAAM,OAAO,CAAC,aAAa,CAAC,CAAC;IACtC,IAAI,EAAE,CAAC,OAAO,EAAE,aAAa,KAAK,IAAI,CAAC;IACvC,KAAK,EAAE,MAAM,IAAI,CAAC;CACnB,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;AAUpB,wBAAsB,qBAAqB,CACzC,EAAE,EAAE,qBAAqB,EACzB,KAAK,UAAQ,iBA6Dd;AAED,wBAAgB,mBAAmB,CAAC,OAAO,EAAE,aAAa,GAAG,MAAM,CAQlE;AAOD;;;;;;GAMG;AACH,qBAAa,WAAW;IACtB,cAAc,EAAE,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC;IACzD,cAAc,EAAE,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;IACnD,OAAO,EAAE,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC,EAAE,CAAC;IACnC,UAAU,EAAE,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;IACpC,SAAS,EAAE,MAAM,CAAC;gBAEN,SAAS,SAAK;IAmB1B,YAAY,WACD,gBAAgB,0BAA0B,UAAU,EAAE,UAU7D;CACL"}
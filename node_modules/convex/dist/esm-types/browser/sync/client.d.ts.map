{"version": 3, "file": "client.d.ts", "sourceRoot": "", "sources": ["../../../../src/browser/sync/client.ts"], "names": [], "mappings": "AACA,OAAO,EAAgB,KAAK,EAAE,MAAM,uBAAuB,CAAC;AAC5D,OAAO,EAML,MAAM,EACP,MAAM,eAAe,CAAC;AAGvB,OAAO,EAEL,gBAAgB,EACjB,MAAM,yBAAyB,CAAC;AAKjC,OAAO,EAIL,YAAY,EACZ,SAAS,EAET,EAAE,EAEH,MAAM,eAAe,CAAC;AAEvB,OAAO,EAAE,UAAU,EAAwB,MAAM,qBAAqB,CAAC;AAGvE,OAAO,EAAE,cAAc,EAAE,MAAM,sBAAsB,CAAC;AACtD,OAAO,EAEL,gBAAgB,EACjB,MAAM,6BAA6B,CAAC;AACrC,OAAO,EAAE,KAAK,gBAAgB,EAAE,MAAM,6BAA6B,CAAC;AAKpE;;;;GAIG;AACH,MAAM,WAAW,uBAAuB;IACtC;;;;;;;OAOG;IACH,qBAAqB,CAAC,EAAE,OAAO,CAAC;IAChC;;;;;OAKG;IACH,oBAAoB,CAAC,EAAE,OAAO,SAAS,CAAC;IACxC;;;;OAIG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB;;;;;OAKG;IACH,MAAM,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC;IAC1B;;;;OAIG;IACH,uBAAuB,CAAC,EAAE,OAAO,CAAC;IAClC;;;;;;;;;;;;OAYG;IACH,uBAAuB,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,KAAK,IAAI,CAAC;IACpD;;;;;;;;OAQG;IACH,4BAA4B,CAAC,EAAE,OAAO,CAAC;IACvC;;;;OAIG;IACH,6BAA6B,CAAC,EAAE,MAAM,CAAC;CACxC;AAED;;;;GAIG;AACH,MAAM,MAAM,eAAe,GAAG;IAC5B,mBAAmB,EAAE,OAAO,CAAC;IAC7B,oBAAoB,EAAE,OAAO,CAAC;IAC9B,2BAA2B,EAAE,IAAI,GAAG,IAAI,CAAC;IACzC;;OAEG;IACH,gBAAgB,EAAE,OAAO,CAAC;IAC1B;;;;;;OAMG;IACH,eAAe,EAAE,MAAM,CAAC;IACxB;;OAEG;IACH,iBAAiB,EAAE,MAAM,CAAC;IAC1B;;OAEG;IACH,iBAAiB,EAAE,MAAM,CAAC;IAC1B;;OAEG;IACH,eAAe,EAAE,MAAM,CAAC;CACzB,CAAC;AAEF;;;;GAIG;AACH,MAAM,WAAW,gBAAgB;IAC/B;;;;;;OAMG;IACH,OAAO,CAAC,EAAE,YAAY,CAAC;CAMxB;AAED;;;;GAIG;AACH,MAAM,WAAW,eAAe;IAC9B;;;;;OAKG;IACH,gBAAgB,CAAC,EAAE,gBAAgB,CAAC,GAAG,CAAC,CAAC;CAC1C;AAED;;;;GAIG;AACH,MAAM,MAAM,iBAAiB,GAE3B;IAAE,IAAI,EAAE,SAAS,CAAC;IAAC,MAAM,EAAE,cAAc,GAAG,SAAS,CAAA;CAAE,GAAG;IAAE,IAAI,EAAE,SAAS,CAAA;CAAE,CAAC;AAEhF;;;;;;;GAOG;AACH,MAAM,MAAM,UAAU,GAAG;IACvB,OAAO,EAAE,KAAK,CAAC;QAAE,KAAK,EAAE,UAAU,CAAC;QAAC,YAAY,EAAE,iBAAiB,CAAA;KAAE,CAAC,CAAC;IACvE,kBAAkB,EAAE,KAAK,CAAC;QAAE,SAAS,EAAE,SAAS,CAAC;QAAC,MAAM,EAAE,cAAc,CAAA;KAAE,CAAC,CAAC;IAC5E,SAAS,EAAE,EAAE,CAAC;CACf,CAAC;AAEF;;;;;;;;GAQG;AACH,qBAAa,gBAAgB;IAC3B,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAS;IACjC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAiB;IACvC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAiB;IAChD,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAmB;IACpD,OAAO,CAAC,QAAQ,CAAC,qBAAqB,CAAwB;IAC9D,OAAO,CAAC,cAAc,CAAiB;IACvC,OAAO,CAAC,QAAQ,CAAC,sBAAsB,CAAyB;IAChE,OAAO,CAAC,yBAAyB,CAAK;IACtC,OAAO,CAAC,cAAc,CAAY;IAClC,OAAO,CAAC,gBAAgB,CACZ;IACZ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAS;IACpC,OAAO,CAAC,oBAAoB,CAAS;IACrC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAU;IAChC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAS;IAChC,OAAO,CAAC,oBAAoB,CAAiB;IAE7C;;;;;;;OAOG;gBAED,OAAO,EAAE,MAAM,EACf,YAAY,EAAE,CAAC,cAAc,EAAE,UAAU,EAAE,KAAK,IAAI,EACpD,OAAO,CAAC,EAAE,uBAAuB;IAiOnC;;;;OAIG;IACH,OAAO,CAAC,0BAA0B;IAOlC,OAAO,CAAC,iBAAiB;IASzB,uBAAuB;IAIvB;;;;;;;OAOG;IACH,OAAO,CAAC,0BAA0B;IAoDlC,OAAO,CAAC,gBAAgB;IAMxB;;;;;;;;OAQG;IACH,sBAAsB,CAAC,EAAE,EAAE,CAAC,UAAU,EAAE,UAAU,KAAK,IAAI;IAM3D;;;;;;;OAOG;IACH,OAAO,CACL,UAAU,EAAE,gBAAgB,EAC5B,QAAQ,EAAE,CAAC,eAAe,EAAE,OAAO,KAAK,IAAI;IAK9C,OAAO;IAUP,SAAS;IAKT;;;;;;;;;;;;;OAaG;IACH,SAAS,CACP,IAAI,EAAE,MAAM,EACZ,IAAI,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,EAC5B,OAAO,CAAC,EAAE,gBAAgB,GACzB;QAAE,UAAU,EAAE,UAAU,CAAC;QAAC,WAAW,EAAE,MAAM,IAAI,CAAA;KAAE;IAuBtD;;;;;OAKG;IACH,gBAAgB,CACd,OAAO,EAAE,MAAM,EACf,IAAI,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,GAC3B,KAAK,GAAG,SAAS;IAyCpB;;;;;;;;OAQG;IACH,YAAY,CACV,IAAI,EAAE,MAAM,EACZ,IAAI,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,GAC3B,YAAY,GAAG,SAAS;IAM3B;;;;;OAKG;IACH,eAAe,IAAI,eAAe;IAelC;;;;;;;;;OASG;IACG,QAAQ,CACZ,IAAI,EAAE,MAAM,EACZ,IAAI,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,EAC5B,OAAO,CAAC,EAAE,eAAe,GACxB,OAAO,CAAC,GAAG,CAAC;IA6Gf;;;;;;;OAOG;IACG,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC;IAuCtE;;;;;;;OAOG;IACG,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAK5B;;;;;OAKG;IACH,IAAI,GAAG,WAEN;IAiBD,OAAO,CAAC,IAAI,CAIV;IAEF;;;OAGG;IACH,OAAO,CAAC,WAAW;IAWnB,OAAO,CAAC,uBAAuB;CAiChC"}
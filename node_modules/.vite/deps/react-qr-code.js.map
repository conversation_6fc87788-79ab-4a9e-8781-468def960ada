{"version": 3, "sources": ["../../react-is/cjs/react-is.development.js", "../../react-is/index.js", "../../object-assign/index.js", "../../prop-types/lib/ReactPropTypesSecret.js", "../../prop-types/lib/has.js", "../../prop-types/checkPropTypes.js", "../../prop-types/factoryWithTypeCheckers.js", "../../prop-types/index.js", "../../qr.js/lib/ErrorCorrectLevel.js", "../../qr.js/lib/mode.js", "../../qr.js/lib/8BitByte.js", "../../qr.js/lib/RSBlock.js", "../../qr.js/lib/BitBuffer.js", "../../qr.js/lib/math.js", "../../qr.js/lib/Polynomial.js", "../../qr.js/lib/util.js", "../../qr.js/lib/QRCode.js", "../../react-qr-code/lib/QRCodeSvg/index.js", "../../react-qr-code/lib/index.js"], "sourcesContent": ["/** @license React v16.13.1\n * react-is.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\n\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n'use strict';\n\n// The Symbol used to tag the ReactElement-like types. If there is no native Symbol\n// nor polyfill, then a plain number is used for performance.\nvar hasSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = hasSymbol ? Symbol.for('react.element') : 0xeac7;\nvar REACT_PORTAL_TYPE = hasSymbol ? Symbol.for('react.portal') : 0xeaca;\nvar REACT_FRAGMENT_TYPE = hasSymbol ? Symbol.for('react.fragment') : 0xeacb;\nvar REACT_STRICT_MODE_TYPE = hasSymbol ? Symbol.for('react.strict_mode') : 0xeacc;\nvar REACT_PROFILER_TYPE = hasSymbol ? Symbol.for('react.profiler') : 0xead2;\nvar REACT_PROVIDER_TYPE = hasSymbol ? Symbol.for('react.provider') : 0xeacd;\nvar REACT_CONTEXT_TYPE = hasSymbol ? Symbol.for('react.context') : 0xeace; // TODO: We don't use AsyncMode or ConcurrentMode anymore. They were temporary\n// (unstable) APIs that have been removed. Can we remove the symbols?\n\nvar REACT_ASYNC_MODE_TYPE = hasSymbol ? Symbol.for('react.async_mode') : 0xeacf;\nvar REACT_CONCURRENT_MODE_TYPE = hasSymbol ? Symbol.for('react.concurrent_mode') : 0xeacf;\nvar REACT_FORWARD_REF_TYPE = hasSymbol ? Symbol.for('react.forward_ref') : 0xead0;\nvar REACT_SUSPENSE_TYPE = hasSymbol ? Symbol.for('react.suspense') : 0xead1;\nvar REACT_SUSPENSE_LIST_TYPE = hasSymbol ? Symbol.for('react.suspense_list') : 0xead8;\nvar REACT_MEMO_TYPE = hasSymbol ? Symbol.for('react.memo') : 0xead3;\nvar REACT_LAZY_TYPE = hasSymbol ? Symbol.for('react.lazy') : 0xead4;\nvar REACT_BLOCK_TYPE = hasSymbol ? Symbol.for('react.block') : 0xead9;\nvar REACT_FUNDAMENTAL_TYPE = hasSymbol ? Symbol.for('react.fundamental') : 0xead5;\nvar REACT_RESPONDER_TYPE = hasSymbol ? Symbol.for('react.responder') : 0xead6;\nvar REACT_SCOPE_TYPE = hasSymbol ? Symbol.for('react.scope') : 0xead7;\n\nfunction isValidElementType(type) {\n  return typeof type === 'string' || typeof type === 'function' || // Note: its typeof might be other than 'symbol' or 'number' if it's a polyfill.\n  type === REACT_FRAGMENT_TYPE || type === REACT_CONCURRENT_MODE_TYPE || type === REACT_PROFILER_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || typeof type === 'object' && type !== null && (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_FUNDAMENTAL_TYPE || type.$$typeof === REACT_RESPONDER_TYPE || type.$$typeof === REACT_SCOPE_TYPE || type.$$typeof === REACT_BLOCK_TYPE);\n}\n\nfunction typeOf(object) {\n  if (typeof object === 'object' && object !== null) {\n    var $$typeof = object.$$typeof;\n\n    switch ($$typeof) {\n      case REACT_ELEMENT_TYPE:\n        var type = object.type;\n\n        switch (type) {\n          case REACT_ASYNC_MODE_TYPE:\n          case REACT_CONCURRENT_MODE_TYPE:\n          case REACT_FRAGMENT_TYPE:\n          case REACT_PROFILER_TYPE:\n          case REACT_STRICT_MODE_TYPE:\n          case REACT_SUSPENSE_TYPE:\n            return type;\n\n          default:\n            var $$typeofType = type && type.$$typeof;\n\n            switch ($$typeofType) {\n              case REACT_CONTEXT_TYPE:\n              case REACT_FORWARD_REF_TYPE:\n              case REACT_LAZY_TYPE:\n              case REACT_MEMO_TYPE:\n              case REACT_PROVIDER_TYPE:\n                return $$typeofType;\n\n              default:\n                return $$typeof;\n            }\n\n        }\n\n      case REACT_PORTAL_TYPE:\n        return $$typeof;\n    }\n  }\n\n  return undefined;\n} // AsyncMode is deprecated along with isAsyncMode\n\nvar AsyncMode = REACT_ASYNC_MODE_TYPE;\nvar ConcurrentMode = REACT_CONCURRENT_MODE_TYPE;\nvar ContextConsumer = REACT_CONTEXT_TYPE;\nvar ContextProvider = REACT_PROVIDER_TYPE;\nvar Element = REACT_ELEMENT_TYPE;\nvar ForwardRef = REACT_FORWARD_REF_TYPE;\nvar Fragment = REACT_FRAGMENT_TYPE;\nvar Lazy = REACT_LAZY_TYPE;\nvar Memo = REACT_MEMO_TYPE;\nvar Portal = REACT_PORTAL_TYPE;\nvar Profiler = REACT_PROFILER_TYPE;\nvar StrictMode = REACT_STRICT_MODE_TYPE;\nvar Suspense = REACT_SUSPENSE_TYPE;\nvar hasWarnedAboutDeprecatedIsAsyncMode = false; // AsyncMode should be deprecated\n\nfunction isAsyncMode(object) {\n  {\n    if (!hasWarnedAboutDeprecatedIsAsyncMode) {\n      hasWarnedAboutDeprecatedIsAsyncMode = true; // Using console['warn'] to evade Babel and ESLint\n\n      console['warn']('The ReactIs.isAsyncMode() alias has been deprecated, ' + 'and will be removed in React 17+. Update your code to use ' + 'ReactIs.isConcurrentMode() instead. It has the exact same API.');\n    }\n  }\n\n  return isConcurrentMode(object) || typeOf(object) === REACT_ASYNC_MODE_TYPE;\n}\nfunction isConcurrentMode(object) {\n  return typeOf(object) === REACT_CONCURRENT_MODE_TYPE;\n}\nfunction isContextConsumer(object) {\n  return typeOf(object) === REACT_CONTEXT_TYPE;\n}\nfunction isContextProvider(object) {\n  return typeOf(object) === REACT_PROVIDER_TYPE;\n}\nfunction isElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\nfunction isForwardRef(object) {\n  return typeOf(object) === REACT_FORWARD_REF_TYPE;\n}\nfunction isFragment(object) {\n  return typeOf(object) === REACT_FRAGMENT_TYPE;\n}\nfunction isLazy(object) {\n  return typeOf(object) === REACT_LAZY_TYPE;\n}\nfunction isMemo(object) {\n  return typeOf(object) === REACT_MEMO_TYPE;\n}\nfunction isPortal(object) {\n  return typeOf(object) === REACT_PORTAL_TYPE;\n}\nfunction isProfiler(object) {\n  return typeOf(object) === REACT_PROFILER_TYPE;\n}\nfunction isStrictMode(object) {\n  return typeOf(object) === REACT_STRICT_MODE_TYPE;\n}\nfunction isSuspense(object) {\n  return typeOf(object) === REACT_SUSPENSE_TYPE;\n}\n\nexports.AsyncMode = AsyncMode;\nexports.ConcurrentMode = ConcurrentMode;\nexports.ContextConsumer = ContextConsumer;\nexports.ContextProvider = ContextProvider;\nexports.Element = Element;\nexports.ForwardRef = ForwardRef;\nexports.Fragment = Fragment;\nexports.Lazy = Lazy;\nexports.Memo = Memo;\nexports.Portal = Portal;\nexports.Profiler = Profiler;\nexports.StrictMode = StrictMode;\nexports.Suspense = Suspense;\nexports.isAsyncMode = isAsyncMode;\nexports.isConcurrentMode = isConcurrentMode;\nexports.isContextConsumer = isContextConsumer;\nexports.isContextProvider = isContextProvider;\nexports.isElement = isElement;\nexports.isForwardRef = isForwardRef;\nexports.isFragment = isFragment;\nexports.isLazy = isLazy;\nexports.isMemo = isMemo;\nexports.isPortal = isPortal;\nexports.isProfiler = isProfiler;\nexports.isStrictMode = isStrictMode;\nexports.isSuspense = isSuspense;\nexports.isValidElementType = isValidElementType;\nexports.typeOf = typeOf;\n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-is.production.min.js');\n} else {\n  module.exports = require('./cjs/react-is.development.js');\n}\n", "/*\nobject-assign\n(c) <PERSON><PERSON> Sorhus\n@license MIT\n*/\n\n'use strict';\n/* eslint-disable no-unused-vars */\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar propIsEnumerable = Object.prototype.propertyIsEnumerable;\n\nfunction toObject(val) {\n\tif (val === null || val === undefined) {\n\t\tthrow new TypeError('Object.assign cannot be called with null or undefined');\n\t}\n\n\treturn Object(val);\n}\n\nfunction shouldUseNative() {\n\ttry {\n\t\tif (!Object.assign) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// Detect buggy property enumeration order in older V8 versions.\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=4118\n\t\tvar test1 = new String('abc');  // eslint-disable-line no-new-wrappers\n\t\ttest1[5] = 'de';\n\t\tif (Object.getOwnPropertyNames(test1)[0] === '5') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test2 = {};\n\t\tfor (var i = 0; i < 10; i++) {\n\t\t\ttest2['_' + String.fromCharCode(i)] = i;\n\t\t}\n\t\tvar order2 = Object.getOwnPropertyNames(test2).map(function (n) {\n\t\t\treturn test2[n];\n\t\t});\n\t\tif (order2.join('') !== '0123456789') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test3 = {};\n\t\t'abcdefghijklmnopqrst'.split('').forEach(function (letter) {\n\t\t\ttest3[letter] = letter;\n\t\t});\n\t\tif (Object.keys(Object.assign({}, test3)).join('') !==\n\t\t\t\t'abcdefghijklmnopqrst') {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t} catch (err) {\n\t\t// We don't expect any of the above to throw, but better to be safe.\n\t\treturn false;\n\t}\n}\n\nmodule.exports = shouldUseNative() ? Object.assign : function (target, source) {\n\tvar from;\n\tvar to = toObject(target);\n\tvar symbols;\n\n\tfor (var s = 1; s < arguments.length; s++) {\n\t\tfrom = Object(arguments[s]);\n\n\t\tfor (var key in from) {\n\t\t\tif (hasOwnProperty.call(from, key)) {\n\t\t\t\tto[key] = from[key];\n\t\t\t}\n\t\t}\n\n\t\tif (getOwnPropertySymbols) {\n\t\t\tsymbols = getOwnPropertySymbols(from);\n\t\t\tfor (var i = 0; i < symbols.length; i++) {\n\t\t\t\tif (propIsEnumerable.call(from, symbols[i])) {\n\t\t\t\t\tto[symbols[i]] = from[symbols[i]];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn to;\n};\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\n\nmodule.exports = ReactPropTypesSecret;\n", "module.exports = Function.call.bind(Object.prototype.hasOwnProperty);\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar printWarning = function() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\n  var loggedTypeFailures = {};\n  var has = require('./lib/has');\n\n  printWarning = function(text) {\n    var message = 'Warning: ' + text;\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) { /**/ }\n  };\n}\n\n/**\n * Assert that the values match with the type specs.\n * Error messages are memorized and will only be shown once.\n *\n * @param {object} typeSpecs Map of name to a ReactPropType\n * @param {object} values Runtime values that need to be type-checked\n * @param {string} location e.g. \"prop\", \"context\", \"child context\"\n * @param {string} componentName Name of the component for error messages.\n * @param {?Function} getStack Returns the component stack.\n * @private\n */\nfunction checkPropTypes(typeSpecs, values, location, componentName, getStack) {\n  if (process.env.NODE_ENV !== 'production') {\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error;\n        // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            var err = Error(\n              (componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' +\n              'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' +\n              'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.'\n            );\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n          error = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, ReactPropTypesSecret);\n        } catch (ex) {\n          error = ex;\n        }\n        if (error && !(error instanceof Error)) {\n          printWarning(\n            (componentName || 'React class') + ': type specification of ' +\n            location + ' `' + typeSpecName + '` is invalid; the type checker ' +\n            'function must return `null` or an `Error` but returned a ' + typeof error + '. ' +\n            'You may have forgotten to pass an argument to the type checker ' +\n            'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' +\n            'shape all require an argument).'\n          );\n        }\n        if (error instanceof Error && !(error.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error.message] = true;\n\n          var stack = getStack ? getStack() : '';\n\n          printWarning(\n            'Failed ' + location + ' type: ' + error.message + (stack != null ? stack : '')\n          );\n        }\n      }\n    }\n  }\n}\n\n/**\n * Resets warning cache when testing.\n *\n * @private\n */\ncheckPropTypes.resetWarningCache = function() {\n  if (process.env.NODE_ENV !== 'production') {\n    loggedTypeFailures = {};\n  }\n}\n\nmodule.exports = checkPropTypes;\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactIs = require('react-is');\nvar assign = require('object-assign');\n\nvar ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\nvar has = require('./lib/has');\nvar checkPropTypes = require('./checkPropTypes');\n\nvar printWarning = function() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  printWarning = function(text) {\n    var message = 'Warning: ' + text;\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) {}\n  };\n}\n\nfunction emptyFunctionThatReturnsNull() {\n  return null;\n}\n\nmodule.exports = function(isValidElement, throwOnDirectAccess) {\n  /* global Symbol */\n  var ITERATOR_SYMBOL = typeof Symbol === 'function' && Symbol.iterator;\n  var FAUX_ITERATOR_SYMBOL = '@@iterator'; // Before Symbol spec.\n\n  /**\n   * Returns the iterator method function contained on the iterable object.\n   *\n   * Be sure to invoke the function with the iterable as context:\n   *\n   *     var iteratorFn = getIteratorFn(myIterable);\n   *     if (iteratorFn) {\n   *       var iterator = iteratorFn.call(myIterable);\n   *       ...\n   *     }\n   *\n   * @param {?object} maybeIterable\n   * @return {?function}\n   */\n  function getIteratorFn(maybeIterable) {\n    var iteratorFn = maybeIterable && (ITERATOR_SYMBOL && maybeIterable[ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL]);\n    if (typeof iteratorFn === 'function') {\n      return iteratorFn;\n    }\n  }\n\n  /**\n   * Collection of methods that allow declaration and validation of props that are\n   * supplied to React components. Example usage:\n   *\n   *   var Props = require('ReactPropTypes');\n   *   var MyArticle = React.createClass({\n   *     propTypes: {\n   *       // An optional string prop named \"description\".\n   *       description: Props.string,\n   *\n   *       // A required enum prop named \"category\".\n   *       category: Props.oneOf(['News','Photos']).isRequired,\n   *\n   *       // A prop named \"dialog\" that requires an instance of Dialog.\n   *       dialog: Props.instanceOf(Dialog).isRequired\n   *     },\n   *     render: function() { ... }\n   *   });\n   *\n   * A more formal specification of how these methods are used:\n   *\n   *   type := array|bool|func|object|number|string|oneOf([...])|instanceOf(...)\n   *   decl := ReactPropTypes.{type}(.isRequired)?\n   *\n   * Each and every declaration produces a function with the same signature. This\n   * allows the creation of custom validation functions. For example:\n   *\n   *  var MyLink = React.createClass({\n   *    propTypes: {\n   *      // An optional string or URI prop named \"href\".\n   *      href: function(props, propName, componentName) {\n   *        var propValue = props[propName];\n   *        if (propValue != null && typeof propValue !== 'string' &&\n   *            !(propValue instanceof URI)) {\n   *          return new Error(\n   *            'Expected a string or an URI for ' + propName + ' in ' +\n   *            componentName\n   *          );\n   *        }\n   *      }\n   *    },\n   *    render: function() {...}\n   *  });\n   *\n   * @internal\n   */\n\n  var ANONYMOUS = '<<anonymous>>';\n\n  // Important!\n  // Keep this list in sync with production version in `./factoryWithThrowingShims.js`.\n  var ReactPropTypes = {\n    array: createPrimitiveTypeChecker('array'),\n    bigint: createPrimitiveTypeChecker('bigint'),\n    bool: createPrimitiveTypeChecker('boolean'),\n    func: createPrimitiveTypeChecker('function'),\n    number: createPrimitiveTypeChecker('number'),\n    object: createPrimitiveTypeChecker('object'),\n    string: createPrimitiveTypeChecker('string'),\n    symbol: createPrimitiveTypeChecker('symbol'),\n\n    any: createAnyTypeChecker(),\n    arrayOf: createArrayOfTypeChecker,\n    element: createElementTypeChecker(),\n    elementType: createElementTypeTypeChecker(),\n    instanceOf: createInstanceTypeChecker,\n    node: createNodeChecker(),\n    objectOf: createObjectOfTypeChecker,\n    oneOf: createEnumTypeChecker,\n    oneOfType: createUnionTypeChecker,\n    shape: createShapeTypeChecker,\n    exact: createStrictShapeTypeChecker,\n  };\n\n  /**\n   * inlined Object.is polyfill to avoid requiring consumers ship their own\n   * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n   */\n  /*eslint-disable no-self-compare*/\n  function is(x, y) {\n    // SameValue algorithm\n    if (x === y) {\n      // Steps 1-5, 7-10\n      // Steps 6.b-6.e: +0 != -0\n      return x !== 0 || 1 / x === 1 / y;\n    } else {\n      // Step 6.a: NaN == NaN\n      return x !== x && y !== y;\n    }\n  }\n  /*eslint-enable no-self-compare*/\n\n  /**\n   * We use an Error-like object for backward compatibility as people may call\n   * PropTypes directly and inspect their output. However, we don't use real\n   * Errors anymore. We don't inspect their stack anyway, and creating them\n   * is prohibitively expensive if they are created too often, such as what\n   * happens in oneOfType() for any type before the one that matched.\n   */\n  function PropTypeError(message, data) {\n    this.message = message;\n    this.data = data && typeof data === 'object' ? data: {};\n    this.stack = '';\n  }\n  // Make `instanceof Error` still work for returned errors.\n  PropTypeError.prototype = Error.prototype;\n\n  function createChainableTypeChecker(validate) {\n    if (process.env.NODE_ENV !== 'production') {\n      var manualPropTypeCallCache = {};\n      var manualPropTypeWarningCount = 0;\n    }\n    function checkType(isRequired, props, propName, componentName, location, propFullName, secret) {\n      componentName = componentName || ANONYMOUS;\n      propFullName = propFullName || propName;\n\n      if (secret !== ReactPropTypesSecret) {\n        if (throwOnDirectAccess) {\n          // New behavior only for users of `prop-types` package\n          var err = new Error(\n            'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +\n            'Use `PropTypes.checkPropTypes()` to call them. ' +\n            'Read more at http://fb.me/use-check-prop-types'\n          );\n          err.name = 'Invariant Violation';\n          throw err;\n        } else if (process.env.NODE_ENV !== 'production' && typeof console !== 'undefined') {\n          // Old behavior for people using React.PropTypes\n          var cacheKey = componentName + ':' + propName;\n          if (\n            !manualPropTypeCallCache[cacheKey] &&\n            // Avoid spamming the console because they are often not actionable except for lib authors\n            manualPropTypeWarningCount < 3\n          ) {\n            printWarning(\n              'You are manually calling a React.PropTypes validation ' +\n              'function for the `' + propFullName + '` prop on `' + componentName + '`. This is deprecated ' +\n              'and will throw in the standalone `prop-types` package. ' +\n              'You may be seeing this warning due to a third-party PropTypes ' +\n              'library. See https://fb.me/react-warning-dont-call-proptypes ' + 'for details.'\n            );\n            manualPropTypeCallCache[cacheKey] = true;\n            manualPropTypeWarningCount++;\n          }\n        }\n      }\n      if (props[propName] == null) {\n        if (isRequired) {\n          if (props[propName] === null) {\n            return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required ' + ('in `' + componentName + '`, but its value is `null`.'));\n          }\n          return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required in ' + ('`' + componentName + '`, but its value is `undefined`.'));\n        }\n        return null;\n      } else {\n        return validate(props, propName, componentName, location, propFullName);\n      }\n    }\n\n    var chainedCheckType = checkType.bind(null, false);\n    chainedCheckType.isRequired = checkType.bind(null, true);\n\n    return chainedCheckType;\n  }\n\n  function createPrimitiveTypeChecker(expectedType) {\n    function validate(props, propName, componentName, location, propFullName, secret) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== expectedType) {\n        // `propValue` being instance of, say, date/regexp, pass the 'object'\n        // check, but we can offer a more precise error message here rather than\n        // 'of type `object`'.\n        var preciseType = getPreciseType(propValue);\n\n        return new PropTypeError(\n          'Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + preciseType + '` supplied to `' + componentName + '`, expected ') + ('`' + expectedType + '`.'),\n          {expectedType: expectedType}\n        );\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createAnyTypeChecker() {\n    return createChainableTypeChecker(emptyFunctionThatReturnsNull);\n  }\n\n  function createArrayOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside arrayOf.');\n      }\n      var propValue = props[propName];\n      if (!Array.isArray(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an array.'));\n      }\n      for (var i = 0; i < propValue.length; i++) {\n        var error = typeChecker(propValue, i, componentName, location, propFullName + '[' + i + ']', ReactPropTypesSecret);\n        if (error instanceof Error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!isValidElement(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!ReactIs.isValidElementType(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement type.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createInstanceTypeChecker(expectedClass) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!(props[propName] instanceof expectedClass)) {\n        var expectedClassName = expectedClass.name || ANONYMOUS;\n        var actualClassName = getClassName(props[propName]);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + actualClassName + '` supplied to `' + componentName + '`, expected ') + ('instance of `' + expectedClassName + '`.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createEnumTypeChecker(expectedValues) {\n    if (!Array.isArray(expectedValues)) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (arguments.length > 1) {\n          printWarning(\n            'Invalid arguments supplied to oneOf, expected an array, got ' + arguments.length + ' arguments. ' +\n            'A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z]).'\n          );\n        } else {\n          printWarning('Invalid argument supplied to oneOf, expected an array.');\n        }\n      }\n      return emptyFunctionThatReturnsNull;\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      for (var i = 0; i < expectedValues.length; i++) {\n        if (is(propValue, expectedValues[i])) {\n          return null;\n        }\n      }\n\n      var valuesString = JSON.stringify(expectedValues, function replacer(key, value) {\n        var type = getPreciseType(value);\n        if (type === 'symbol') {\n          return String(value);\n        }\n        return value;\n      });\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of value `' + String(propValue) + '` ' + ('supplied to `' + componentName + '`, expected one of ' + valuesString + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createObjectOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside objectOf.');\n      }\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an object.'));\n      }\n      for (var key in propValue) {\n        if (has(propValue, key)) {\n          var error = typeChecker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n          if (error instanceof Error) {\n            return error;\n          }\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createUnionTypeChecker(arrayOfTypeCheckers) {\n    if (!Array.isArray(arrayOfTypeCheckers)) {\n      process.env.NODE_ENV !== 'production' ? printWarning('Invalid argument supplied to oneOfType, expected an instance of array.') : void 0;\n      return emptyFunctionThatReturnsNull;\n    }\n\n    for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n      var checker = arrayOfTypeCheckers[i];\n      if (typeof checker !== 'function') {\n        printWarning(\n          'Invalid argument supplied to oneOfType. Expected an array of check functions, but ' +\n          'received ' + getPostfixForTypeWarning(checker) + ' at index ' + i + '.'\n        );\n        return emptyFunctionThatReturnsNull;\n      }\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var expectedTypes = [];\n      for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n        var checker = arrayOfTypeCheckers[i];\n        var checkerResult = checker(props, propName, componentName, location, propFullName, ReactPropTypesSecret);\n        if (checkerResult == null) {\n          return null;\n        }\n        if (checkerResult.data && has(checkerResult.data, 'expectedType')) {\n          expectedTypes.push(checkerResult.data.expectedType);\n        }\n      }\n      var expectedTypesMessage = (expectedTypes.length > 0) ? ', expected one of type [' + expectedTypes.join(', ') + ']': '';\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`' + expectedTypesMessage + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createNodeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!isNode(props[propName])) {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`, expected a ReactNode.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function invalidValidatorError(componentName, location, propFullName, key, type) {\n    return new PropTypeError(\n      (componentName || 'React class') + ': ' + location + ' type `' + propFullName + '.' + key + '` is invalid; ' +\n      'it must be a function, usually from the `prop-types` package, but received `' + type + '`.'\n    );\n  }\n\n  function createShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      for (var key in shapeTypes) {\n        var checker = shapeTypes[key];\n        if (typeof checker !== 'function') {\n          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createStrictShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      // We need to check all keys in case some are required but missing from props.\n      var allKeys = assign({}, props[propName], shapeTypes);\n      for (var key in allKeys) {\n        var checker = shapeTypes[key];\n        if (has(shapeTypes, key) && typeof checker !== 'function') {\n          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n        }\n        if (!checker) {\n          return new PropTypeError(\n            'Invalid ' + location + ' `' + propFullName + '` key `' + key + '` supplied to `' + componentName + '`.' +\n            '\\nBad object: ' + JSON.stringify(props[propName], null, '  ') +\n            '\\nValid keys: ' + JSON.stringify(Object.keys(shapeTypes), null, '  ')\n          );\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n\n    return createChainableTypeChecker(validate);\n  }\n\n  function isNode(propValue) {\n    switch (typeof propValue) {\n      case 'number':\n      case 'string':\n      case 'undefined':\n        return true;\n      case 'boolean':\n        return !propValue;\n      case 'object':\n        if (Array.isArray(propValue)) {\n          return propValue.every(isNode);\n        }\n        if (propValue === null || isValidElement(propValue)) {\n          return true;\n        }\n\n        var iteratorFn = getIteratorFn(propValue);\n        if (iteratorFn) {\n          var iterator = iteratorFn.call(propValue);\n          var step;\n          if (iteratorFn !== propValue.entries) {\n            while (!(step = iterator.next()).done) {\n              if (!isNode(step.value)) {\n                return false;\n              }\n            }\n          } else {\n            // Iterator will provide entry [k,v] tuples rather than values.\n            while (!(step = iterator.next()).done) {\n              var entry = step.value;\n              if (entry) {\n                if (!isNode(entry[1])) {\n                  return false;\n                }\n              }\n            }\n          }\n        } else {\n          return false;\n        }\n\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  function isSymbol(propType, propValue) {\n    // Native Symbol.\n    if (propType === 'symbol') {\n      return true;\n    }\n\n    // falsy value can't be a Symbol\n    if (!propValue) {\n      return false;\n    }\n\n    // 19.4.3.5 Symbol.prototype[@@toStringTag] === 'Symbol'\n    if (propValue['@@toStringTag'] === 'Symbol') {\n      return true;\n    }\n\n    // Fallback for non-spec compliant Symbols which are polyfilled.\n    if (typeof Symbol === 'function' && propValue instanceof Symbol) {\n      return true;\n    }\n\n    return false;\n  }\n\n  // Equivalent of `typeof` but with special handling for array and regexp.\n  function getPropType(propValue) {\n    var propType = typeof propValue;\n    if (Array.isArray(propValue)) {\n      return 'array';\n    }\n    if (propValue instanceof RegExp) {\n      // Old webkits (at least until Android 4.0) return 'function' rather than\n      // 'object' for typeof a RegExp. We'll normalize this here so that /bla/\n      // passes PropTypes.object.\n      return 'object';\n    }\n    if (isSymbol(propType, propValue)) {\n      return 'symbol';\n    }\n    return propType;\n  }\n\n  // This handles more types than `getPropType`. Only used for error messages.\n  // See `createPrimitiveTypeChecker`.\n  function getPreciseType(propValue) {\n    if (typeof propValue === 'undefined' || propValue === null) {\n      return '' + propValue;\n    }\n    var propType = getPropType(propValue);\n    if (propType === 'object') {\n      if (propValue instanceof Date) {\n        return 'date';\n      } else if (propValue instanceof RegExp) {\n        return 'regexp';\n      }\n    }\n    return propType;\n  }\n\n  // Returns a string that is postfixed to a warning about an invalid type.\n  // For example, \"undefined\" or \"of type array\"\n  function getPostfixForTypeWarning(value) {\n    var type = getPreciseType(value);\n    switch (type) {\n      case 'array':\n      case 'object':\n        return 'an ' + type;\n      case 'boolean':\n      case 'date':\n      case 'regexp':\n        return 'a ' + type;\n      default:\n        return type;\n    }\n  }\n\n  // Returns class name of the object, if any.\n  function getClassName(propValue) {\n    if (!propValue.constructor || !propValue.constructor.name) {\n      return ANONYMOUS;\n    }\n    return propValue.constructor.name;\n  }\n\n  ReactPropTypes.checkPropTypes = checkPropTypes;\n  ReactPropTypes.resetWarningCache = checkPropTypes.resetWarningCache;\n  ReactPropTypes.PropTypes = ReactPropTypes;\n\n  return ReactPropTypes;\n};\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactIs = require('react-is');\n\n  // By explicitly using `prop-types` you are opting into new development behavior.\n  // http://fb.me/prop-types-in-prod\n  var throwOnDirectAccess = true;\n  module.exports = require('./factoryWithTypeCheckers')(ReactIs.isElement, throwOnDirectAccess);\n} else {\n  // By explicitly using `prop-types` you are opting into new production behavior.\n  // http://fb.me/prop-types-in-prod\n  module.exports = require('./factoryWithThrowingShims')();\n}\n", "module.exports = {\n\tL : 1,\n\tM : 0,\n\tQ : 3,\n\tH : 2\n};\n\n", "module.exports = {\n\tMODE_NUMBER :\t\t1 << 0,\n\tMODE_ALPHA_NUM : \t1 << 1,\n\tMODE_8BIT_BYTE : \t1 << 2,\n\tMODE_KANJI :\t\t1 << 3\n};\n", "var mode = require('./mode');\n\nfunction QR8bitByte(data) {\n\tthis.mode = mode.MODE_8BIT_BYTE;\n\tthis.data = data;\n}\n\nQR8bitByte.prototype = {\n\n\tgetLength : function(buffer) {\n\t\treturn this.data.length;\n\t},\n\t\n\twrite : function(buffer) {\n\t\tfor (var i = 0; i < this.data.length; i++) {\n\t\t\t// not JIS ...\n\t\t\tbuffer.put(this.data.charCodeAt(i), 8);\n\t\t}\n\t}\n};\n\nmodule.exports = QR8bitByte;\n\n", "// ErrorCorrectLevel\nvar ECL = require('./ErrorCorrectLevel');\n\nfunction QRRSBlock(totalCount, dataCount) {\n\tthis.totalCount = totalCount;\n\tthis.dataCount  = dataCount;\n}\n\nQRRSBlock.RS_BLOCK_TABLE = [\n\n\t// L\n\t// M\n\t// Q\n\t// H\n\n\t// 1\n\t[1, 26, 19],\n\t[1, 26, 16],\n\t[1, 26, 13],\n\t[1, 26, 9],\n\t\n\t// 2\n\t[1, 44, 34],\n\t[1, 44, 28],\n\t[1, 44, 22],\n\t[1, 44, 16],\n\n\t// 3\n\t[1, 70, 55],\n\t[1, 70, 44],\n\t[2, 35, 17],\n\t[2, 35, 13],\n\n\t// 4\t\t\n\t[1, 100, 80],\n\t[2, 50, 32],\n\t[2, 50, 24],\n\t[4, 25, 9],\n\t\n\t// 5\n\t[1, 134, 108],\n\t[2, 67, 43],\n\t[2, 33, 15, 2, 34, 16],\n\t[2, 33, 11, 2, 34, 12],\n\t\n\t// 6\n\t[2, 86, 68],\n\t[4, 43, 27],\n\t[4, 43, 19],\n\t[4, 43, 15],\n\t\n\t// 7\t\t\n\t[2, 98, 78],\n\t[4, 49, 31],\n\t[2, 32, 14, 4, 33, 15],\n\t[4, 39, 13, 1, 40, 14],\n\t\n\t// 8\n\t[2, 121, 97],\n\t[2, 60, 38, 2, 61, 39],\n\t[4, 40, 18, 2, 41, 19],\n\t[4, 40, 14, 2, 41, 15],\n\t\n\t// 9\n\t[2, 146, 116],\n\t[3, 58, 36, 2, 59, 37],\n\t[4, 36, 16, 4, 37, 17],\n\t[4, 36, 12, 4, 37, 13],\n\t\n\t// 10\t\t\n\t[2, 86, 68, 2, 87, 69],\n\t[4, 69, 43, 1, 70, 44],\n\t[6, 43, 19, 2, 44, 20],\n\t[6, 43, 15, 2, 44, 16],\n\n\t// 11\n\t[4, 101, 81],\n\t[1, 80, 50, 4, 81, 51],\n\t[4, 50, 22, 4, 51, 23],\n\t[3, 36, 12, 8, 37, 13],\n\n\t// 12\n\t[2, 116, 92, 2, 117, 93],\n\t[6, 58, 36, 2, 59, 37],\n\t[4, 46, 20, 6, 47, 21],\n\t[7, 42, 14, 4, 43, 15],\n\n\t// 13\n\t[4, 133, 107],\n\t[8, 59, 37, 1, 60, 38],\n\t[8, 44, 20, 4, 45, 21],\n\t[12, 33, 11, 4, 34, 12],\n\n\t// 14\n\t[3, 145, 115, 1, 146, 116],\n\t[4, 64, 40, 5, 65, 41],\n\t[11, 36, 16, 5, 37, 17],\n\t[11, 36, 12, 5, 37, 13],\n\n\t// 15\n\t[5, 109, 87, 1, 110, 88],\n\t[5, 65, 41, 5, 66, 42],\n\t[5, 54, 24, 7, 55, 25],\n\t[11, 36, 12],\n\n\t// 16\n\t[5, 122, 98, 1, 123, 99],\n\t[7, 73, 45, 3, 74, 46],\n\t[15, 43, 19, 2, 44, 20],\n\t[3, 45, 15, 13, 46, 16],\n\n\t// 17\n\t[1, 135, 107, 5, 136, 108],\n\t[10, 74, 46, 1, 75, 47],\n\t[1, 50, 22, 15, 51, 23],\n\t[2, 42, 14, 17, 43, 15],\n\n\t// 18\n\t[5, 150, 120, 1, 151, 121],\n\t[9, 69, 43, 4, 70, 44],\n\t[17, 50, 22, 1, 51, 23],\n\t[2, 42, 14, 19, 43, 15],\n\n\t// 19\n\t[3, 141, 113, 4, 142, 114],\n\t[3, 70, 44, 11, 71, 45],\n\t[17, 47, 21, 4, 48, 22],\n\t[9, 39, 13, 16, 40, 14],\n\n\t// 20\n\t[3, 135, 107, 5, 136, 108],\n\t[3, 67, 41, 13, 68, 42],\n\t[15, 54, 24, 5, 55, 25],\n\t[15, 43, 15, 10, 44, 16],\n\n\t// 21\n\t[4, 144, 116, 4, 145, 117],\n\t[17, 68, 42],\n\t[17, 50, 22, 6, 51, 23],\n\t[19, 46, 16, 6, 47, 17],\n\n\t// 22\n\t[2, 139, 111, 7, 140, 112],\n\t[17, 74, 46],\n\t[7, 54, 24, 16, 55, 25],\n\t[34, 37, 13],\n\n\t// 23\n\t[4, 151, 121, 5, 152, 122],\n\t[4, 75, 47, 14, 76, 48],\n\t[11, 54, 24, 14, 55, 25],\n\t[16, 45, 15, 14, 46, 16],\n\n\t// 24\n\t[6, 147, 117, 4, 148, 118],\n\t[6, 73, 45, 14, 74, 46],\n\t[11, 54, 24, 16, 55, 25],\n\t[30, 46, 16, 2, 47, 17],\n\n\t// 25\n\t[8, 132, 106, 4, 133, 107],\n\t[8, 75, 47, 13, 76, 48],\n\t[7, 54, 24, 22, 55, 25],\n\t[22, 45, 15, 13, 46, 16],\n\n\t// 26\n\t[10, 142, 114, 2, 143, 115],\n\t[19, 74, 46, 4, 75, 47],\n\t[28, 50, 22, 6, 51, 23],\n\t[33, 46, 16, 4, 47, 17],\n\n\t// 27\n\t[8, 152, 122, 4, 153, 123],\n\t[22, 73, 45, 3, 74, 46],\n\t[8, 53, 23, 26, 54, 24],\n\t[12, 45, 15, 28, 46, 16],\n\n\t// 28\n\t[3, 147, 117, 10, 148, 118],\n\t[3, 73, 45, 23, 74, 46],\n\t[4, 54, 24, 31, 55, 25],\n\t[11, 45, 15, 31, 46, 16],\n\n\t// 29\n\t[7, 146, 116, 7, 147, 117],\n\t[21, 73, 45, 7, 74, 46],\n\t[1, 53, 23, 37, 54, 24],\n\t[19, 45, 15, 26, 46, 16],\n\n\t// 30\n\t[5, 145, 115, 10, 146, 116],\n\t[19, 75, 47, 10, 76, 48],\n\t[15, 54, 24, 25, 55, 25],\n\t[23, 45, 15, 25, 46, 16],\n\n\t// 31\n\t[13, 145, 115, 3, 146, 116],\n\t[2, 74, 46, 29, 75, 47],\n\t[42, 54, 24, 1, 55, 25],\n\t[23, 45, 15, 28, 46, 16],\n\n\t// 32\n\t[17, 145, 115],\n\t[10, 74, 46, 23, 75, 47],\n\t[10, 54, 24, 35, 55, 25],\n\t[19, 45, 15, 35, 46, 16],\n\n\t// 33\n\t[17, 145, 115, 1, 146, 116],\n\t[14, 74, 46, 21, 75, 47],\n\t[29, 54, 24, 19, 55, 25],\n\t[11, 45, 15, 46, 46, 16],\n\n\t// 34\n\t[13, 145, 115, 6, 146, 116],\n\t[14, 74, 46, 23, 75, 47],\n\t[44, 54, 24, 7, 55, 25],\n\t[59, 46, 16, 1, 47, 17],\n\n\t// 35\n\t[12, 151, 121, 7, 152, 122],\n\t[12, 75, 47, 26, 76, 48],\n\t[39, 54, 24, 14, 55, 25],\n\t[22, 45, 15, 41, 46, 16],\n\n\t// 36\n\t[6, 151, 121, 14, 152, 122],\n\t[6, 75, 47, 34, 76, 48],\n\t[46, 54, 24, 10, 55, 25],\n\t[2, 45, 15, 64, 46, 16],\n\n\t// 37\n\t[17, 152, 122, 4, 153, 123],\n\t[29, 74, 46, 14, 75, 47],\n\t[49, 54, 24, 10, 55, 25],\n\t[24, 45, 15, 46, 46, 16],\n\n\t// 38\n\t[4, 152, 122, 18, 153, 123],\n\t[13, 74, 46, 32, 75, 47],\n\t[48, 54, 24, 14, 55, 25],\n\t[42, 45, 15, 32, 46, 16],\n\n\t// 39\n\t[20, 147, 117, 4, 148, 118],\n\t[40, 75, 47, 7, 76, 48],\n\t[43, 54, 24, 22, 55, 25],\n\t[10, 45, 15, 67, 46, 16],\n\n\t// 40\n\t[19, 148, 118, 6, 149, 119],\n\t[18, 75, 47, 31, 76, 48],\n\t[34, 54, 24, 34, 55, 25],\n\t[20, 45, 15, 61, 46, 16]\n];\n\nQRRSBlock.getRSBlocks = function(typeNumber, errorCorrectLevel) {\n\t\n\tvar rsBlock = QRRSBlock.getRsBlockTable(typeNumber, errorCorrectLevel);\n\t\n\tif (rsBlock == undefined) {\n\t\tthrow new Error(\"bad rs block @ typeNumber:\" + typeNumber + \"/errorCorrectLevel:\" + errorCorrectLevel);\n\t}\n\n\tvar length = rsBlock.length / 3;\n\t\n\tvar list = new Array();\n\t\n\tfor (var i = 0; i < length; i++) {\n\n\t\tvar count = rsBlock[i * 3 + 0];\n\t\tvar totalCount = rsBlock[i * 3 + 1];\n\t\tvar dataCount  = rsBlock[i * 3 + 2];\n\n\t\tfor (var j = 0; j < count; j++) {\n\t\t\tlist.push(new QRRSBlock(totalCount, dataCount) );\t\n\t\t}\n\t}\n\t\n\treturn list;\n}\n\nQRRSBlock.getRsBlockTable = function(typeNumber, errorCorrectLevel) {\n\n\tswitch(errorCorrectLevel) {\n\tcase ECL.L :\n\t\treturn QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 0];\n\tcase ECL.M :\n\t\treturn QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 1];\n\tcase ECL.Q :\n\t\treturn QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 2];\n\tcase ECL.H :\n\t\treturn QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 3];\n\tdefault :\n\t\treturn undefined;\n\t}\n}\n\nmodule.exports = QRRSBlock;\n", "function QRBitBuffer() {\n\tthis.buffer = new Array();\n\tthis.length = 0;\n}\n\nQRBitBuffer.prototype = {\n\n\tget : function(index) {\n\t\tvar bufIndex = Math.floor(index / 8);\n\t\treturn ( (this.buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1;\n\t},\n\t\n\tput : function(num, length) {\n\t\tfor (var i = 0; i < length; i++) {\n\t\t\tthis.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);\n\t\t}\n\t},\n\t\n\tgetLengthInBits : function() {\n\t\treturn this.length;\n\t},\n\t\n\tputBit : function(bit) {\n\t\n\t\tvar bufIndex = Math.floor(this.length / 8);\n\t\tif (this.buffer.length <= bufIndex) {\n\t\t\tthis.buffer.push(0);\n\t\t}\n\t\n\t\tif (bit) {\n\t\t\tthis.buffer[bufIndex] |= (0x80 >>> (this.length % 8) );\n\t\t}\n\t\n\t\tthis.length++;\n\t}\n};\n\nmodule.exports = QRBitBuffer;\n", "var QRMath = {\n\n\tglog : function(n) {\n\t\n\t\tif (n < 1) {\n\t\t\tthrow new Error(\"glog(\" + n + \")\");\n\t\t}\n\t\t\n\t\treturn QRMath.LOG_TABLE[n];\n\t},\n\t\n\tgexp : function(n) {\n\t\n\t\twhile (n < 0) {\n\t\t\tn += 255;\n\t\t}\n\t\n\t\twhile (n >= 256) {\n\t\t\tn -= 255;\n\t\t}\n\t\n\t\treturn QRMath.EXP_TABLE[n];\n\t},\n\t\n\tEXP_TABLE : new Array(256),\n\t\n\tLOG_TABLE : new Array(256)\n\n};\n\t\nfor (var i = 0; i < 8; i++) {\n\tQRMath.EXP_TABLE[i] = 1 << i;\n}\nfor (var i = 8; i < 256; i++) {\n\tQRMath.EXP_TABLE[i] = QRMath.EXP_TABLE[i - 4]\n\t\t^ QRMath.EXP_TABLE[i - 5]\n\t\t^ QRMath.EXP_TABLE[i - 6]\n\t\t^ QRMath.EXP_TABLE[i - 8];\n}\nfor (var i = 0; i < 255; i++) {\n\tQRMath.LOG_TABLE[QRMath.EXP_TABLE[i] ] = i;\n}\n\nmodule.exports = QRMath;\n", "var math = require('./math');\n\nfunction QRPolynomial(num, shift) {\n\n\tif (num.length == undefined) {\n\t\tthrow new Error(num.length + \"/\" + shift);\n\t}\n\n\tvar offset = 0;\n\n\twhile (offset < num.length && num[offset] == 0) {\n\t\toffset++;\n\t}\n\n\tthis.num = new Array(num.length - offset + shift);\n\tfor (var i = 0; i < num.length - offset; i++) {\n\t\tthis.num[i] = num[i + offset];\n\t}\n}\n\nQRPolynomial.prototype = {\n\n\tget : function(index) {\n\t\treturn this.num[index];\n\t},\n\t\n\tgetLength : function() {\n\t\treturn this.num.length;\n\t},\n\t\n\tmultiply : function(e) {\n\t\n\t\tvar num = new Array(this.getLength() + e.getLength() - 1);\n\t\n\t\tfor (var i = 0; i < this.getLength(); i++) {\n\t\t\tfor (var j = 0; j < e.getLength(); j++) {\n\t\t\t\tnum[i + j] ^= math.gexp(math.glog(this.get(i) ) + math.glog(e.get(j) ) );\n\t\t\t}\n\t\t}\n\t\n\t\treturn new QRPolynomial(num, 0);\n\t},\n\t\n\tmod : function(e) {\n\t\n\t\tif (this.getLength() - e.getLength() < 0) {\n\t\t\treturn this;\n\t\t}\n\t\n\t\tvar ratio = math.glog(this.get(0) ) - math.glog(e.get(0) );\n\t\n\t\tvar num = new Array(this.getLength() );\n\t\t\n\t\tfor (var i = 0; i < this.getLength(); i++) {\n\t\t\tnum[i] = this.get(i);\n\t\t}\n\t\t\n\t\tfor (var i = 0; i < e.getLength(); i++) {\n\t\t\tnum[i] ^= math.gexp(math.glog(e.get(i) ) + ratio);\n\t\t}\n\t\n\t\t// recursive call\n\t\treturn new QRPolynomial(num, 0).mod(e);\n\t}\n};\n\nmodule.exports = QRPolynomial;\n", "var Mode = require('./mode');\nvar Polynomial = require('./Polynomial');\nvar math = require('./math');\n\nvar QRMaskPattern = {\n\tPATTERN000 : 0,\n\tPATTERN001 : 1,\n\tPATTERN010 : 2,\n\tPATTERN011 : 3,\n\tPATTERN100 : 4,\n\tPATTERN101 : 5,\n\tPATTERN110 : 6,\n\tPATTERN111 : 7\n};\n\nvar QRUtil = {\n\n    PATTERN_POSITION_TABLE : [\n\t    [],\n\t    [6, 18],\n\t    [6, 22],\n\t    [6, 26],\n\t    [6, 30],\n\t    [6, 34],\n\t    [6, 22, 38],\n\t    [6, 24, 42],\n\t    [6, 26, 46],\n\t    [6, 28, 50],\n\t    [6, 30, 54],\t\t\n\t    [6, 32, 58],\n\t    [6, 34, 62],\n\t    [6, 26, 46, 66],\n\t    [6, 26, 48, 70],\n\t    [6, 26, 50, 74],\n\t    [6, 30, 54, 78],\n\t    [6, 30, 56, 82],\n\t    [6, 30, 58, 86],\n\t    [6, 34, 62, 90],\n\t    [6, 28, 50, 72, 94],\n\t    [6, 26, 50, 74, 98],\n\t    [6, 30, 54, 78, 102],\n\t    [6, 28, 54, 80, 106],\n\t    [6, 32, 58, 84, 110],\n\t    [6, 30, 58, 86, 114],\n\t    [6, 34, 62, 90, 118],\n\t    [6, 26, 50, 74, 98, 122],\n\t    [6, 30, 54, 78, 102, 126],\n\t    [6, 26, 52, 78, 104, 130],\n\t    [6, 30, 56, 82, 108, 134],\n\t    [6, 34, 60, 86, 112, 138],\n\t    [6, 30, 58, 86, 114, 142],\n\t    [6, 34, 62, 90, 118, 146],\n\t    [6, 30, 54, 78, 102, 126, 150],\n\t    [6, 24, 50, 76, 102, 128, 154],\n\t    [6, 28, 54, 80, 106, 132, 158],\n\t    [6, 32, 58, 84, 110, 136, 162],\n\t    [6, 26, 54, 82, 110, 138, 166],\n\t    [6, 30, 58, 86, 114, 142, 170]\n    ],\n\n    G15 : (1 << 10) | (1 << 8) | (1 << 5) | (1 << 4) | (1 << 2) | (1 << 1) | (1 << 0),\n    G18 : (1 << 12) | (1 << 11) | (1 << 10) | (1 << 9) | (1 << 8) | (1 << 5) | (1 << 2) | (1 << 0),\n    G15_MASK : (1 << 14) | (1 << 12) | (1 << 10)\t| (1 << 4) | (1 << 1),\n\n    getBCHTypeInfo : function(data) {\n\t    var d = data << 10;\n\t    while (QRUtil.getBCHDigit(d) - QRUtil.getBCHDigit(QRUtil.G15) >= 0) {\n\t\t    d ^= (QRUtil.G15 << (QRUtil.getBCHDigit(d) - QRUtil.getBCHDigit(QRUtil.G15) ) ); \t\n\t    }\n\t    return ( (data << 10) | d) ^ QRUtil.G15_MASK;\n    },\n\n    getBCHTypeNumber : function(data) {\n\t    var d = data << 12;\n\t    while (QRUtil.getBCHDigit(d) - QRUtil.getBCHDigit(QRUtil.G18) >= 0) {\n\t\t    d ^= (QRUtil.G18 << (QRUtil.getBCHDigit(d) - QRUtil.getBCHDigit(QRUtil.G18) ) ); \t\n\t    }\n\t    return (data << 12) | d;\n    },\n\n    getBCHDigit : function(data) {\n\n\t    var digit = 0;\n\n\t    while (data != 0) {\n\t\t    digit++;\n\t\t    data >>>= 1;\n\t    }\n\n\t    return digit;\n    },\n\n    getPatternPosition : function(typeNumber) {\n\t    return QRUtil.PATTERN_POSITION_TABLE[typeNumber - 1];\n    },\n\n    getMask : function(maskPattern, i, j) {\n\t    \n\t    switch (maskPattern) {\n\t\t    \n\t    case QRMaskPattern.PATTERN000 : return (i + j) % 2 == 0;\n\t    case QRMaskPattern.PATTERN001 : return i % 2 == 0;\n\t    case QRMaskPattern.PATTERN010 : return j % 3 == 0;\n\t    case QRMaskPattern.PATTERN011 : return (i + j) % 3 == 0;\n\t    case QRMaskPattern.PATTERN100 : return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0;\n\t    case QRMaskPattern.PATTERN101 : return (i * j) % 2 + (i * j) % 3 == 0;\n\t    case QRMaskPattern.PATTERN110 : return ( (i * j) % 2 + (i * j) % 3) % 2 == 0;\n\t    case QRMaskPattern.PATTERN111 : return ( (i * j) % 3 + (i + j) % 2) % 2 == 0;\n\n\t    default :\n\t\t    throw new Error(\"bad maskPattern:\" + maskPattern);\n\t    }\n    },\n\n    getErrorCorrectPolynomial : function(errorCorrectLength) {\n\n\t    var a = new Polynomial([1], 0);\n\n\t    for (var i = 0; i < errorCorrectLength; i++) {\n\t\t    a = a.multiply(new Polynomial([1, math.gexp(i)], 0) );\n\t    }\n\n\t    return a;\n    },\n\n    getLengthInBits : function(mode, type) {\n\n\t    if (1 <= type && type < 10) {\n\n\t\t    // 1 - 9\n\n\t\t    switch(mode) {\n\t\t    case Mode.MODE_NUMBER \t: return 10;\n\t\t    case Mode.MODE_ALPHA_NUM \t: return 9;\n\t\t    case Mode.MODE_8BIT_BYTE\t: return 8;\n\t\t    case Mode.MODE_KANJI  \t: return 8;\n\t\t    default :\n\t\t\t    throw new Error(\"mode:\" + mode);\n\t\t    }\n\n\t    } else if (type < 27) {\n\n\t\t    // 10 - 26\n\n\t\t    switch(mode) {\n\t\t    case Mode.MODE_NUMBER \t: return 12;\n\t\t    case Mode.MODE_ALPHA_NUM \t: return 11;\n\t\t    case Mode.MODE_8BIT_BYTE\t: return 16;\n\t\t    case Mode.MODE_KANJI  \t: return 10;\n\t\t    default :\n\t\t\t    throw new Error(\"mode:\" + mode);\n\t\t    }\n\n\t    } else if (type < 41) {\n\n\t\t    // 27 - 40\n\n\t\t    switch(mode) {\n\t\t    case Mode.MODE_NUMBER \t: return 14;\n\t\t    case Mode.MODE_ALPHA_NUM\t: return 13;\n\t\t    case Mode.MODE_8BIT_BYTE\t: return 16;\n\t\t    case Mode.MODE_KANJI  \t: return 12;\n\t\t    default :\n\t\t\t    throw new Error(\"mode:\" + mode);\n\t\t    }\n\n\t    } else {\n\t\t    throw new Error(\"type:\" + type);\n\t    }\n    },\n\n    getLostPoint : function(qrCode) {\n\t    \n\t    var moduleCount = qrCode.getModuleCount();\n\t    \n\t    var lostPoint = 0;\n\t    \n\t    // LEVEL1\n\t    \n\t    for (var row = 0; row < moduleCount; row++) {\n\n\t\t    for (var col = 0; col < moduleCount; col++) {\n\n\t\t\t    var sameCount = 0;\n\t\t\t    var dark = qrCode.isDark(row, col);\n\n\t\t\t\tfor (var r = -1; r <= 1; r++) {\n\n\t\t\t\t    if (row + r < 0 || moduleCount <= row + r) {\n\t\t\t\t\t    continue;\n\t\t\t\t    }\n\n\t\t\t\t    for (var c = -1; c <= 1; c++) {\n\n\t\t\t\t\t    if (col + c < 0 || moduleCount <= col + c) {\n\t\t\t\t\t\t    continue;\n\t\t\t\t\t    }\n\n\t\t\t\t\t    if (r == 0 && c == 0) {\n\t\t\t\t\t\t    continue;\n\t\t\t\t\t    }\n\n\t\t\t\t\t    if (dark == qrCode.isDark(row + r, col + c) ) {\n\t\t\t\t\t\t    sameCount++;\n\t\t\t\t\t    }\n\t\t\t\t    }\n\t\t\t    }\n\n\t\t\t    if (sameCount > 5) {\n\t\t\t\t    lostPoint += (3 + sameCount - 5);\n\t\t\t    }\n\t\t    }\n\t    }\n\n\t    // LEVEL2\n\n\t    for (var row = 0; row < moduleCount - 1; row++) {\n\t\t    for (var col = 0; col < moduleCount - 1; col++) {\n\t\t\t    var count = 0;\n\t\t\t    if (qrCode.isDark(row,     col    ) ) count++;\n\t\t\t    if (qrCode.isDark(row + 1, col    ) ) count++;\n\t\t\t    if (qrCode.isDark(row,     col + 1) ) count++;\n\t\t\t    if (qrCode.isDark(row + 1, col + 1) ) count++;\n\t\t\t    if (count == 0 || count == 4) {\n\t\t\t\t    lostPoint += 3;\n\t\t\t    }\n\t\t    }\n\t    }\n\n\t    // LEVEL3\n\n\t    for (var row = 0; row < moduleCount; row++) {\n\t\t    for (var col = 0; col < moduleCount - 6; col++) {\n\t\t\t    if (qrCode.isDark(row, col)\n\t\t\t\t\t    && !qrCode.isDark(row, col + 1)\n\t\t\t\t\t    &&  qrCode.isDark(row, col + 2)\n\t\t\t\t\t    &&  qrCode.isDark(row, col + 3)\n\t\t\t\t\t    &&  qrCode.isDark(row, col + 4)\n\t\t\t\t\t    && !qrCode.isDark(row, col + 5)\n\t\t\t\t\t    &&  qrCode.isDark(row, col + 6) ) {\n\t\t\t\t    lostPoint += 40;\n\t\t\t    }\n\t\t    }\n\t    }\n\n\t    for (var col = 0; col < moduleCount; col++) {\n\t\t    for (var row = 0; row < moduleCount - 6; row++) {\n\t\t\t    if (qrCode.isDark(row, col)\n\t\t\t\t\t    && !qrCode.isDark(row + 1, col)\n\t\t\t\t\t    &&  qrCode.isDark(row + 2, col)\n\t\t\t\t\t    &&  qrCode.isDark(row + 3, col)\n\t\t\t\t\t    &&  qrCode.isDark(row + 4, col)\n\t\t\t\t\t    && !qrCode.isDark(row + 5, col)\n\t\t\t\t\t    &&  qrCode.isDark(row + 6, col) ) {\n\t\t\t\t    lostPoint += 40;\n\t\t\t    }\n\t\t    }\n\t    }\n\n\t    // LEVEL4\n\t    \n\t    var darkCount = 0;\n\n\t    for (var col = 0; col < moduleCount; col++) {\n\t\t    for (var row = 0; row < moduleCount; row++) {\n\t\t\t    if (qrCode.isDark(row, col) ) {\n\t\t\t\t    darkCount++;\n\t\t\t    }\n\t\t    }\n\t    }\n\t    \n\t    var ratio = Math.abs(100 * darkCount / moduleCount / moduleCount - 50) / 5;\n\t    lostPoint += ratio * 10;\n\n\t    return lostPoint;\t\t\n    }\n};\n\nmodule.exports = QRUtil;\n", "var BitByte = require('./8BitByte');\nvar RSBlock = require('./RSBlock');\nvar BitBuffer = require('./BitBuffer');\nvar util = require('./util');\nvar Polynomial = require('./Polynomial');\n\nfunction QRCode(typeNumber, errorCorrectLevel) {\n\tthis.typeNumber = typeNumber;\n\tthis.errorCorrectLevel = errorCorrectLevel;\n\tthis.modules = null;\n\tthis.moduleCount = 0;\n\tthis.dataCache = null;\n\tthis.dataList = [];\n}\n\n// for client side minification\nvar proto = QRCode.prototype;\n\nproto.addData = function(data) {\n\tvar newData = new BitByte(data);\n\tthis.dataList.push(newData);\n\tthis.dataCache = null;\n};\n\nproto.isDark = function(row, col) {\n\tif (row < 0 || this.moduleCount <= row || col < 0 || this.moduleCount <= col) {\n\t\tthrow new Error(row + \",\" + col);\n\t}\n\treturn this.modules[row][col];\n};\n\nproto.getModuleCount = function() {\n\treturn this.moduleCount;\n};\n\nproto.make = function() {\n\t// Calculate automatically typeNumber if provided is < 1\n\tif (this.typeNumber < 1 ){\n\t\tvar typeNumber = 1;\n\t\tfor (typeNumber = 1; typeNumber < 40; typeNumber++) {\n\t\t\tvar rsBlocks = RSBlock.getRSBlocks(typeNumber, this.errorCorrectLevel);\n\n\t\t\tvar buffer = new BitBuffer();\n\t\t\tvar totalDataCount = 0;\n\t\t\tfor (var i = 0; i < rsBlocks.length; i++) {\n\t\t\t\ttotalDataCount += rsBlocks[i].dataCount;\n\t\t\t}\n\n\t\t\tfor (var i = 0; i < this.dataList.length; i++) {\n\t\t\t\tvar data = this.dataList[i];\n\t\t\t\tbuffer.put(data.mode, 4);\n\t\t\t\tbuffer.put(data.getLength(), util.getLengthInBits(data.mode, typeNumber) );\n\t\t\t\tdata.write(buffer);\n\t\t\t}\n\t\t\tif (buffer.getLengthInBits() <= totalDataCount * 8)\n\t\t\t\tbreak;\n\t\t}\n\t\tthis.typeNumber = typeNumber;\n\t}\n\tthis.makeImpl(false, this.getBestMaskPattern() );\n};\n\nproto.makeImpl = function(test, maskPattern) {\n\t\n\tthis.moduleCount = this.typeNumber * 4 + 17;\n\tthis.modules = new Array(this.moduleCount);\n\t\n\tfor (var row = 0; row < this.moduleCount; row++) {\n\t\t\n\t\tthis.modules[row] = new Array(this.moduleCount);\n\t\t\n\t\tfor (var col = 0; col < this.moduleCount; col++) {\n\t\t\tthis.modules[row][col] = null;//(col + row) % 3;\n\t\t}\n\t}\n\n\tthis.setupPositionProbePattern(0, 0);\n\tthis.setupPositionProbePattern(this.moduleCount - 7, 0);\n\tthis.setupPositionProbePattern(0, this.moduleCount - 7);\n\tthis.setupPositionAdjustPattern();\n\tthis.setupTimingPattern();\n\tthis.setupTypeInfo(test, maskPattern);\n\t\n\tif (this.typeNumber >= 7) {\n\t\tthis.setupTypeNumber(test);\n\t}\n\n\tif (this.dataCache == null) {\n\t\tthis.dataCache = QRCode.createData(this.typeNumber, this.errorCorrectLevel, this.dataList);\n\t}\n\n\tthis.mapData(this.dataCache, maskPattern);\n};\n\nproto.setupPositionProbePattern = function(row, col)  {\n\t\n\tfor (var r = -1; r <= 7; r++) {\n\t\t\n\t\tif (row + r <= -1 || this.moduleCount <= row + r) continue;\n\t\t\n\t\tfor (var c = -1; c <= 7; c++) {\n\t\t\t\n\t\t\tif (col + c <= -1 || this.moduleCount <= col + c) continue;\n\t\t\t\n\t\t\tif ( (0 <= r && r <= 6 && (c == 0 || c == 6) )\n\t\t\t\t\t|| (0 <= c && c <= 6 && (r == 0 || r == 6) )\n\t\t\t\t\t|| (2 <= r && r <= 4 && 2 <= c && c <= 4) ) {\n\t\t\t\tthis.modules[row + r][col + c] = true;\n\t\t\t} else {\n\t\t\t\tthis.modules[row + r][col + c] = false;\n\t\t\t}\n\t\t}\t\t\n\t}\t\t\n};\n\nproto.getBestMaskPattern = function() {\n\n\tvar minLostPoint = 0;\n\tvar pattern = 0;\n\n\tfor (var i = 0; i < 8; i++) {\n\t\t\n\t\tthis.makeImpl(true, i);\n\n\t\tvar lostPoint = util.getLostPoint(this);\n\n\t\tif (i == 0 || minLostPoint >  lostPoint) {\n\t\t\tminLostPoint = lostPoint;\n\t\t\tpattern = i;\n\t\t}\n\t}\n\n\treturn pattern;\n};\n\nproto.createMovieClip = function(target_mc, instance_name, depth) {\n\n\tvar qr_mc = target_mc.createEmptyMovieClip(instance_name, depth);\n\tvar cs = 1;\n\n\tthis.make();\n\n\tfor (var row = 0; row < this.modules.length; row++) {\n\t\t\n\t\tvar y = row * cs;\n\t\t\n\t\tfor (var col = 0; col < this.modules[row].length; col++) {\n\n\t\t\tvar x = col * cs;\n\t\t\tvar dark = this.modules[row][col];\n\t\t\n\t\t\tif (dark) {\n\t\t\t\tqr_mc.beginFill(0, 100);\n\t\t\t\tqr_mc.moveTo(x, y);\n\t\t\t\tqr_mc.lineTo(x + cs, y);\n\t\t\t\tqr_mc.lineTo(x + cs, y + cs);\n\t\t\t\tqr_mc.lineTo(x, y + cs);\n\t\t\t\tqr_mc.endFill();\n\t\t\t}\n\t\t}\n\t}\n\t\n\treturn qr_mc;\n};\n\nproto.setupTimingPattern = function() {\n\t\n\tfor (var r = 8; r < this.moduleCount - 8; r++) {\n\t\tif (this.modules[r][6] != null) {\n\t\t\tcontinue;\n\t\t}\n\t\tthis.modules[r][6] = (r % 2 == 0);\n\t}\n\n\tfor (var c = 8; c < this.moduleCount - 8; c++) {\n\t\tif (this.modules[6][c] != null) {\n\t\t\tcontinue;\n\t\t}\n\t\tthis.modules[6][c] = (c % 2 == 0);\n\t}\n};\n\nproto.setupPositionAdjustPattern = function() {\n\n\tvar pos = util.getPatternPosition(this.typeNumber);\n\t\n\tfor (var i = 0; i < pos.length; i++) {\n\t\n\t\tfor (var j = 0; j < pos.length; j++) {\n\t\t\n\t\t\tvar row = pos[i];\n\t\t\tvar col = pos[j];\n\t\t\t\n\t\t\tif (this.modules[row][col] != null) {\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\t\n\t\t\tfor (var r = -2; r <= 2; r++) {\n\t\t\t\n\t\t\t\tfor (var c = -2; c <= 2; c++) {\n\t\t\t\t\n\t\t\t\t\tif (r == -2 || r == 2 || c == -2 || c == 2\n\t\t\t\t\t\t\t|| (r == 0 && c == 0) ) {\n\t\t\t\t\t\tthis.modules[row + r][col + c] = true;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.modules[row + r][col + c] = false;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n};\n\nproto.setupTypeNumber = function(test) {\n\n\tvar bits = util.getBCHTypeNumber(this.typeNumber);\n\n\tfor (var i = 0; i < 18; i++) {\n\t\tvar mod = (!test && ( (bits >> i) & 1) == 1);\n\t\tthis.modules[Math.floor(i / 3)][i % 3 + this.moduleCount - 8 - 3] = mod;\n\t}\n\n\tfor (var i = 0; i < 18; i++) {\n\t\tvar mod = (!test && ( (bits >> i) & 1) == 1);\n\t\tthis.modules[i % 3 + this.moduleCount - 8 - 3][Math.floor(i / 3)] = mod;\n\t}\n};\n\nproto.setupTypeInfo = function(test, maskPattern) {\n\n\tvar data = (this.errorCorrectLevel << 3) | maskPattern;\n\tvar bits = util.getBCHTypeInfo(data);\n\n\t// vertical\t\t\n\tfor (var i = 0; i < 15; i++) {\n\n\t\tvar mod = (!test && ( (bits >> i) & 1) == 1);\n\n\t\tif (i < 6) {\n\t\t\tthis.modules[i][8] = mod;\n\t\t} else if (i < 8) {\n\t\t\tthis.modules[i + 1][8] = mod;\n\t\t} else {\n\t\t\tthis.modules[this.moduleCount - 15 + i][8] = mod;\n\t\t}\n\t}\n\n\t// horizontal\n\tfor (var i = 0; i < 15; i++) {\n\n\t\tvar mod = (!test && ( (bits >> i) & 1) == 1);\n\t\t\n\t\tif (i < 8) {\n\t\t\tthis.modules[8][this.moduleCount - i - 1] = mod;\n\t\t} else if (i < 9) {\n\t\t\tthis.modules[8][15 - i - 1 + 1] = mod;\n\t\t} else {\n\t\t\tthis.modules[8][15 - i - 1] = mod;\n\t\t}\n\t}\n\n\t// fixed module\n\tthis.modules[this.moduleCount - 8][8] = (!test);\n};\n\nproto.mapData = function(data, maskPattern) {\n\t\n\tvar inc = -1;\n\tvar row = this.moduleCount - 1;\n\tvar bitIndex = 7;\n\tvar byteIndex = 0;\n\t\n\tfor (var col = this.moduleCount - 1; col > 0; col -= 2) {\n\n\t\tif (col == 6) col--;\n\n\t\twhile (true) {\n\n\t\t\tfor (var c = 0; c < 2; c++) {\n\t\t\t\t\n\t\t\t\tif (this.modules[row][col - c] == null) {\n\t\t\t\t\t\n\t\t\t\t\tvar dark = false;\n\n\t\t\t\t\tif (byteIndex < data.length) {\n\t\t\t\t\t\tdark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);\n\t\t\t\t\t}\n\n\t\t\t\t\tvar mask = util.getMask(maskPattern, row, col - c);\n\n\t\t\t\t\tif (mask) {\n\t\t\t\t\t\tdark = !dark;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tthis.modules[row][col - c] = dark;\n\t\t\t\t\tbitIndex--;\n\n\t\t\t\t\tif (bitIndex == -1) {\n\t\t\t\t\t\tbyteIndex++;\n\t\t\t\t\t\tbitIndex = 7;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\trow += inc;\n\n\t\t\tif (row < 0 || this.moduleCount <= row) {\n\t\t\t\trow -= inc;\n\t\t\t\tinc = -inc;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t}\n};\n\nQRCode.PAD0 = 0xEC;\nQRCode.PAD1 = 0x11;\n\nQRCode.createData = function(typeNumber, errorCorrectLevel, dataList) {\n\t\n\tvar rsBlocks = RSBlock.getRSBlocks(typeNumber, errorCorrectLevel);\n\t\n\tvar buffer = new BitBuffer();\n\t\n\tfor (var i = 0; i < dataList.length; i++) {\n\t\tvar data = dataList[i];\n\t\tbuffer.put(data.mode, 4);\n\t\tbuffer.put(data.getLength(), util.getLengthInBits(data.mode, typeNumber) );\n\t\tdata.write(buffer);\n\t}\n\n\t// calc num max data.\n\tvar totalDataCount = 0;\n\tfor (var i = 0; i < rsBlocks.length; i++) {\n\t\ttotalDataCount += rsBlocks[i].dataCount;\n\t}\n\n\tif (buffer.getLengthInBits() > totalDataCount * 8) {\n\t\tthrow new Error(\"code length overflow. (\"\n\t\t\t+ buffer.getLengthInBits()\n\t\t\t+ \">\"\n\t\t\t+  totalDataCount * 8\n\t\t\t+ \")\");\n\t}\n\n\t// end code\n\tif (buffer.getLengthInBits() + 4 <= totalDataCount * 8) {\n\t\tbuffer.put(0, 4);\n\t}\n\n\t// padding\n\twhile (buffer.getLengthInBits() % 8 != 0) {\n\t\tbuffer.putBit(false);\n\t}\n\n\t// padding\n\twhile (true) {\n\t\t\n\t\tif (buffer.getLengthInBits() >= totalDataCount * 8) {\n\t\t\tbreak;\n\t\t}\n\t\tbuffer.put(QRCode.PAD0, 8);\n\t\t\n\t\tif (buffer.getLengthInBits() >= totalDataCount * 8) {\n\t\t\tbreak;\n\t\t}\n\t\tbuffer.put(QRCode.PAD1, 8);\n\t}\n\n\treturn QRCode.createBytes(buffer, rsBlocks);\n};\n\nQRCode.createBytes = function(buffer, rsBlocks) {\n\n\tvar offset = 0;\n\t\n\tvar maxDcCount = 0;\n\tvar maxEcCount = 0;\n\t\n\tvar dcdata = new Array(rsBlocks.length);\n\tvar ecdata = new Array(rsBlocks.length);\n\t\n\tfor (var r = 0; r < rsBlocks.length; r++) {\n\n\t\tvar dcCount = rsBlocks[r].dataCount;\n\t\tvar ecCount = rsBlocks[r].totalCount - dcCount;\n\n\t\tmaxDcCount = Math.max(maxDcCount, dcCount);\n\t\tmaxEcCount = Math.max(maxEcCount, ecCount);\n\t\t\n\t\tdcdata[r] = new Array(dcCount);\n\t\t\n\t\tfor (var i = 0; i < dcdata[r].length; i++) {\n\t\t\tdcdata[r][i] = 0xff & buffer.buffer[i + offset];\n\t\t}\n\t\toffset += dcCount;\n\t\t\n\t\tvar rsPoly = util.getErrorCorrectPolynomial(ecCount);\n\t\tvar rawPoly = new Polynomial(dcdata[r], rsPoly.getLength() - 1);\n\n\t\tvar modPoly = rawPoly.mod(rsPoly);\n\t\tecdata[r] = new Array(rsPoly.getLength() - 1);\n\t\tfor (var i = 0; i < ecdata[r].length; i++) {\n            var modIndex = i + modPoly.getLength() - ecdata[r].length;\n\t\t\tecdata[r][i] = (modIndex >= 0)? modPoly.get(modIndex) : 0;\n\t\t}\n\n\t}\n\t\n\tvar totalCodeCount = 0;\n\tfor (var i = 0; i < rsBlocks.length; i++) {\n\t\ttotalCodeCount += rsBlocks[i].totalCount;\n\t}\n\n\tvar data = new Array(totalCodeCount);\n\tvar index = 0;\n\n\tfor (var i = 0; i < maxDcCount; i++) {\n\t\tfor (var r = 0; r < rsBlocks.length; r++) {\n\t\t\tif (i < dcdata[r].length) {\n\t\t\t\tdata[index++] = dcdata[r][i];\n\t\t\t}\n\t\t}\n\t}\n\n\tfor (var i = 0; i < maxEcCount; i++) {\n\t\tfor (var r = 0; r < rsBlocks.length; r++) {\n\t\t\tif (i < ecdata[r].length) {\n\t\t\t\tdata[index++] = ecdata[r][i];\n\t\t\t}\n\t\t}\n\t}\n\n\treturn data;\n};\n\nmodule.exports = QRCode;\n\n", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\nvar _propTypes = require(\"prop-types\");\n\nvar _propTypes2 = _interopRequireDefault(_propTypes);\n\nvar _react = require(\"react\");\n\nvar _react2 = _interopRequireDefault(_react);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _objectWithoutProperties(obj, keys) { var target = {}; for (var i in obj) { if (keys.indexOf(i) >= 0) continue; if (!Object.prototype.hasOwnProperty.call(obj, i)) continue; target[i] = obj[i]; } return target; }\n\nvar propTypes = {\n  bgColor: _propTypes2.default.oneOfType([_propTypes2.default.object, _propTypes2.default.string]).isRequired,\n  bgD: _propTypes2.default.string.isRequired,\n  fgColor: _propTypes2.default.oneOfType([_propTypes2.default.object, _propTypes2.default.string]).isRequired,\n  fgD: _propTypes2.default.string.isRequired,\n  size: _propTypes2.default.number.isRequired,\n  title: _propTypes2.default.string,\n  viewBoxSize: _propTypes2.default.number.isRequired,\n  xmlns: _propTypes2.default.string\n};\n\nvar QRCodeSvg = (0, _react.forwardRef)(function (_ref, ref) {\n  var bgColor = _ref.bgColor,\n      bgD = _ref.bgD,\n      fgD = _ref.fgD,\n      fgColor = _ref.fgColor,\n      size = _ref.size,\n      title = _ref.title,\n      viewBoxSize = _ref.viewBoxSize,\n      _ref$xmlns = _ref.xmlns,\n      xmlns = _ref$xmlns === undefined ? \"http://www.w3.org/2000/svg\" : _ref$xmlns,\n      props = _objectWithoutProperties(_ref, [\"bgColor\", \"bgD\", \"fgD\", \"fgColor\", \"size\", \"title\", \"viewBoxSize\", \"xmlns\"]);\n\n  return _react2.default.createElement(\n    \"svg\",\n    _extends({}, props, { height: size, ref: ref, viewBox: \"0 0 \" + viewBoxSize + \" \" + viewBoxSize, width: size, xmlns: xmlns }),\n    title ? _react2.default.createElement(\n      \"title\",\n      null,\n      title\n    ) : null,\n    _react2.default.createElement(\"path\", { d: bgD, fill: bgColor }),\n    _react2.default.createElement(\"path\", { d: fgD, fill: fgColor })\n  );\n});\n\nQRCodeSvg.displayName = \"QRCodeSvg\";\nQRCodeSvg.propTypes = propTypes;\n\nexports.default = QRCodeSvg;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.QRCode = undefined;\n\nvar _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\nvar _propTypes = require(\"prop-types\");\n\nvar _propTypes2 = _interopRequireDefault(_propTypes);\n\nvar _ErrorCorrectLevel = require(\"qr.js/lib/ErrorCorrectLevel\");\n\nvar _ErrorCorrectLevel2 = _interopRequireDefault(_ErrorCorrectLevel);\n\nvar _QRCode = require(\"qr.js/lib/QRCode\");\n\nvar _QRCode2 = _interopRequireDefault(_QRCode);\n\nvar _react = require(\"react\");\n\nvar _react2 = _interopRequireDefault(_react);\n\nvar _QRCodeSvg = require(\"./QRCodeSvg\");\n\nvar _QRCodeSvg2 = _interopRequireDefault(_QRCodeSvg);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _objectWithoutProperties(obj, keys) { var target = {}; for (var i in obj) { if (keys.indexOf(i) >= 0) continue; if (!Object.prototype.hasOwnProperty.call(obj, i)) continue; target[i] = obj[i]; } return target; }\n// A `qr.js` doesn't handle error level of zero (M) so we need to do it right, thus the deep require.\n\n\nvar propTypes = {\n  bgColor: _propTypes2.default.oneOfType([_propTypes2.default.object, _propTypes2.default.string]),\n  fgColor: _propTypes2.default.oneOfType([_propTypes2.default.object, _propTypes2.default.string]),\n  level: _propTypes2.default.string,\n  size: _propTypes2.default.number,\n  value: _propTypes2.default.string.isRequired\n};\n\nvar QRCode = (0, _react.forwardRef)(function (_ref, ref) {\n  var _ref$bgColor = _ref.bgColor,\n      bgColor = _ref$bgColor === undefined ? \"#FFFFFF\" : _ref$bgColor,\n      _ref$fgColor = _ref.fgColor,\n      fgColor = _ref$fgColor === undefined ? \"#000000\" : _ref$fgColor,\n      _ref$level = _ref.level,\n      level = _ref$level === undefined ? \"L\" : _ref$level,\n      _ref$size = _ref.size,\n      size = _ref$size === undefined ? 256 : _ref$size,\n      value = _ref.value,\n      props = _objectWithoutProperties(_ref, [\"bgColor\", \"fgColor\", \"level\", \"size\", \"value\"]);\n\n  // Use type === -1 to automatically pick the best type.\n  var qrcode = new _QRCode2.default(-1, _ErrorCorrectLevel2.default[level]);\n  qrcode.addData(value);\n  qrcode.make();\n  var cells = qrcode.modules;\n  return _react2.default.createElement(_QRCodeSvg2.default, _extends({}, props, {\n    bgColor: bgColor,\n    bgD: cells.map(function (row, rowIndex) {\n      return row.map(function (cell, cellIndex) {\n        return !cell ? \"M \" + cellIndex + \" \" + rowIndex + \" l 1 0 0 1 -1 0 Z\" : \"\";\n      }).join(\" \");\n    }).join(\" \"),\n    fgColor: fgColor,\n    fgD: cells.map(function (row, rowIndex) {\n      return row.map(function (cell, cellIndex) {\n        return cell ? \"M \" + cellIndex + \" \" + rowIndex + \" l 1 0 0 1 -1 0 Z\" : \"\";\n      }).join(\" \");\n    }).join(\" \"),\n    ref: ref,\n    size: size,\n    viewBoxSize: cells.length\n  }));\n});\n\nexports.QRCode = QRCode;\nQRCode.displayName = \"QRCode\";\nQRCode.propTypes = propTypes;\n\nexports.default = QRCode;"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA;AAaA,QAAI,MAAuC;AACzC,OAAC,WAAW;AACd;AAIA,YAAI,YAAY,OAAO,WAAW,cAAc,OAAO;AACvD,YAAI,qBAAqB,YAAY,OAAO,IAAI,eAAe,IAAI;AACnE,YAAI,oBAAoB,YAAY,OAAO,IAAI,cAAc,IAAI;AACjE,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,yBAAyB,YAAY,OAAO,IAAI,mBAAmB,IAAI;AAC3E,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,qBAAqB,YAAY,OAAO,IAAI,eAAe,IAAI;AAGnE,YAAI,wBAAwB,YAAY,OAAO,IAAI,kBAAkB,IAAI;AACzE,YAAI,6BAA6B,YAAY,OAAO,IAAI,uBAAuB,IAAI;AACnF,YAAI,yBAAyB,YAAY,OAAO,IAAI,mBAAmB,IAAI;AAC3E,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,2BAA2B,YAAY,OAAO,IAAI,qBAAqB,IAAI;AAC/E,YAAI,kBAAkB,YAAY,OAAO,IAAI,YAAY,IAAI;AAC7D,YAAI,kBAAkB,YAAY,OAAO,IAAI,YAAY,IAAI;AAC7D,YAAI,mBAAmB,YAAY,OAAO,IAAI,aAAa,IAAI;AAC/D,YAAI,yBAAyB,YAAY,OAAO,IAAI,mBAAmB,IAAI;AAC3E,YAAI,uBAAuB,YAAY,OAAO,IAAI,iBAAiB,IAAI;AACvE,YAAI,mBAAmB,YAAY,OAAO,IAAI,aAAa,IAAI;AAE/D,iBAAS,mBAAmB,MAAM;AAChC,iBAAO,OAAO,SAAS,YAAY,OAAO,SAAS;AAAA,UACnD,SAAS,uBAAuB,SAAS,8BAA8B,SAAS,uBAAuB,SAAS,0BAA0B,SAAS,uBAAuB,SAAS,4BAA4B,OAAO,SAAS,YAAY,SAAS,SAAS,KAAK,aAAa,mBAAmB,KAAK,aAAa,mBAAmB,KAAK,aAAa,uBAAuB,KAAK,aAAa,sBAAsB,KAAK,aAAa,0BAA0B,KAAK,aAAa,0BAA0B,KAAK,aAAa,wBAAwB,KAAK,aAAa,oBAAoB,KAAK,aAAa;AAAA,QACplB;AAEA,iBAAS,OAAO,QAAQ;AACtB,cAAI,OAAO,WAAW,YAAY,WAAW,MAAM;AACjD,gBAAI,WAAW,OAAO;AAEtB,oBAAQ,UAAU;AAAA,cAChB,KAAK;AACH,oBAAI,OAAO,OAAO;AAElB,wBAAQ,MAAM;AAAA,kBACZ,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AACH,2BAAO;AAAA,kBAET;AACE,wBAAI,eAAe,QAAQ,KAAK;AAEhC,4BAAQ,cAAc;AAAA,sBACpB,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AACH,+BAAO;AAAA,sBAET;AACE,+BAAO;AAAA,oBACX;AAAA,gBAEJ;AAAA,cAEF,KAAK;AACH,uBAAO;AAAA,YACX;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAEA,YAAI,YAAY;AAChB,YAAI,iBAAiB;AACrB,YAAI,kBAAkB;AACtB,YAAI,kBAAkB;AACtB,YAAI,UAAU;AACd,YAAI,aAAa;AACjB,YAAI,WAAW;AACf,YAAI,OAAO;AACX,YAAI,OAAO;AACX,YAAI,SAAS;AACb,YAAI,WAAW;AACf,YAAI,aAAa;AACjB,YAAI,WAAW;AACf,YAAI,sCAAsC;AAE1C,iBAAS,YAAY,QAAQ;AAC3B;AACE,gBAAI,CAAC,qCAAqC;AACxC,oDAAsC;AAEtC,sBAAQ,MAAM,EAAE,+KAAyL;AAAA,YAC3M;AAAA,UACF;AAEA,iBAAO,iBAAiB,MAAM,KAAK,OAAO,MAAM,MAAM;AAAA,QACxD;AACA,iBAAS,iBAAiB,QAAQ;AAChC,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,kBAAkB,QAAQ;AACjC,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,kBAAkB,QAAQ;AACjC,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,UAAU,QAAQ;AACzB,iBAAO,OAAO,WAAW,YAAY,WAAW,QAAQ,OAAO,aAAa;AAAA,QAC9E;AACA,iBAAS,aAAa,QAAQ;AAC5B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,OAAO,QAAQ;AACtB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,OAAO,QAAQ;AACtB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,SAAS,QAAQ;AACxB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,aAAa,QAAQ;AAC5B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AAEA,gBAAQ,YAAY;AACpB,gBAAQ,iBAAiB;AACzB,gBAAQ,kBAAkB;AAC1B,gBAAQ,kBAAkB;AAC1B,gBAAQ,UAAU;AAClB,gBAAQ,aAAa;AACrB,gBAAQ,WAAW;AACnB,gBAAQ,OAAO;AACf,gBAAQ,OAAO;AACf,gBAAQ,SAAS;AACjB,gBAAQ,WAAW;AACnB,gBAAQ,aAAa;AACrB,gBAAQ,WAAW;AACnB,gBAAQ,cAAc;AACtB,gBAAQ,mBAAmB;AAC3B,gBAAQ,oBAAoB;AAC5B,gBAAQ,oBAAoB;AAC5B,gBAAQ,YAAY;AACpB,gBAAQ,eAAe;AACvB,gBAAQ,aAAa;AACrB,gBAAQ,SAAS;AACjB,gBAAQ,SAAS;AACjB,gBAAQ,WAAW;AACnB,gBAAQ,aAAa;AACrB,gBAAQ,eAAe;AACvB,gBAAQ,aAAa;AACrB,gBAAQ,qBAAqB;AAC7B,gBAAQ,SAAS;AAAA,MACf,GAAG;AAAA,IACL;AAAA;AAAA;;;ACpLA;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACNA;AAAA;AAAA;AAQA,QAAI,wBAAwB,OAAO;AACnC,QAAI,iBAAiB,OAAO,UAAU;AACtC,QAAI,mBAAmB,OAAO,UAAU;AAExC,aAAS,SAAS,KAAK;AACtB,UAAI,QAAQ,QAAQ,QAAQ,QAAW;AACtC,cAAM,IAAI,UAAU,uDAAuD;AAAA,MAC5E;AAEA,aAAO,OAAO,GAAG;AAAA,IAClB;AAEA,aAAS,kBAAkB;AAC1B,UAAI;AACH,YAAI,CAAC,OAAO,QAAQ;AACnB,iBAAO;AAAA,QACR;AAKA,YAAI,QAAQ,IAAI,OAAO,KAAK;AAC5B,cAAM,CAAC,IAAI;AACX,YAAI,OAAO,oBAAoB,KAAK,EAAE,CAAC,MAAM,KAAK;AACjD,iBAAO;AAAA,QACR;AAGA,YAAI,QAAQ,CAAC;AACb,iBAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC5B,gBAAM,MAAM,OAAO,aAAa,CAAC,CAAC,IAAI;AAAA,QACvC;AACA,YAAI,SAAS,OAAO,oBAAoB,KAAK,EAAE,IAAI,SAAU,GAAG;AAC/D,iBAAO,MAAM,CAAC;AAAA,QACf,CAAC;AACD,YAAI,OAAO,KAAK,EAAE,MAAM,cAAc;AACrC,iBAAO;AAAA,QACR;AAGA,YAAI,QAAQ,CAAC;AACb,+BAAuB,MAAM,EAAE,EAAE,QAAQ,SAAU,QAAQ;AAC1D,gBAAM,MAAM,IAAI;AAAA,QACjB,CAAC;AACD,YAAI,OAAO,KAAK,OAAO,OAAO,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,EAAE,MAC/C,wBAAwB;AACzB,iBAAO;AAAA,QACR;AAEA,eAAO;AAAA,MACR,SAAS,KAAK;AAEb,eAAO;AAAA,MACR;AAAA,IACD;AAEA,WAAO,UAAU,gBAAgB,IAAI,OAAO,SAAS,SAAU,QAAQ,QAAQ;AAC9E,UAAI;AACJ,UAAI,KAAK,SAAS,MAAM;AACxB,UAAI;AAEJ,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAC1C,eAAO,OAAO,UAAU,CAAC,CAAC;AAE1B,iBAAS,OAAO,MAAM;AACrB,cAAI,eAAe,KAAK,MAAM,GAAG,GAAG;AACnC,eAAG,GAAG,IAAI,KAAK,GAAG;AAAA,UACnB;AAAA,QACD;AAEA,YAAI,uBAAuB;AAC1B,oBAAU,sBAAsB,IAAI;AACpC,mBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACxC,gBAAI,iBAAiB,KAAK,MAAM,QAAQ,CAAC,CAAC,GAAG;AAC5C,iBAAG,QAAQ,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;AAAA,YACjC;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAAA;AAAA;;;ACzFA;AAAA;AAAA;AASA,QAAI,uBAAuB;AAE3B,WAAO,UAAU;AAAA;AAAA;;;ACXjB;AAAA;AAAA,WAAO,UAAU,SAAS,KAAK,KAAK,OAAO,UAAU,cAAc;AAAA;AAAA;;;ACAnE;AAAA;AAAA;AASA,QAAI,eAAe,WAAW;AAAA,IAAC;AAE/B,QAAI,MAAuC;AACrC,6BAAuB;AACvB,2BAAqB,CAAC;AACtB,YAAM;AAEV,qBAAe,SAAS,MAAM;AAC5B,YAAI,UAAU,cAAc;AAC5B,YAAI,OAAO,YAAY,aAAa;AAClC,kBAAQ,MAAM,OAAO;AAAA,QACvB;AACA,YAAI;AAIF,gBAAM,IAAI,MAAM,OAAO;AAAA,QACzB,SAAS,GAAG;AAAA,QAAO;AAAA,MACrB;AAAA,IACF;AAhBM;AACA;AACA;AA2BN,aAAS,eAAe,WAAW,QAAQ,UAAU,eAAe,UAAU;AAC5E,UAAI,MAAuC;AACzC,iBAAS,gBAAgB,WAAW;AAClC,cAAI,IAAI,WAAW,YAAY,GAAG;AAChC,gBAAI;AAIJ,gBAAI;AAGF,kBAAI,OAAO,UAAU,YAAY,MAAM,YAAY;AACjD,oBAAI,MAAM;AAAA,mBACP,iBAAiB,iBAAiB,OAAO,WAAW,YAAY,eAAe,+FACC,OAAO,UAAU,YAAY,IAAI;AAAA,gBAEpH;AACA,oBAAI,OAAO;AACX,sBAAM;AAAA,cACR;AACA,sBAAQ,UAAU,YAAY,EAAE,QAAQ,cAAc,eAAe,UAAU,MAAM,oBAAoB;AAAA,YAC3G,SAAS,IAAI;AACX,sBAAQ;AAAA,YACV;AACA,gBAAI,SAAS,EAAE,iBAAiB,QAAQ;AACtC;AAAA,iBACG,iBAAiB,iBAAiB,6BACnC,WAAW,OAAO,eAAe,6FAC6B,OAAO,QAAQ;AAAA,cAI/E;AAAA,YACF;AACA,gBAAI,iBAAiB,SAAS,EAAE,MAAM,WAAW,qBAAqB;AAGpE,iCAAmB,MAAM,OAAO,IAAI;AAEpC,kBAAI,QAAQ,WAAW,SAAS,IAAI;AAEpC;AAAA,gBACE,YAAY,WAAW,YAAY,MAAM,WAAW,SAAS,OAAO,QAAQ;AAAA,cAC9E;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAOA,mBAAe,oBAAoB,WAAW;AAC5C,UAAI,MAAuC;AACzC,6BAAqB,CAAC;AAAA,MACxB;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACtGjB;AAAA;AAAA;AASA,QAAI,UAAU;AACd,QAAI,SAAS;AAEb,QAAI,uBAAuB;AAC3B,QAAI,MAAM;AACV,QAAI,iBAAiB;AAErB,QAAI,eAAe,WAAW;AAAA,IAAC;AAE/B,QAAI,MAAuC;AACzC,qBAAe,SAAS,MAAM;AAC5B,YAAI,UAAU,cAAc;AAC5B,YAAI,OAAO,YAAY,aAAa;AAClC,kBAAQ,MAAM,OAAO;AAAA,QACvB;AACA,YAAI;AAIF,gBAAM,IAAI,MAAM,OAAO;AAAA,QACzB,SAAS,GAAG;AAAA,QAAC;AAAA,MACf;AAAA,IACF;AAEA,aAAS,+BAA+B;AACtC,aAAO;AAAA,IACT;AAEA,WAAO,UAAU,SAAS,gBAAgB,qBAAqB;AAE7D,UAAI,kBAAkB,OAAO,WAAW,cAAc,OAAO;AAC7D,UAAI,uBAAuB;AAgB3B,eAAS,cAAc,eAAe;AACpC,YAAI,aAAa,kBAAkB,mBAAmB,cAAc,eAAe,KAAK,cAAc,oBAAoB;AAC1H,YAAI,OAAO,eAAe,YAAY;AACpC,iBAAO;AAAA,QACT;AAAA,MACF;AAiDA,UAAI,YAAY;AAIhB,UAAI,iBAAiB;AAAA,QACnB,OAAO,2BAA2B,OAAO;AAAA,QACzC,QAAQ,2BAA2B,QAAQ;AAAA,QAC3C,MAAM,2BAA2B,SAAS;AAAA,QAC1C,MAAM,2BAA2B,UAAU;AAAA,QAC3C,QAAQ,2BAA2B,QAAQ;AAAA,QAC3C,QAAQ,2BAA2B,QAAQ;AAAA,QAC3C,QAAQ,2BAA2B,QAAQ;AAAA,QAC3C,QAAQ,2BAA2B,QAAQ;AAAA,QAE3C,KAAK,qBAAqB;AAAA,QAC1B,SAAS;AAAA,QACT,SAAS,yBAAyB;AAAA,QAClC,aAAa,6BAA6B;AAAA,QAC1C,YAAY;AAAA,QACZ,MAAM,kBAAkB;AAAA,QACxB,UAAU;AAAA,QACV,OAAO;AAAA,QACP,WAAW;AAAA,QACX,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAOA,eAAS,GAAG,GAAG,GAAG;AAEhB,YAAI,MAAM,GAAG;AAGX,iBAAO,MAAM,KAAK,IAAI,MAAM,IAAI;AAAA,QAClC,OAAO;AAEL,iBAAO,MAAM,KAAK,MAAM;AAAA,QAC1B;AAAA,MACF;AAUA,eAAS,cAAc,SAAS,MAAM;AACpC,aAAK,UAAU;AACf,aAAK,OAAO,QAAQ,OAAO,SAAS,WAAW,OAAM,CAAC;AACtD,aAAK,QAAQ;AAAA,MACf;AAEA,oBAAc,YAAY,MAAM;AAEhC,eAAS,2BAA2B,UAAU;AAC5C,YAAI,MAAuC;AACzC,cAAI,0BAA0B,CAAC;AAC/B,cAAI,6BAA6B;AAAA,QACnC;AACA,iBAAS,UAAU,YAAY,OAAO,UAAU,eAAe,UAAU,cAAc,QAAQ;AAC7F,0BAAgB,iBAAiB;AACjC,yBAAe,gBAAgB;AAE/B,cAAI,WAAW,sBAAsB;AACnC,gBAAI,qBAAqB;AAEvB,kBAAI,MAAM,IAAI;AAAA,gBACZ;AAAA,cAGF;AACA,kBAAI,OAAO;AACX,oBAAM;AAAA,YACR,WAAoD,OAAO,YAAY,aAAa;AAElF,kBAAI,WAAW,gBAAgB,MAAM;AACrC,kBACE,CAAC,wBAAwB,QAAQ;AAAA,cAEjC,6BAA6B,GAC7B;AACA;AAAA,kBACE,6EACuB,eAAe,gBAAgB,gBAAgB;AAAA,gBAIxE;AACA,wCAAwB,QAAQ,IAAI;AACpC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,cAAI,MAAM,QAAQ,KAAK,MAAM;AAC3B,gBAAI,YAAY;AACd,kBAAI,MAAM,QAAQ,MAAM,MAAM;AAC5B,uBAAO,IAAI,cAAc,SAAS,WAAW,OAAO,eAAe,8BAA8B,SAAS,gBAAgB,8BAA8B;AAAA,cAC1J;AACA,qBAAO,IAAI,cAAc,SAAS,WAAW,OAAO,eAAe,iCAAiC,MAAM,gBAAgB,mCAAmC;AAAA,YAC/J;AACA,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO,SAAS,OAAO,UAAU,eAAe,UAAU,YAAY;AAAA,UACxE;AAAA,QACF;AAEA,YAAI,mBAAmB,UAAU,KAAK,MAAM,KAAK;AACjD,yBAAiB,aAAa,UAAU,KAAK,MAAM,IAAI;AAEvD,eAAO;AAAA,MACT;AAEA,eAAS,2BAA2B,cAAc;AAChD,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc,QAAQ;AAChF,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,WAAW,YAAY,SAAS;AACpC,cAAI,aAAa,cAAc;AAI7B,gBAAI,cAAc,eAAe,SAAS;AAE1C,mBAAO,IAAI;AAAA,cACT,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,cAAc,oBAAoB,gBAAgB,mBAAmB,MAAM,eAAe;AAAA,cAC9J,EAAC,aAA0B;AAAA,YAC7B;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,uBAAuB;AAC9B,eAAO,2BAA2B,4BAA4B;AAAA,MAChE;AAEA,eAAS,yBAAyB,aAAa;AAC7C,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,OAAO,gBAAgB,YAAY;AACrC,mBAAO,IAAI,cAAc,eAAe,eAAe,qBAAqB,gBAAgB,iDAAiD;AAAA,UAC/I;AACA,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,CAAC,MAAM,QAAQ,SAAS,GAAG;AAC7B,gBAAI,WAAW,YAAY,SAAS;AACpC,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,wBAAwB;AAAA,UACtK;AACA,mBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,gBAAI,QAAQ,YAAY,WAAW,GAAG,eAAe,UAAU,eAAe,MAAM,IAAI,KAAK,oBAAoB;AACjH,gBAAI,iBAAiB,OAAO;AAC1B,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,2BAA2B;AAClC,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,CAAC,eAAe,SAAS,GAAG;AAC9B,gBAAI,WAAW,YAAY,SAAS;AACpC,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,qCAAqC;AAAA,UACnL;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,+BAA+B;AACtC,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,CAAC,QAAQ,mBAAmB,SAAS,GAAG;AAC1C,gBAAI,WAAW,YAAY,SAAS;AACpC,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,0CAA0C;AAAA,UACxL;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,0BAA0B,eAAe;AAChD,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,EAAE,MAAM,QAAQ,aAAa,gBAAgB;AAC/C,gBAAI,oBAAoB,cAAc,QAAQ;AAC9C,gBAAI,kBAAkB,aAAa,MAAM,QAAQ,CAAC;AAClD,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,kBAAkB,oBAAoB,gBAAgB,mBAAmB,kBAAkB,oBAAoB,KAAK;AAAA,UACnN;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,sBAAsB,gBAAgB;AAC7C,YAAI,CAAC,MAAM,QAAQ,cAAc,GAAG;AAClC,cAAI,MAAuC;AACzC,gBAAI,UAAU,SAAS,GAAG;AACxB;AAAA,gBACE,iEAAiE,UAAU,SAAS;AAAA,cAEtF;AAAA,YACF,OAAO;AACL,2BAAa,wDAAwD;AAAA,YACvE;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAEA,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,mBAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,gBAAI,GAAG,WAAW,eAAe,CAAC,CAAC,GAAG;AACpC,qBAAO;AAAA,YACT;AAAA,UACF;AAEA,cAAI,eAAe,KAAK,UAAU,gBAAgB,SAAS,SAAS,KAAK,OAAO;AAC9E,gBAAI,OAAO,eAAe,KAAK;AAC/B,gBAAI,SAAS,UAAU;AACrB,qBAAO,OAAO,KAAK;AAAA,YACrB;AACA,mBAAO;AAAA,UACT,CAAC;AACD,iBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,iBAAiB,OAAO,SAAS,IAAI,QAAQ,kBAAkB,gBAAgB,wBAAwB,eAAe,IAAI;AAAA,QACnM;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,0BAA0B,aAAa;AAC9C,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,OAAO,gBAAgB,YAAY;AACrC,mBAAO,IAAI,cAAc,eAAe,eAAe,qBAAqB,gBAAgB,kDAAkD;AAAA,UAChJ;AACA,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,WAAW,YAAY,SAAS;AACpC,cAAI,aAAa,UAAU;AACzB,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,yBAAyB;AAAA,UACvK;AACA,mBAAS,OAAO,WAAW;AACzB,gBAAI,IAAI,WAAW,GAAG,GAAG;AACvB,kBAAI,QAAQ,YAAY,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK,oBAAoB;AAC/G,kBAAI,iBAAiB,OAAO;AAC1B,uBAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,uBAAuB,qBAAqB;AACnD,YAAI,CAAC,MAAM,QAAQ,mBAAmB,GAAG;AACvC,iBAAwC,aAAa,wEAAwE,IAAI;AACjI,iBAAO;AAAA,QACT;AAEA,iBAAS,IAAI,GAAG,IAAI,oBAAoB,QAAQ,KAAK;AACnD,cAAI,UAAU,oBAAoB,CAAC;AACnC,cAAI,OAAO,YAAY,YAAY;AACjC;AAAA,cACE,gGACc,yBAAyB,OAAO,IAAI,eAAe,IAAI;AAAA,YACvE;AACA,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,gBAAgB,CAAC;AACrB,mBAASA,KAAI,GAAGA,KAAI,oBAAoB,QAAQA,MAAK;AACnD,gBAAIC,WAAU,oBAAoBD,EAAC;AACnC,gBAAI,gBAAgBC,SAAQ,OAAO,UAAU,eAAe,UAAU,cAAc,oBAAoB;AACxG,gBAAI,iBAAiB,MAAM;AACzB,qBAAO;AAAA,YACT;AACA,gBAAI,cAAc,QAAQ,IAAI,cAAc,MAAM,cAAc,GAAG;AACjE,4BAAc,KAAK,cAAc,KAAK,YAAY;AAAA,YACpD;AAAA,UACF;AACA,cAAI,uBAAwB,cAAc,SAAS,IAAK,6BAA6B,cAAc,KAAK,IAAI,IAAI,MAAK;AACrH,iBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,oBAAoB,MAAM,gBAAgB,MAAM,uBAAuB,IAAI;AAAA,QACpJ;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,oBAAoB;AAC3B,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,CAAC,OAAO,MAAM,QAAQ,CAAC,GAAG;AAC5B,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,oBAAoB,MAAM,gBAAgB,2BAA2B;AAAA,UAC9I;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,sBAAsB,eAAe,UAAU,cAAc,KAAK,MAAM;AAC/E,eAAO,IAAI;AAAA,WACR,iBAAiB,iBAAiB,OAAO,WAAW,YAAY,eAAe,MAAM,MAAM,+FACX,OAAO;AAAA,QAC1F;AAAA,MACF;AAEA,eAAS,uBAAuB,YAAY;AAC1C,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,WAAW,YAAY,SAAS;AACpC,cAAI,aAAa,UAAU;AACzB,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,WAAW,QAAQ,kBAAkB,gBAAgB,wBAAwB;AAAA,UACtK;AACA,mBAAS,OAAO,YAAY;AAC1B,gBAAI,UAAU,WAAW,GAAG;AAC5B,gBAAI,OAAO,YAAY,YAAY;AACjC,qBAAO,sBAAsB,eAAe,UAAU,cAAc,KAAK,eAAe,OAAO,CAAC;AAAA,YAClG;AACA,gBAAI,QAAQ,QAAQ,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK,oBAAoB;AAC3G,gBAAI,OAAO;AACT,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,6BAA6B,YAAY;AAChD,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,WAAW,YAAY,SAAS;AACpC,cAAI,aAAa,UAAU;AACzB,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,WAAW,QAAQ,kBAAkB,gBAAgB,wBAAwB;AAAA,UACtK;AAEA,cAAI,UAAU,OAAO,CAAC,GAAG,MAAM,QAAQ,GAAG,UAAU;AACpD,mBAAS,OAAO,SAAS;AACvB,gBAAI,UAAU,WAAW,GAAG;AAC5B,gBAAI,IAAI,YAAY,GAAG,KAAK,OAAO,YAAY,YAAY;AACzD,qBAAO,sBAAsB,eAAe,UAAU,cAAc,KAAK,eAAe,OAAO,CAAC;AAAA,YAClG;AACA,gBAAI,CAAC,SAAS;AACZ,qBAAO,IAAI;AAAA,gBACT,aAAa,WAAW,OAAO,eAAe,YAAY,MAAM,oBAAoB,gBAAgB,qBACjF,KAAK,UAAU,MAAM,QAAQ,GAAG,MAAM,IAAI,IAC7D,mBAAmB,KAAK,UAAU,OAAO,KAAK,UAAU,GAAG,MAAM,IAAI;AAAA,cACvE;AAAA,YACF;AACA,gBAAI,QAAQ,QAAQ,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK,oBAAoB;AAC3G,gBAAI,OAAO;AACT,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAEA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,OAAO,WAAW;AACzB,gBAAQ,OAAO,WAAW;AAAA,UACxB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,mBAAO;AAAA,UACT,KAAK;AACH,mBAAO,CAAC;AAAA,UACV,KAAK;AACH,gBAAI,MAAM,QAAQ,SAAS,GAAG;AAC5B,qBAAO,UAAU,MAAM,MAAM;AAAA,YAC/B;AACA,gBAAI,cAAc,QAAQ,eAAe,SAAS,GAAG;AACnD,qBAAO;AAAA,YACT;AAEA,gBAAI,aAAa,cAAc,SAAS;AACxC,gBAAI,YAAY;AACd,kBAAI,WAAW,WAAW,KAAK,SAAS;AACxC,kBAAI;AACJ,kBAAI,eAAe,UAAU,SAAS;AACpC,uBAAO,EAAE,OAAO,SAAS,KAAK,GAAG,MAAM;AACrC,sBAAI,CAAC,OAAO,KAAK,KAAK,GAAG;AACvB,2BAAO;AAAA,kBACT;AAAA,gBACF;AAAA,cACF,OAAO;AAEL,uBAAO,EAAE,OAAO,SAAS,KAAK,GAAG,MAAM;AACrC,sBAAI,QAAQ,KAAK;AACjB,sBAAI,OAAO;AACT,wBAAI,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG;AACrB,6BAAO;AAAA,oBACT;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF,OAAO;AACL,qBAAO;AAAA,YACT;AAEA,mBAAO;AAAA,UACT;AACE,mBAAO;AAAA,QACX;AAAA,MACF;AAEA,eAAS,SAAS,UAAU,WAAW;AAErC,YAAI,aAAa,UAAU;AACzB,iBAAO;AAAA,QACT;AAGA,YAAI,CAAC,WAAW;AACd,iBAAO;AAAA,QACT;AAGA,YAAI,UAAU,eAAe,MAAM,UAAU;AAC3C,iBAAO;AAAA,QACT;AAGA,YAAI,OAAO,WAAW,cAAc,qBAAqB,QAAQ;AAC/D,iBAAO;AAAA,QACT;AAEA,eAAO;AAAA,MACT;AAGA,eAAS,YAAY,WAAW;AAC9B,YAAI,WAAW,OAAO;AACtB,YAAI,MAAM,QAAQ,SAAS,GAAG;AAC5B,iBAAO;AAAA,QACT;AACA,YAAI,qBAAqB,QAAQ;AAI/B,iBAAO;AAAA,QACT;AACA,YAAI,SAAS,UAAU,SAAS,GAAG;AACjC,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAIA,eAAS,eAAe,WAAW;AACjC,YAAI,OAAO,cAAc,eAAe,cAAc,MAAM;AAC1D,iBAAO,KAAK;AAAA,QACd;AACA,YAAI,WAAW,YAAY,SAAS;AACpC,YAAI,aAAa,UAAU;AACzB,cAAI,qBAAqB,MAAM;AAC7B,mBAAO;AAAA,UACT,WAAW,qBAAqB,QAAQ;AACtC,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAIA,eAAS,yBAAyB,OAAO;AACvC,YAAI,OAAO,eAAe,KAAK;AAC/B,gBAAQ,MAAM;AAAA,UACZ,KAAK;AAAA,UACL,KAAK;AACH,mBAAO,QAAQ;AAAA,UACjB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,mBAAO,OAAO;AAAA,UAChB;AACE,mBAAO;AAAA,QACX;AAAA,MACF;AAGA,eAAS,aAAa,WAAW;AAC/B,YAAI,CAAC,UAAU,eAAe,CAAC,UAAU,YAAY,MAAM;AACzD,iBAAO;AAAA,QACT;AACA,eAAO,UAAU,YAAY;AAAA,MAC/B;AAEA,qBAAe,iBAAiB;AAChC,qBAAe,oBAAoB,eAAe;AAClD,qBAAe,YAAY;AAE3B,aAAO;AAAA,IACT;AAAA;AAAA;;;ACjmBA;AAAA;AAOA,QAAI,MAAuC;AACrC,gBAAU;AAIV,4BAAsB;AAC1B,aAAO,UAAU,kCAAqC,QAAQ,WAAW,mBAAmB;AAAA,IAC9F,OAAO;AAGL,aAAO,UAAU,KAAsC;AAAA,IACzD;AAVM;AAIA;AAAA;AAAA;;;ACZN;AAAA;AAAA,WAAO,UAAU;AAAA,MAChB,GAAI;AAAA,MACJ,GAAI;AAAA,MACJ,GAAI;AAAA,MACJ,GAAI;AAAA,IACL;AAAA;AAAA;;;ACLA;AAAA;AAAA,WAAO,UAAU;AAAA,MAChB,aAAe,KAAK;AAAA,MACpB,gBAAkB,KAAK;AAAA,MACvB,gBAAkB,KAAK;AAAA,MACvB,YAAc,KAAK;AAAA,IACpB;AAAA;AAAA;;;ACLA;AAAA;AAAA,QAAI,OAAO;AAEX,aAAS,WAAW,MAAM;AACzB,WAAK,OAAO,KAAK;AACjB,WAAK,OAAO;AAAA,IACb;AAEA,eAAW,YAAY;AAAA,MAEtB,WAAY,SAAS,QAAQ;AAC5B,eAAO,KAAK,KAAK;AAAA,MAClB;AAAA,MAEA,OAAQ,SAAS,QAAQ;AACxB,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK;AAE1C,iBAAO,IAAI,KAAK,KAAK,WAAW,CAAC,GAAG,CAAC;AAAA,QACtC;AAAA,MACD;AAAA,IACD;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACrBjB;AAAA;AACA,QAAI,MAAM;AAEV,aAAS,UAAU,YAAY,WAAW;AACzC,WAAK,aAAa;AAClB,WAAK,YAAa;AAAA,IACnB;AAEA,cAAU,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQ1B,CAAC,GAAG,IAAI,EAAE;AAAA,MACV,CAAC,GAAG,IAAI,EAAE;AAAA,MACV,CAAC,GAAG,IAAI,EAAE;AAAA,MACV,CAAC,GAAG,IAAI,CAAC;AAAA;AAAA,MAGT,CAAC,GAAG,IAAI,EAAE;AAAA,MACV,CAAC,GAAG,IAAI,EAAE;AAAA,MACV,CAAC,GAAG,IAAI,EAAE;AAAA,MACV,CAAC,GAAG,IAAI,EAAE;AAAA;AAAA,MAGV,CAAC,GAAG,IAAI,EAAE;AAAA,MACV,CAAC,GAAG,IAAI,EAAE;AAAA,MACV,CAAC,GAAG,IAAI,EAAE;AAAA,MACV,CAAC,GAAG,IAAI,EAAE;AAAA;AAAA,MAGV,CAAC,GAAG,KAAK,EAAE;AAAA,MACX,CAAC,GAAG,IAAI,EAAE;AAAA,MACV,CAAC,GAAG,IAAI,EAAE;AAAA,MACV,CAAC,GAAG,IAAI,CAAC;AAAA;AAAA,MAGT,CAAC,GAAG,KAAK,GAAG;AAAA,MACZ,CAAC,GAAG,IAAI,EAAE;AAAA,MACV,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,MAGrB,CAAC,GAAG,IAAI,EAAE;AAAA,MACV,CAAC,GAAG,IAAI,EAAE;AAAA,MACV,CAAC,GAAG,IAAI,EAAE;AAAA,MACV,CAAC,GAAG,IAAI,EAAE;AAAA;AAAA,MAGV,CAAC,GAAG,IAAI,EAAE;AAAA,MACV,CAAC,GAAG,IAAI,EAAE;AAAA,MACV,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,MAGrB,CAAC,GAAG,KAAK,EAAE;AAAA,MACX,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,MAGrB,CAAC,GAAG,KAAK,GAAG;AAAA,MACZ,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,MAGrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,MAGrB,CAAC,GAAG,KAAK,EAAE;AAAA,MACX,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,MAGrB,CAAC,GAAG,KAAK,IAAI,GAAG,KAAK,EAAE;AAAA,MACvB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,MAGrB,CAAC,GAAG,KAAK,GAAG;AAAA,MACZ,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,MAGtB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MACzB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,MAGtB,CAAC,GAAG,KAAK,IAAI,GAAG,KAAK,EAAE;AAAA,MACvB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,IAAI,IAAI,EAAE;AAAA;AAAA,MAGX,CAAC,GAAG,KAAK,IAAI,GAAG,KAAK,EAAE;AAAA,MACvB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGtB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MACzB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGtB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MACzB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACrB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGtB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MACzB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGtB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MACzB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGvB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MACzB,CAAC,IAAI,IAAI,EAAE;AAAA,MACX,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,MAGtB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MACzB,CAAC,IAAI,IAAI,EAAE;AAAA,MACX,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,EAAE;AAAA;AAAA,MAGX,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MACzB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGvB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MACzB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,MAGtB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MACzB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGvB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MAC1B,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,MAGtB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MACzB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGvB,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,GAAG;AAAA,MAC1B,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGvB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MACzB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGvB,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,GAAG;AAAA,MAC1B,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGvB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MAC1B,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGvB,CAAC,IAAI,KAAK,GAAG;AAAA,MACb,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGvB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MAC1B,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGvB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MAC1B,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA;AAAA,MAGtB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MAC1B,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGvB,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,GAAG;AAAA,MAC1B,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGtB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MAC1B,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGvB,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,GAAG;AAAA,MAC1B,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGvB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MAC1B,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA;AAAA,MAGvB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG;AAAA,MAC1B,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,IACxB;AAEA,cAAU,cAAc,SAAS,YAAY,mBAAmB;AAE/D,UAAI,UAAU,UAAU,gBAAgB,YAAY,iBAAiB;AAErE,UAAI,WAAW,QAAW;AACzB,cAAM,IAAI,MAAM,+BAA+B,aAAa,wBAAwB,iBAAiB;AAAA,MACtG;AAEA,UAAI,SAAS,QAAQ,SAAS;AAE9B,UAAI,OAAO,IAAI,MAAM;AAErB,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAEhC,YAAI,QAAQ,QAAQ,IAAI,IAAI,CAAC;AAC7B,YAAI,aAAa,QAAQ,IAAI,IAAI,CAAC;AAClC,YAAI,YAAa,QAAQ,IAAI,IAAI,CAAC;AAElC,iBAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC/B,eAAK,KAAK,IAAI,UAAU,YAAY,SAAS,CAAE;AAAA,QAChD;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAEA,cAAU,kBAAkB,SAAS,YAAY,mBAAmB;AAEnE,cAAO,mBAAmB;AAAA,QAC1B,KAAK,IAAI;AACR,iBAAO,UAAU,gBAAgB,aAAa,KAAK,IAAI,CAAC;AAAA,QACzD,KAAK,IAAI;AACR,iBAAO,UAAU,gBAAgB,aAAa,KAAK,IAAI,CAAC;AAAA,QACzD,KAAK,IAAI;AACR,iBAAO,UAAU,gBAAgB,aAAa,KAAK,IAAI,CAAC;AAAA,QACzD,KAAK,IAAI;AACR,iBAAO,UAAU,gBAAgB,aAAa,KAAK,IAAI,CAAC;AAAA,QACzD;AACC,iBAAO;AAAA,MACR;AAAA,IACD;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC1SjB;AAAA;AAAA,aAAS,cAAc;AACtB,WAAK,SAAS,IAAI,MAAM;AACxB,WAAK,SAAS;AAAA,IACf;AAEA,gBAAY,YAAY;AAAA,MAEvB,KAAM,SAAS,OAAO;AACrB,YAAI,WAAW,KAAK,MAAM,QAAQ,CAAC;AACnC,gBAAU,KAAK,OAAO,QAAQ,MAAO,IAAI,QAAQ,IAAO,MAAM;AAAA,MAC/D;AAAA,MAEA,KAAM,SAAS,KAAK,QAAQ;AAC3B,iBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,eAAK,QAAW,QAAS,SAAS,IAAI,IAAO,MAAM,CAAC;AAAA,QACrD;AAAA,MACD;AAAA,MAEA,iBAAkB,WAAW;AAC5B,eAAO,KAAK;AAAA,MACb;AAAA,MAEA,QAAS,SAAS,KAAK;AAEtB,YAAI,WAAW,KAAK,MAAM,KAAK,SAAS,CAAC;AACzC,YAAI,KAAK,OAAO,UAAU,UAAU;AACnC,eAAK,OAAO,KAAK,CAAC;AAAA,QACnB;AAEA,YAAI,KAAK;AACR,eAAK,OAAO,QAAQ,KAAM,QAAU,KAAK,SAAS;AAAA,QACnD;AAEA,aAAK;AAAA,MACN;AAAA,IACD;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACrCjB;AAAA;AAAA,QAAI,SAAS;AAAA,MAEZ,MAAO,SAAS,GAAG;AAElB,YAAI,IAAI,GAAG;AACV,gBAAM,IAAI,MAAM,UAAU,IAAI,GAAG;AAAA,QAClC;AAEA,eAAO,OAAO,UAAU,CAAC;AAAA,MAC1B;AAAA,MAEA,MAAO,SAAS,GAAG;AAElB,eAAO,IAAI,GAAG;AACb,eAAK;AAAA,QACN;AAEA,eAAO,KAAK,KAAK;AAChB,eAAK;AAAA,QACN;AAEA,eAAO,OAAO,UAAU,CAAC;AAAA,MAC1B;AAAA,MAEA,WAAY,IAAI,MAAM,GAAG;AAAA,MAEzB,WAAY,IAAI,MAAM,GAAG;AAAA,IAE1B;AAEA,SAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC3B,aAAO,UAAU,CAAC,IAAI,KAAK;AAAA,IAC5B;AAFS;AAGT,SAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC7B,aAAO,UAAU,CAAC,IAAI,OAAO,UAAU,IAAI,CAAC,IACzC,OAAO,UAAU,IAAI,CAAC,IACtB,OAAO,UAAU,IAAI,CAAC,IACtB,OAAO,UAAU,IAAI,CAAC;AAAA,IAC1B;AALS;AAMT,SAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC7B,aAAO,UAAU,OAAO,UAAU,CAAC,CAAE,IAAI;AAAA,IAC1C;AAFS;AAIT,WAAO,UAAU;AAAA;AAAA;;;AC3CjB;AAAA;AAAA,QAAI,OAAO;AAEX,aAAS,aAAa,KAAK,OAAO;AAEjC,UAAI,IAAI,UAAU,QAAW;AAC5B,cAAM,IAAI,MAAM,IAAI,SAAS,MAAM,KAAK;AAAA,MACzC;AAEA,UAAI,SAAS;AAEb,aAAO,SAAS,IAAI,UAAU,IAAI,MAAM,KAAK,GAAG;AAC/C;AAAA,MACD;AAEA,WAAK,MAAM,IAAI,MAAM,IAAI,SAAS,SAAS,KAAK;AAChD,eAAS,IAAI,GAAG,IAAI,IAAI,SAAS,QAAQ,KAAK;AAC7C,aAAK,IAAI,CAAC,IAAI,IAAI,IAAI,MAAM;AAAA,MAC7B;AAAA,IACD;AAEA,iBAAa,YAAY;AAAA,MAExB,KAAM,SAAS,OAAO;AACrB,eAAO,KAAK,IAAI,KAAK;AAAA,MACtB;AAAA,MAEA,WAAY,WAAW;AACtB,eAAO,KAAK,IAAI;AAAA,MACjB;AAAA,MAEA,UAAW,SAAS,GAAG;AAEtB,YAAI,MAAM,IAAI,MAAM,KAAK,UAAU,IAAI,EAAE,UAAU,IAAI,CAAC;AAExD,iBAAS,IAAI,GAAG,IAAI,KAAK,UAAU,GAAG,KAAK;AAC1C,mBAAS,IAAI,GAAG,IAAI,EAAE,UAAU,GAAG,KAAK;AACvC,gBAAI,IAAI,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,CAAC,CAAE,IAAI,KAAK,KAAK,EAAE,IAAI,CAAC,CAAE,CAAE;AAAA,UACxE;AAAA,QACD;AAEA,eAAO,IAAI,aAAa,KAAK,CAAC;AAAA,MAC/B;AAAA,MAEA,KAAM,SAAS,GAAG;AAEjB,YAAI,KAAK,UAAU,IAAI,EAAE,UAAU,IAAI,GAAG;AACzC,iBAAO;AAAA,QACR;AAEA,YAAI,QAAQ,KAAK,KAAK,KAAK,IAAI,CAAC,CAAE,IAAI,KAAK,KAAK,EAAE,IAAI,CAAC,CAAE;AAEzD,YAAI,MAAM,IAAI,MAAM,KAAK,UAAU,CAAE;AAErC,iBAAS,IAAI,GAAG,IAAI,KAAK,UAAU,GAAG,KAAK;AAC1C,cAAI,CAAC,IAAI,KAAK,IAAI,CAAC;AAAA,QACpB;AAEA,iBAAS,IAAI,GAAG,IAAI,EAAE,UAAU,GAAG,KAAK;AACvC,cAAI,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE,IAAI,CAAC,CAAE,IAAI,KAAK;AAAA,QACjD;AAGA,eAAO,IAAI,aAAa,KAAK,CAAC,EAAE,IAAI,CAAC;AAAA,MACtC;AAAA,IACD;AAEA,WAAO,UAAU;AAAA;AAAA;;;AClEjB;AAAA;AAAA,QAAI,OAAO;AACX,QAAI,aAAa;AACjB,QAAI,OAAO;AAEX,QAAI,gBAAgB;AAAA,MACnB,YAAa;AAAA,MACb,YAAa;AAAA,MACb,YAAa;AAAA,MACb,YAAa;AAAA,MACb,YAAa;AAAA,MACb,YAAa;AAAA,MACb,YAAa;AAAA,MACb,YAAa;AAAA,IACd;AAEA,QAAI,SAAS;AAAA,MAET,wBAAyB;AAAA,QACxB,CAAC;AAAA,QACD,CAAC,GAAG,EAAE;AAAA,QACN,CAAC,GAAG,EAAE;AAAA,QACN,CAAC,GAAG,EAAE;AAAA,QACN,CAAC,GAAG,EAAE;AAAA,QACN,CAAC,GAAG,EAAE;AAAA,QACN,CAAC,GAAG,IAAI,EAAE;AAAA,QACV,CAAC,GAAG,IAAI,EAAE;AAAA,QACV,CAAC,GAAG,IAAI,EAAE;AAAA,QACV,CAAC,GAAG,IAAI,EAAE;AAAA,QACV,CAAC,GAAG,IAAI,EAAE;AAAA,QACV,CAAC,GAAG,IAAI,EAAE;AAAA,QACV,CAAC,GAAG,IAAI,EAAE;AAAA,QACV,CAAC,GAAG,IAAI,IAAI,EAAE;AAAA,QACd,CAAC,GAAG,IAAI,IAAI,EAAE;AAAA,QACd,CAAC,GAAG,IAAI,IAAI,EAAE;AAAA,QACd,CAAC,GAAG,IAAI,IAAI,EAAE;AAAA,QACd,CAAC,GAAG,IAAI,IAAI,EAAE;AAAA,QACd,CAAC,GAAG,IAAI,IAAI,EAAE;AAAA,QACd,CAAC,GAAG,IAAI,IAAI,EAAE;AAAA,QACd,CAAC,GAAG,IAAI,IAAI,IAAI,EAAE;AAAA,QAClB,CAAC,GAAG,IAAI,IAAI,IAAI,EAAE;AAAA,QAClB,CAAC,GAAG,IAAI,IAAI,IAAI,GAAG;AAAA,QACnB,CAAC,GAAG,IAAI,IAAI,IAAI,GAAG;AAAA,QACnB,CAAC,GAAG,IAAI,IAAI,IAAI,GAAG;AAAA,QACnB,CAAC,GAAG,IAAI,IAAI,IAAI,GAAG;AAAA,QACnB,CAAC,GAAG,IAAI,IAAI,IAAI,GAAG;AAAA,QACnB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG;AAAA,QACvB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,GAAG;AAAA,QACxB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,GAAG;AAAA,QACxB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,GAAG;AAAA,QACxB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,GAAG;AAAA,QACxB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,GAAG;AAAA,QACxB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,GAAG;AAAA,QACxB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG;AAAA,QAC7B,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG;AAAA,QAC7B,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG;AAAA,QAC7B,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG;AAAA,QAC7B,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG;AAAA,QAC7B,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG;AAAA,MAC9B;AAAA,MAEA,KAAO,KAAK,KAAO,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK;AAAA,MAC/E,KAAO,KAAK,KAAO,KAAK,KAAO,KAAK,KAAO,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK;AAAA,MAC5F,UAAY,KAAK,KAAO,KAAK,KAAO,KAAK,KAAO,KAAK,IAAM,KAAK;AAAA,MAEhE,gBAAiB,SAAS,MAAM;AAC/B,YAAI,IAAI,QAAQ;AAChB,eAAO,OAAO,YAAY,CAAC,IAAI,OAAO,YAAY,OAAO,GAAG,KAAK,GAAG;AACnE,eAAM,OAAO,OAAQ,OAAO,YAAY,CAAC,IAAI,OAAO,YAAY,OAAO,GAAG;AAAA,QAC3E;AACA,gBAAU,QAAQ,KAAM,KAAK,OAAO;AAAA,MACrC;AAAA,MAEA,kBAAmB,SAAS,MAAM;AACjC,YAAI,IAAI,QAAQ;AAChB,eAAO,OAAO,YAAY,CAAC,IAAI,OAAO,YAAY,OAAO,GAAG,KAAK,GAAG;AACnE,eAAM,OAAO,OAAQ,OAAO,YAAY,CAAC,IAAI,OAAO,YAAY,OAAO,GAAG;AAAA,QAC3E;AACA,eAAQ,QAAQ,KAAM;AAAA,MACvB;AAAA,MAEA,aAAc,SAAS,MAAM;AAE5B,YAAI,QAAQ;AAEZ,eAAO,QAAQ,GAAG;AACjB;AACA,oBAAU;AAAA,QACX;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,oBAAqB,SAAS,YAAY;AACzC,eAAO,OAAO,uBAAuB,aAAa,CAAC;AAAA,MACpD;AAAA,MAEA,SAAU,SAAS,aAAa,GAAG,GAAG;AAErC,gBAAQ,aAAa;AAAA,UAErB,KAAK,cAAc;AAAa,oBAAQ,IAAI,KAAK,KAAK;AAAA,UACtD,KAAK,cAAc;AAAa,mBAAO,IAAI,KAAK;AAAA,UAChD,KAAK,cAAc;AAAa,mBAAO,IAAI,KAAK;AAAA,UAChD,KAAK,cAAc;AAAa,oBAAQ,IAAI,KAAK,KAAK;AAAA,UACtD,KAAK,cAAc;AAAa,oBAAQ,KAAK,MAAM,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,KAAM,KAAK;AAAA,UACvF,KAAK,cAAc;AAAa,mBAAQ,IAAI,IAAK,IAAK,IAAI,IAAK,KAAK;AAAA,UACpE,KAAK,cAAc;AAAa,oBAAU,IAAI,IAAK,IAAK,IAAI,IAAK,KAAK,KAAK;AAAA,UAC3E,KAAK,cAAc;AAAa,oBAAU,IAAI,IAAK,KAAK,IAAI,KAAK,KAAK,KAAK;AAAA,UAE3E;AACC,kBAAM,IAAI,MAAM,qBAAqB,WAAW;AAAA,QACjD;AAAA,MACD;AAAA,MAEA,2BAA4B,SAAS,oBAAoB;AAExD,YAAI,IAAI,IAAI,WAAW,CAAC,CAAC,GAAG,CAAC;AAE7B,iBAAS,IAAI,GAAG,IAAI,oBAAoB,KAAK;AAC5C,cAAI,EAAE,SAAS,IAAI,WAAW,CAAC,GAAG,KAAK,KAAK,CAAC,CAAC,GAAG,CAAC,CAAE;AAAA,QACrD;AAEA,eAAO;AAAA,MACR;AAAA,MAEA,iBAAkB,SAAS,MAAM,MAAM;AAEtC,YAAI,KAAK,QAAQ,OAAO,IAAI;AAI3B,kBAAO,MAAM;AAAA,YACb,KAAK,KAAK;AAAe,qBAAO;AAAA,YAChC,KAAK,KAAK;AAAkB,qBAAO;AAAA,YACnC,KAAK,KAAK;AAAiB,qBAAO;AAAA,YAClC,KAAK,KAAK;AAAe,qBAAO;AAAA,YAChC;AACC,oBAAM,IAAI,MAAM,UAAU,IAAI;AAAA,UAC/B;AAAA,QAED,WAAW,OAAO,IAAI;AAIrB,kBAAO,MAAM;AAAA,YACb,KAAK,KAAK;AAAe,qBAAO;AAAA,YAChC,KAAK,KAAK;AAAkB,qBAAO;AAAA,YACnC,KAAK,KAAK;AAAiB,qBAAO;AAAA,YAClC,KAAK,KAAK;AAAe,qBAAO;AAAA,YAChC;AACC,oBAAM,IAAI,MAAM,UAAU,IAAI;AAAA,UAC/B;AAAA,QAED,WAAW,OAAO,IAAI;AAIrB,kBAAO,MAAM;AAAA,YACb,KAAK,KAAK;AAAe,qBAAO;AAAA,YAChC,KAAK,KAAK;AAAiB,qBAAO;AAAA,YAClC,KAAK,KAAK;AAAiB,qBAAO;AAAA,YAClC,KAAK,KAAK;AAAe,qBAAO;AAAA,YAChC;AACC,oBAAM,IAAI,MAAM,UAAU,IAAI;AAAA,UAC/B;AAAA,QAED,OAAO;AACN,gBAAM,IAAI,MAAM,UAAU,IAAI;AAAA,QAC/B;AAAA,MACD;AAAA,MAEA,cAAe,SAAS,QAAQ;AAE/B,YAAI,cAAc,OAAO,eAAe;AAExC,YAAI,YAAY;AAIhB,iBAAS,MAAM,GAAG,MAAM,aAAa,OAAO;AAE3C,mBAAS,MAAM,GAAG,MAAM,aAAa,OAAO;AAE3C,gBAAI,YAAY;AAChB,gBAAI,OAAO,OAAO,OAAO,KAAK,GAAG;AAEpC,qBAAS,IAAI,IAAI,KAAK,GAAG,KAAK;AAE1B,kBAAI,MAAM,IAAI,KAAK,eAAe,MAAM,GAAG;AAC1C;AAAA,cACD;AAEA,uBAAS,IAAI,IAAI,KAAK,GAAG,KAAK;AAE7B,oBAAI,MAAM,IAAI,KAAK,eAAe,MAAM,GAAG;AAC1C;AAAA,gBACD;AAEA,oBAAI,KAAK,KAAK,KAAK,GAAG;AACrB;AAAA,gBACD;AAEA,oBAAI,QAAQ,OAAO,OAAO,MAAM,GAAG,MAAM,CAAC,GAAI;AAC7C;AAAA,gBACD;AAAA,cACD;AAAA,YACD;AAEA,gBAAI,YAAY,GAAG;AAClB,2BAAc,IAAI,YAAY;AAAA,YAC/B;AAAA,UACD;AAAA,QACD;AAIA,iBAAS,MAAM,GAAG,MAAM,cAAc,GAAG,OAAO;AAC/C,mBAAS,MAAM,GAAG,MAAM,cAAc,GAAG,OAAO;AAC/C,gBAAI,QAAQ;AACZ,gBAAI,OAAO,OAAO,KAAS,GAAO,EAAI;AACtC,gBAAI,OAAO,OAAO,MAAM,GAAG,GAAO,EAAI;AACtC,gBAAI,OAAO,OAAO,KAAS,MAAM,CAAC,EAAI;AACtC,gBAAI,OAAO,OAAO,MAAM,GAAG,MAAM,CAAC,EAAI;AACtC,gBAAI,SAAS,KAAK,SAAS,GAAG;AAC7B,2BAAa;AAAA,YACd;AAAA,UACD;AAAA,QACD;AAIA,iBAAS,MAAM,GAAG,MAAM,aAAa,OAAO;AAC3C,mBAAS,MAAM,GAAG,MAAM,cAAc,GAAG,OAAO;AAC/C,gBAAI,OAAO,OAAO,KAAK,GAAG,KACrB,CAAC,OAAO,OAAO,KAAK,MAAM,CAAC,KAC1B,OAAO,OAAO,KAAK,MAAM,CAAC,KAC1B,OAAO,OAAO,KAAK,MAAM,CAAC,KAC1B,OAAO,OAAO,KAAK,MAAM,CAAC,KAC3B,CAAC,OAAO,OAAO,KAAK,MAAM,CAAC,KAC1B,OAAO,OAAO,KAAK,MAAM,CAAC,GAAI;AACnC,2BAAa;AAAA,YACd;AAAA,UACD;AAAA,QACD;AAEA,iBAAS,MAAM,GAAG,MAAM,aAAa,OAAO;AAC3C,mBAAS,MAAM,GAAG,MAAM,cAAc,GAAG,OAAO;AAC/C,gBAAI,OAAO,OAAO,KAAK,GAAG,KACrB,CAAC,OAAO,OAAO,MAAM,GAAG,GAAG,KAC1B,OAAO,OAAO,MAAM,GAAG,GAAG,KAC1B,OAAO,OAAO,MAAM,GAAG,GAAG,KAC1B,OAAO,OAAO,MAAM,GAAG,GAAG,KAC3B,CAAC,OAAO,OAAO,MAAM,GAAG,GAAG,KAC1B,OAAO,OAAO,MAAM,GAAG,GAAG,GAAI;AACnC,2BAAa;AAAA,YACd;AAAA,UACD;AAAA,QACD;AAIA,YAAI,YAAY;AAEhB,iBAAS,MAAM,GAAG,MAAM,aAAa,OAAO;AAC3C,mBAAS,MAAM,GAAG,MAAM,aAAa,OAAO;AAC3C,gBAAI,OAAO,OAAO,KAAK,GAAG,GAAI;AAC7B;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAEA,YAAI,QAAQ,KAAK,IAAI,MAAM,YAAY,cAAc,cAAc,EAAE,IAAI;AACzE,qBAAa,QAAQ;AAErB,eAAO;AAAA,MACR;AAAA,IACJ;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACtRjB;AAAA;AAAA,QAAI,UAAU;AACd,QAAI,UAAU;AACd,QAAI,YAAY;AAChB,QAAI,OAAO;AACX,QAAI,aAAa;AAEjB,aAAS,OAAO,YAAY,mBAAmB;AAC9C,WAAK,aAAa;AAClB,WAAK,oBAAoB;AACzB,WAAK,UAAU;AACf,WAAK,cAAc;AACnB,WAAK,YAAY;AACjB,WAAK,WAAW,CAAC;AAAA,IAClB;AAGA,QAAI,QAAQ,OAAO;AAEnB,UAAM,UAAU,SAAS,MAAM;AAC9B,UAAI,UAAU,IAAI,QAAQ,IAAI;AAC9B,WAAK,SAAS,KAAK,OAAO;AAC1B,WAAK,YAAY;AAAA,IAClB;AAEA,UAAM,SAAS,SAAS,KAAK,KAAK;AACjC,UAAI,MAAM,KAAK,KAAK,eAAe,OAAO,MAAM,KAAK,KAAK,eAAe,KAAK;AAC7E,cAAM,IAAI,MAAM,MAAM,MAAM,GAAG;AAAA,MAChC;AACA,aAAO,KAAK,QAAQ,GAAG,EAAE,GAAG;AAAA,IAC7B;AAEA,UAAM,iBAAiB,WAAW;AACjC,aAAO,KAAK;AAAA,IACb;AAEA,UAAM,OAAO,WAAW;AAEvB,UAAI,KAAK,aAAa,GAAG;AACxB,YAAI,aAAa;AACjB,aAAK,aAAa,GAAG,aAAa,IAAI,cAAc;AACnD,cAAI,WAAW,QAAQ,YAAY,YAAY,KAAK,iBAAiB;AAErE,cAAI,SAAS,IAAI,UAAU;AAC3B,cAAI,iBAAiB;AACrB,mBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACzC,8BAAkB,SAAS,CAAC,EAAE;AAAA,UAC/B;AAEA,mBAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC9C,gBAAI,OAAO,KAAK,SAAS,CAAC;AAC1B,mBAAO,IAAI,KAAK,MAAM,CAAC;AACvB,mBAAO,IAAI,KAAK,UAAU,GAAG,KAAK,gBAAgB,KAAK,MAAM,UAAU,CAAE;AACzE,iBAAK,MAAM,MAAM;AAAA,UAClB;AACA,cAAI,OAAO,gBAAgB,KAAK,iBAAiB;AAChD;AAAA,QACF;AACA,aAAK,aAAa;AAAA,MACnB;AACA,WAAK,SAAS,OAAO,KAAK,mBAAmB,CAAE;AAAA,IAChD;AAEA,UAAM,WAAW,SAAS,MAAM,aAAa;AAE5C,WAAK,cAAc,KAAK,aAAa,IAAI;AACzC,WAAK,UAAU,IAAI,MAAM,KAAK,WAAW;AAEzC,eAAS,MAAM,GAAG,MAAM,KAAK,aAAa,OAAO;AAEhD,aAAK,QAAQ,GAAG,IAAI,IAAI,MAAM,KAAK,WAAW;AAE9C,iBAAS,MAAM,GAAG,MAAM,KAAK,aAAa,OAAO;AAChD,eAAK,QAAQ,GAAG,EAAE,GAAG,IAAI;AAAA,QAC1B;AAAA,MACD;AAEA,WAAK,0BAA0B,GAAG,CAAC;AACnC,WAAK,0BAA0B,KAAK,cAAc,GAAG,CAAC;AACtD,WAAK,0BAA0B,GAAG,KAAK,cAAc,CAAC;AACtD,WAAK,2BAA2B;AAChC,WAAK,mBAAmB;AACxB,WAAK,cAAc,MAAM,WAAW;AAEpC,UAAI,KAAK,cAAc,GAAG;AACzB,aAAK,gBAAgB,IAAI;AAAA,MAC1B;AAEA,UAAI,KAAK,aAAa,MAAM;AAC3B,aAAK,YAAY,OAAO,WAAW,KAAK,YAAY,KAAK,mBAAmB,KAAK,QAAQ;AAAA,MAC1F;AAEA,WAAK,QAAQ,KAAK,WAAW,WAAW;AAAA,IACzC;AAEA,UAAM,4BAA4B,SAAS,KAAK,KAAM;AAErD,eAAS,IAAI,IAAI,KAAK,GAAG,KAAK;AAE7B,YAAI,MAAM,KAAK,MAAM,KAAK,eAAe,MAAM,EAAG;AAElD,iBAAS,IAAI,IAAI,KAAK,GAAG,KAAK;AAE7B,cAAI,MAAM,KAAK,MAAM,KAAK,eAAe,MAAM,EAAG;AAElD,cAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MACpC,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MACpC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAK;AAC7C,iBAAK,QAAQ,MAAM,CAAC,EAAE,MAAM,CAAC,IAAI;AAAA,UAClC,OAAO;AACN,iBAAK,QAAQ,MAAM,CAAC,EAAE,MAAM,CAAC,IAAI;AAAA,UAClC;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,UAAM,qBAAqB,WAAW;AAErC,UAAI,eAAe;AACnB,UAAI,UAAU;AAEd,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAE3B,aAAK,SAAS,MAAM,CAAC;AAErB,YAAI,YAAY,KAAK,aAAa,IAAI;AAEtC,YAAI,KAAK,KAAK,eAAgB,WAAW;AACxC,yBAAe;AACf,oBAAU;AAAA,QACX;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAEA,UAAM,kBAAkB,SAAS,WAAW,eAAe,OAAO;AAEjE,UAAI,QAAQ,UAAU,qBAAqB,eAAe,KAAK;AAC/D,UAAI,KAAK;AAET,WAAK,KAAK;AAEV,eAAS,MAAM,GAAG,MAAM,KAAK,QAAQ,QAAQ,OAAO;AAEnD,YAAI,IAAI,MAAM;AAEd,iBAAS,MAAM,GAAG,MAAM,KAAK,QAAQ,GAAG,EAAE,QAAQ,OAAO;AAExD,cAAI,IAAI,MAAM;AACd,cAAI,OAAO,KAAK,QAAQ,GAAG,EAAE,GAAG;AAEhC,cAAI,MAAM;AACT,kBAAM,UAAU,GAAG,GAAG;AACtB,kBAAM,OAAO,GAAG,CAAC;AACjB,kBAAM,OAAO,IAAI,IAAI,CAAC;AACtB,kBAAM,OAAO,IAAI,IAAI,IAAI,EAAE;AAC3B,kBAAM,OAAO,GAAG,IAAI,EAAE;AACtB,kBAAM,QAAQ;AAAA,UACf;AAAA,QACD;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAEA,UAAM,qBAAqB,WAAW;AAErC,eAAS,IAAI,GAAG,IAAI,KAAK,cAAc,GAAG,KAAK;AAC9C,YAAI,KAAK,QAAQ,CAAC,EAAE,CAAC,KAAK,MAAM;AAC/B;AAAA,QACD;AACA,aAAK,QAAQ,CAAC,EAAE,CAAC,IAAK,IAAI,KAAK;AAAA,MAChC;AAEA,eAAS,IAAI,GAAG,IAAI,KAAK,cAAc,GAAG,KAAK;AAC9C,YAAI,KAAK,QAAQ,CAAC,EAAE,CAAC,KAAK,MAAM;AAC/B;AAAA,QACD;AACA,aAAK,QAAQ,CAAC,EAAE,CAAC,IAAK,IAAI,KAAK;AAAA,MAChC;AAAA,IACD;AAEA,UAAM,6BAA6B,WAAW;AAE7C,UAAI,MAAM,KAAK,mBAAmB,KAAK,UAAU;AAEjD,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AAEpC,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AAEpC,cAAI,MAAM,IAAI,CAAC;AACf,cAAI,MAAM,IAAI,CAAC;AAEf,cAAI,KAAK,QAAQ,GAAG,EAAE,GAAG,KAAK,MAAM;AACnC;AAAA,UACD;AAEA,mBAAS,IAAI,IAAI,KAAK,GAAG,KAAK;AAE7B,qBAAS,IAAI,IAAI,KAAK,GAAG,KAAK;AAE7B,kBAAI,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KACnC,KAAK,KAAK,KAAK,GAAK;AACzB,qBAAK,QAAQ,MAAM,CAAC,EAAE,MAAM,CAAC,IAAI;AAAA,cAClC,OAAO;AACN,qBAAK,QAAQ,MAAM,CAAC,EAAE,MAAM,CAAC,IAAI;AAAA,cAClC;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,UAAM,kBAAkB,SAAS,MAAM;AAEtC,UAAI,OAAO,KAAK,iBAAiB,KAAK,UAAU;AAEhD,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC5B,YAAI,MAAO,CAAC,SAAW,QAAQ,IAAK,MAAM;AAC1C,aAAK,QAAQ,KAAK,MAAM,IAAI,CAAC,CAAC,EAAE,IAAI,IAAI,KAAK,cAAc,IAAI,CAAC,IAAI;AAAA,MACrE;AAEA,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC5B,YAAI,MAAO,CAAC,SAAW,QAAQ,IAAK,MAAM;AAC1C,aAAK,QAAQ,IAAI,IAAI,KAAK,cAAc,IAAI,CAAC,EAAE,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI;AAAA,MACrE;AAAA,IACD;AAEA,UAAM,gBAAgB,SAAS,MAAM,aAAa;AAEjD,UAAI,OAAQ,KAAK,qBAAqB,IAAK;AAC3C,UAAI,OAAO,KAAK,eAAe,IAAI;AAGnC,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAE5B,YAAI,MAAO,CAAC,SAAW,QAAQ,IAAK,MAAM;AAE1C,YAAI,IAAI,GAAG;AACV,eAAK,QAAQ,CAAC,EAAE,CAAC,IAAI;AAAA,QACtB,WAAW,IAAI,GAAG;AACjB,eAAK,QAAQ,IAAI,CAAC,EAAE,CAAC,IAAI;AAAA,QAC1B,OAAO;AACN,eAAK,QAAQ,KAAK,cAAc,KAAK,CAAC,EAAE,CAAC,IAAI;AAAA,QAC9C;AAAA,MACD;AAGA,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAE5B,YAAI,MAAO,CAAC,SAAW,QAAQ,IAAK,MAAM;AAE1C,YAAI,IAAI,GAAG;AACV,eAAK,QAAQ,CAAC,EAAE,KAAK,cAAc,IAAI,CAAC,IAAI;AAAA,QAC7C,WAAW,IAAI,GAAG;AACjB,eAAK,QAAQ,CAAC,EAAE,KAAK,IAAI,IAAI,CAAC,IAAI;AAAA,QACnC,OAAO;AACN,eAAK,QAAQ,CAAC,EAAE,KAAK,IAAI,CAAC,IAAI;AAAA,QAC/B;AAAA,MACD;AAGA,WAAK,QAAQ,KAAK,cAAc,CAAC,EAAE,CAAC,IAAK,CAAC;AAAA,IAC3C;AAEA,UAAM,UAAU,SAAS,MAAM,aAAa;AAE3C,UAAI,MAAM;AACV,UAAI,MAAM,KAAK,cAAc;AAC7B,UAAI,WAAW;AACf,UAAI,YAAY;AAEhB,eAAS,MAAM,KAAK,cAAc,GAAG,MAAM,GAAG,OAAO,GAAG;AAEvD,YAAI,OAAO,EAAG;AAEd,eAAO,MAAM;AAEZ,mBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAE3B,gBAAI,KAAK,QAAQ,GAAG,EAAE,MAAM,CAAC,KAAK,MAAM;AAEvC,kBAAI,OAAO;AAEX,kBAAI,YAAY,KAAK,QAAQ;AAC5B,wBAAY,KAAK,SAAS,MAAM,WAAY,MAAM;AAAA,cACnD;AAEA,kBAAI,OAAO,KAAK,QAAQ,aAAa,KAAK,MAAM,CAAC;AAEjD,kBAAI,MAAM;AACT,uBAAO,CAAC;AAAA,cACT;AAEA,mBAAK,QAAQ,GAAG,EAAE,MAAM,CAAC,IAAI;AAC7B;AAEA,kBAAI,YAAY,IAAI;AACnB;AACA,2BAAW;AAAA,cACZ;AAAA,YACD;AAAA,UACD;AAEA,iBAAO;AAEP,cAAI,MAAM,KAAK,KAAK,eAAe,KAAK;AACvC,mBAAO;AACP,kBAAM,CAAC;AACP;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,WAAO,OAAO;AACd,WAAO,OAAO;AAEd,WAAO,aAAa,SAAS,YAAY,mBAAmB,UAAU;AAErE,UAAI,WAAW,QAAQ,YAAY,YAAY,iBAAiB;AAEhE,UAAI,SAAS,IAAI,UAAU;AAE3B,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACzC,YAAI,OAAO,SAAS,CAAC;AACrB,eAAO,IAAI,KAAK,MAAM,CAAC;AACvB,eAAO,IAAI,KAAK,UAAU,GAAG,KAAK,gBAAgB,KAAK,MAAM,UAAU,CAAE;AACzE,aAAK,MAAM,MAAM;AAAA,MAClB;AAGA,UAAI,iBAAiB;AACrB,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACzC,0BAAkB,SAAS,CAAC,EAAE;AAAA,MAC/B;AAEA,UAAI,OAAO,gBAAgB,IAAI,iBAAiB,GAAG;AAClD,cAAM,IAAI,MAAM,4BACb,OAAO,gBAAgB,IACvB,MACC,iBAAiB,IAClB,GAAG;AAAA,MACP;AAGA,UAAI,OAAO,gBAAgB,IAAI,KAAK,iBAAiB,GAAG;AACvD,eAAO,IAAI,GAAG,CAAC;AAAA,MAChB;AAGA,aAAO,OAAO,gBAAgB,IAAI,KAAK,GAAG;AACzC,eAAO,OAAO,KAAK;AAAA,MACpB;AAGA,aAAO,MAAM;AAEZ,YAAI,OAAO,gBAAgB,KAAK,iBAAiB,GAAG;AACnD;AAAA,QACD;AACA,eAAO,IAAI,OAAO,MAAM,CAAC;AAEzB,YAAI,OAAO,gBAAgB,KAAK,iBAAiB,GAAG;AACnD;AAAA,QACD;AACA,eAAO,IAAI,OAAO,MAAM,CAAC;AAAA,MAC1B;AAEA,aAAO,OAAO,YAAY,QAAQ,QAAQ;AAAA,IAC3C;AAEA,WAAO,cAAc,SAAS,QAAQ,UAAU;AAE/C,UAAI,SAAS;AAEb,UAAI,aAAa;AACjB,UAAI,aAAa;AAEjB,UAAI,SAAS,IAAI,MAAM,SAAS,MAAM;AACtC,UAAI,SAAS,IAAI,MAAM,SAAS,MAAM;AAEtC,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AAEzC,YAAI,UAAU,SAAS,CAAC,EAAE;AAC1B,YAAI,UAAU,SAAS,CAAC,EAAE,aAAa;AAEvC,qBAAa,KAAK,IAAI,YAAY,OAAO;AACzC,qBAAa,KAAK,IAAI,YAAY,OAAO;AAEzC,eAAO,CAAC,IAAI,IAAI,MAAM,OAAO;AAE7B,iBAAS,IAAI,GAAG,IAAI,OAAO,CAAC,EAAE,QAAQ,KAAK;AAC1C,iBAAO,CAAC,EAAE,CAAC,IAAI,MAAO,OAAO,OAAO,IAAI,MAAM;AAAA,QAC/C;AACA,kBAAU;AAEV,YAAI,SAAS,KAAK,0BAA0B,OAAO;AACnD,YAAI,UAAU,IAAI,WAAW,OAAO,CAAC,GAAG,OAAO,UAAU,IAAI,CAAC;AAE9D,YAAI,UAAU,QAAQ,IAAI,MAAM;AAChC,eAAO,CAAC,IAAI,IAAI,MAAM,OAAO,UAAU,IAAI,CAAC;AAC5C,iBAAS,IAAI,GAAG,IAAI,OAAO,CAAC,EAAE,QAAQ,KAAK;AACjC,cAAI,WAAW,IAAI,QAAQ,UAAU,IAAI,OAAO,CAAC,EAAE;AAC5D,iBAAO,CAAC,EAAE,CAAC,IAAK,YAAY,IAAI,QAAQ,IAAI,QAAQ,IAAI;AAAA,QACzD;AAAA,MAED;AAEA,UAAI,iBAAiB;AACrB,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACzC,0BAAkB,SAAS,CAAC,EAAE;AAAA,MAC/B;AAEA,UAAI,OAAO,IAAI,MAAM,cAAc;AACnC,UAAI,QAAQ;AAEZ,eAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACpC,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACzC,cAAI,IAAI,OAAO,CAAC,EAAE,QAAQ;AACzB,iBAAK,OAAO,IAAI,OAAO,CAAC,EAAE,CAAC;AAAA,UAC5B;AAAA,QACD;AAAA,MACD;AAEA,eAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACpC,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACzC,cAAI,IAAI,OAAO,CAAC,EAAE,QAAQ;AACzB,iBAAK,OAAO,IAAI,OAAO,CAAC,EAAE,CAAC;AAAA,UAC5B;AAAA,QACD;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACpbjB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AAED,QAAI,WAAW,OAAO,UAAU,SAAU,QAAQ;AAAE,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,YAAI,SAAS,UAAU,CAAC;AAAG,iBAAS,OAAO,QAAQ;AAAE,cAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAAE,mBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,UAAG;AAAA,QAAE;AAAA,MAAE;AAAE,aAAO;AAAA,IAAQ;AAE/P,QAAI,aAAa;AAEjB,QAAI,cAAc,uBAAuB,UAAU;AAEnD,QAAI,SAAS;AAEb,QAAI,UAAU,uBAAuB,MAAM;AAE3C,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,yBAAyB,KAAK,MAAM;AAAE,UAAI,SAAS,CAAC;AAAG,eAAS,KAAK,KAAK;AAAE,YAAI,KAAK,QAAQ,CAAC,KAAK,EAAG;AAAU,YAAI,CAAC,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC,EAAG;AAAU,eAAO,CAAC,IAAI,IAAI,CAAC;AAAA,MAAG;AAAE,aAAO;AAAA,IAAQ;AAE3N,QAAI,YAAY;AAAA,MACd,SAAS,YAAY,QAAQ,UAAU,CAAC,YAAY,QAAQ,QAAQ,YAAY,QAAQ,MAAM,CAAC,EAAE;AAAA,MACjG,KAAK,YAAY,QAAQ,OAAO;AAAA,MAChC,SAAS,YAAY,QAAQ,UAAU,CAAC,YAAY,QAAQ,QAAQ,YAAY,QAAQ,MAAM,CAAC,EAAE;AAAA,MACjG,KAAK,YAAY,QAAQ,OAAO;AAAA,MAChC,MAAM,YAAY,QAAQ,OAAO;AAAA,MACjC,OAAO,YAAY,QAAQ;AAAA,MAC3B,aAAa,YAAY,QAAQ,OAAO;AAAA,MACxC,OAAO,YAAY,QAAQ;AAAA,IAC7B;AAEA,QAAI,aAAa,GAAG,OAAO,YAAY,SAAU,MAAM,KAAK;AAC1D,UAAI,UAAU,KAAK,SACf,MAAM,KAAK,KACX,MAAM,KAAK,KACX,UAAU,KAAK,SACf,OAAO,KAAK,MACZ,QAAQ,KAAK,OACb,cAAc,KAAK,aACnB,aAAa,KAAK,OAClB,QAAQ,eAAe,SAAY,+BAA+B,YAClE,QAAQ,yBAAyB,MAAM,CAAC,WAAW,OAAO,OAAO,WAAW,QAAQ,SAAS,eAAe,OAAO,CAAC;AAExH,aAAO,QAAQ,QAAQ;AAAA,QACrB;AAAA,QACA,SAAS,CAAC,GAAG,OAAO,EAAE,QAAQ,MAAM,KAAU,SAAS,SAAS,cAAc,MAAM,aAAa,OAAO,MAAM,MAAa,CAAC;AAAA,QAC5H,QAAQ,QAAQ,QAAQ;AAAA,UACtB;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AAAA,QACJ,QAAQ,QAAQ,cAAc,QAAQ,EAAE,GAAG,KAAK,MAAM,QAAQ,CAAC;AAAA,QAC/D,QAAQ,QAAQ,cAAc,QAAQ,EAAE,GAAG,KAAK,MAAM,QAAQ,CAAC;AAAA,MACjE;AAAA,IACF,CAAC;AAED,cAAU,cAAc;AACxB,cAAU,YAAY;AAEtB,YAAQ,UAAU;AAAA;AAAA;;;AC3DlB;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,SAAS;AAEjB,QAAI,WAAW,OAAO,UAAU,SAAU,QAAQ;AAAE,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,YAAI,SAAS,UAAU,CAAC;AAAG,iBAAS,OAAO,QAAQ;AAAE,cAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAAE,mBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,UAAG;AAAA,QAAE;AAAA,MAAE;AAAE,aAAO;AAAA,IAAQ;AAE/P,QAAI,aAAa;AAEjB,QAAI,cAAc,uBAAuB,UAAU;AAEnD,QAAI,qBAAqB;AAEzB,QAAI,sBAAsB,uBAAuB,kBAAkB;AAEnE,QAAI,UAAU;AAEd,QAAI,WAAW,uBAAuB,OAAO;AAE7C,QAAI,SAAS;AAEb,QAAI,UAAU,uBAAuB,MAAM;AAE3C,QAAI,aAAa;AAEjB,QAAI,cAAc,uBAAuB,UAAU;AAEnD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,yBAAyB,KAAK,MAAM;AAAE,UAAI,SAAS,CAAC;AAAG,eAAS,KAAK,KAAK;AAAE,YAAI,KAAK,QAAQ,CAAC,KAAK,EAAG;AAAU,YAAI,CAAC,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC,EAAG;AAAU,eAAO,CAAC,IAAI,IAAI,CAAC;AAAA,MAAG;AAAE,aAAO;AAAA,IAAQ;AAI3N,QAAI,YAAY;AAAA,MACd,SAAS,YAAY,QAAQ,UAAU,CAAC,YAAY,QAAQ,QAAQ,YAAY,QAAQ,MAAM,CAAC;AAAA,MAC/F,SAAS,YAAY,QAAQ,UAAU,CAAC,YAAY,QAAQ,QAAQ,YAAY,QAAQ,MAAM,CAAC;AAAA,MAC/F,OAAO,YAAY,QAAQ;AAAA,MAC3B,MAAM,YAAY,QAAQ;AAAA,MAC1B,OAAO,YAAY,QAAQ,OAAO;AAAA,IACpC;AAEA,QAAI,UAAU,GAAG,OAAO,YAAY,SAAU,MAAM,KAAK;AACvD,UAAI,eAAe,KAAK,SACpB,UAAU,iBAAiB,SAAY,YAAY,cACnD,eAAe,KAAK,SACpB,UAAU,iBAAiB,SAAY,YAAY,cACnD,aAAa,KAAK,OAClB,QAAQ,eAAe,SAAY,MAAM,YACzC,YAAY,KAAK,MACjB,OAAO,cAAc,SAAY,MAAM,WACvC,QAAQ,KAAK,OACb,QAAQ,yBAAyB,MAAM,CAAC,WAAW,WAAW,SAAS,QAAQ,OAAO,CAAC;AAG3F,UAAI,SAAS,IAAI,SAAS,QAAQ,IAAI,oBAAoB,QAAQ,KAAK,CAAC;AACxE,aAAO,QAAQ,KAAK;AACpB,aAAO,KAAK;AACZ,UAAI,QAAQ,OAAO;AACnB,aAAO,QAAQ,QAAQ,cAAc,YAAY,SAAS,SAAS,CAAC,GAAG,OAAO;AAAA,QAC5E;AAAA,QACA,KAAK,MAAM,IAAI,SAAU,KAAK,UAAU;AACtC,iBAAO,IAAI,IAAI,SAAU,MAAM,WAAW;AACxC,mBAAO,CAAC,OAAO,OAAO,YAAY,MAAM,WAAW,sBAAsB;AAAA,UAC3E,CAAC,EAAE,KAAK,GAAG;AAAA,QACb,CAAC,EAAE,KAAK,GAAG;AAAA,QACX;AAAA,QACA,KAAK,MAAM,IAAI,SAAU,KAAK,UAAU;AACtC,iBAAO,IAAI,IAAI,SAAU,MAAM,WAAW;AACxC,mBAAO,OAAO,OAAO,YAAY,MAAM,WAAW,sBAAsB;AAAA,UAC1E,CAAC,EAAE,KAAK,GAAG;AAAA,QACb,CAAC,EAAE,KAAK,GAAG;AAAA,QACX;AAAA,QACA;AAAA,QACA,aAAa,MAAM;AAAA,MACrB,CAAC,CAAC;AAAA,IACJ,CAAC;AAED,YAAQ,SAAS;AACjB,WAAO,cAAc;AACrB,WAAO,YAAY;AAEnB,YAAQ,UAAU;AAAA;AAAA;", "names": ["i", "checker"]}
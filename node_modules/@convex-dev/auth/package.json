{"name": "@convex-dev/auth", "version": "0.0.87", "description": "Authentication for Convex", "keywords": ["authentication", "authorization", "auth", "login", "sign", "convex"], "homepage": "https://labs.convex.dev/auth", "bugs": "https://github.com/get-convex/convex-auth/issues", "repository": "https://github.com/get-convex/convex-auth", "license": "Apache-2.0", "author": "xixixao", "files": ["dist", "providers", "react", "server", "src"], "type": "module", "bin": "./dist/bin.cjs", "sideEffects": false, "scripts": {"build:bin": "esbuild src/cli/index.ts --bundle --platform=node --format=cjs --outfile=dist/bin.cjs", "build:server": "tsc --project tsconfig.server.json", "build:react": "tsc --project tsconfig.react.json && mkdir -p dist && rm -rf dist/react && mv distreact/react dist/react && rm -r distreact", "build:nextjs": "tsc --project tsconfig.nextjs.json && mkdir -p dist && rm -rf dist/nextjs && mv distnextjs/nextjs dist/nextjs && rm -r distnextjs", "build": "rm -rf dist && npm run build:bin && npm run build:server && npm run build:react && npm run build:nextjs", "docs": "cd docs && npm run dev", "lint": "tsc && eslint . && cd test && npm run lint", "spellcheck": "cspell \"docs/pages/**/*.md*\"", "prepare": "npm run build", "prepublishOnly": "npm run lint && npm run build && npm run test:once", "publish:example": "node publishExample.mjs", "test": "cd test && npm run test", "test:once": "cd test && npm run test:once"}, "exports": {"./server": {"types": "./dist/server/index.d.ts", "import": "./dist/server/index.js"}, "./providers/*": {"types": "./dist/providers/*.d.ts", "import": "./dist/providers/*.js"}, "./react": {"import": "./dist/react/index.js", "require": "./dist/react/index.js"}, "./nextjs": {"import": "./dist/nextjs/index.js", "require": "./dist/nextjs/index.js"}, "./nextjs/server": {"import": "./dist/nextjs/server/index.js", "require": "./dist/nextjs/server/index.js"}}, "dependencies": {"cookie": "^1.0.1", "is-network-error": "^1.1.0", "jose": "^5.2.2", "jwt-decode": "^4.0.0", "lucia": "^3.2.0", "oauth4webapi": "^3.1.2", "oslo": "^1.1.2", "path-to-regexp": "^6.3.0", "server-only": "^0.0.1"}, "peerDependencies": {"@auth/core": "^0.37.0", "convex": "^1.17.0", "react": "^18.2.0 || ^19.0.0-0"}, "peerDependenciesMeta": {"react": {"optional": true}}, "@comment devDependencies": ["The dependencies of the CLI are also in devDependencies, built into", "a bundle."], "devDependencies": {"@commander-js/extra-typings": "^12.1.0", "@edge-runtime/vm": "^3.2.0", "@types/inquirer": "^9.0.7", "@types/node": "20.6.0", "@types/react": "^18.3.12", "@typescript-eslint/eslint-plugin": "^6.18.1", "chalk": "^5.3.0", "convex-test": "^0.0.20", "cspell": "^8.17.2", "dotenv": "^16.4.5", "eslint": "8.49.0", "inquirer": "^9.2.22", "next": "^15.0.3", "npm-run-all": "^4.1.5", "react-dom": "^18.3.1", "shelljs": "^0.8.5", "tsup": "^8.0.1", "typescript": "^5.5.2", "valibot": "^0.35.0", "vitest": "^1.6.0"}}
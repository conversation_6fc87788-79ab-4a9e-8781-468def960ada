{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/react/index.tsx"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,YAAY,CAAC;;AAEb,OAAO,EAAE,gBAAgB,EAAE,MAAM,gBAAgB,CAAC;AAClD,OAAO,EAAE,sBAAsB,EAAqB,MAAM,cAAc,CAAC;AAEzE,OAAO,EAAa,UAAU,EAAE,OAAO,EAAE,MAAM,OAAO,CAAC;AACvD,OAAO,EACL,YAAY,EACZ,wBAAwB,EACxB,sBAAsB,EACtB,OAAO,GACR,MAAM,aAAa,CAAC;AAGrB;;;;;;;;;;;GAWG;AACH,MAAM,UAAU,cAAc;IAC5B,OAAO,UAAU,CAAC,wBAAwB,CAAC,CAAC;AAC9C,CAAC;AAED;;;;;;;;;;;;;;GAcG;AACH,MAAM,UAAU,kBAAkB,CAAC,KA8ClC;IACC,MAAM,EACJ,MAAM,EACN,OAAO,EACP,gBAAgB,EAChB,UAAU,EACV,gBAAgB,EAChB,QAAQ,GACT,GAAG,KAAK,CAAC;IACV,MAAM,UAAU,GAAG,OAAO,CACxB,GAAG,EAAE,CACH,CAAC;QACC,iBAAiB,CAAC,MAAM,EAAE,IAAI;YAC5B,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACrC,CAAC;QACD,mBAAmB,CAAC,MAAM,EAAE,IAAI;YAC9B,OAAO,IAAI,gBAAgB,CAAE,MAAc,CAAC,OAAO,EAAE;gBACnD,MAAM,EAAE,MAAM,CAAC,MAAM;aACtB,CAAC,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC1B,CAAC;QACD,OAAO,EAAG,MAAc,CAAC,OAAO,EAAE,OAAO;QACzC,MAAM,EAAE,MAAM,CAAC,MAAM;KACtB,CAAsB,EACzB,CAAC,MAAM,CAAC,CACT,CAAC;IACF,OAAO,CACL,KAAC,YAAY,IACX,MAAM,EAAE,UAAU,EAClB,OAAO,EACL,OAAO;YACP,4BAA4B;YAC5B,uDAAuD;YACvD,yBAAyB;YACzB,CAAC,OAAO,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,EAAE,YAAY,CAAE,EAErE,gBAAgB,EAAE,gBAAgB,IAAK,MAAc,CAAC,OAAO,EAC7D,UAAU,EACR,UAAU;YACV,CAAC,CAAC,GAAG,EAAE,EAAE;gBACP,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;YAC3C,CAAC,CAAC,EAEJ,gBAAgB,EAAE,gBAAgB,YAElC,KAAC,sBAAsB,IAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,YACrD,QAAQ,GACc,GACZ,CAChB,CAAC;AACJ,CAAC;AA0GD;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,MAAM,UAAU,YAAY;IAC1B,OAAO,UAAU,CAAC,sBAAsB,CAAC,CAAC;AAC5C,CAAC"}
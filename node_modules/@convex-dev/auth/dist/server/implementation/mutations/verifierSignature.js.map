{"version": 3, "file": "verifierSignature.js", "sourceRoot": "", "sources": ["../../../../src/server/implementation/mutations/verifierSignature.ts"], "names": [], "mappings": "AAAA,OAAO,EAAoB,CAAC,EAAE,MAAM,eAAe,CAAC;AAGpD,MAAM,CAAC,MAAM,qBAAqB,GAAG,CAAC,CAAC,MAAM,CAAC;IAC5C,QAAQ,EAAE,CAAC,CAAC,MAAM,EAAE;IACpB,SAAS,EAAE,CAAC,CAAC,MAAM,EAAE;CACtB,CAAC,CAAC;AAIH,MAAM,CAAC,KAAK,UAAU,qBAAqB,CACzC,GAAgB,EAChB,IAAyC;IAEzC,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC;IACrC,MAAM,WAAW,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAsC,CAAC,CAAC;IAC7E,IAAI,WAAW,KAAK,IAAI,EAAE,CAAC;QACzB,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;IACtC,CAAC;IACD,OAAO,MAAM,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;AAC5D,CAAC;AAED,MAAM,CAAC,MAAM,qBAAqB,GAAG,KAAK,EACxC,GAAc,EACd,IAAyC,EAC1B,EAAE;IACjB,OAAO,GAAG,CAAC,WAAW,CAAC,YAAmB,EAAE;QAC1C,IAAI,EAAE;YACJ,IAAI,EAAE,mBAAmB;YACzB,GAAG,IAAI;SACR;KACF,CAAC,CAAC;AACL,CAAC,CAAC"}
import { jsx as _jsx } from "react/jsx-runtime";
import "server-only";
import { fetchQuery } from "convex/nextjs";
import { NextResponse, } from "next/server";
import { ConvexAuthNextjsClientProvider, } from "../client.js";
import { getRequestCookies, getRequestCookiesInMiddleware } from "./cookies.js";
import { proxyAuthActionToConvex, shouldProxyAuthAction } from "./proxy.js";
import { handleAuthenticationInRequest } from "./request.js";
import { logVerbose, setAuthCookies, setAuthCookiesInMiddleware, getConvexNextjsOptions, } from "./utils.js";
/**
 * Wrap your app with this provider in your root `layout.tsx`.
 */
export async function ConvexAuthNextjsServerProvider(props) {
    const { apiRoute, storage, storageNamespace, shouldHandleCode, verbose, children, } = props;
    const serverState = await convexAuthNextjsServerState();
    return (_jsx(ConvexAuthNextjsClientProvider, { serverState: serverState, apiRoute: apiRoute, storage: storage, storageNamespace: storageNamespace, shouldHandleCode: shouldHandleCode, verbose: verbose, children: children }));
}
/**
 * Retrieve the token for authenticating calls to your
 * Convex backend from Server Components, Server Actions and Route Handlers.
 * @returns The token if the client is authenticated, otherwise `undefined`.
 */
export async function convexAuthNextjsToken() {
    return (await getRequestCookies()).token ?? undefined;
}
/**
 * Whether the client is authenticated, which you can check
 * in Server Actions, Route Handlers and Middleware.
 *
 * Avoid the pitfall of checking authentication state in layouts,
 * since they won't stop nested pages from rendering.
 */
export async function isAuthenticatedNextjs(options = {}) {
    const cookies = await getRequestCookies();
    return isAuthenticated(cookies.token, options);
}
/**
 * Use in your `middleware.ts` to enable your Next.js app to use
 * Convex Auth for authentication on the server.
 *
 * @returns A Next.js middleware.
 */
export function convexAuthNextjsMiddleware(
/**
 * A custom handler, which you can use to decide
 * which routes should be accessible based on the client's authentication.
 */
handler, options = {}) {
    return async (request, event) => {
        const verbose = options.verbose ?? false;
        const cookieConfig = options.cookieConfig ?? { maxAge: null };
        if (cookieConfig.maxAge !== null && cookieConfig.maxAge <= 0) {
            throw new Error("cookieConfig.maxAge must be null or a positive number of seconds");
        }
        logVerbose(`Begin middleware for request with URL ${request.url}`, verbose);
        const requestUrl = new URL(request.url);
        // Proxy signIn and signOut actions to Convex backend
        const apiRoute = options?.apiRoute ?? "/api/auth";
        if (shouldProxyAuthAction(request, apiRoute)) {
            logVerbose(`Proxying auth action to Convex, path matches ${apiRoute} with or without trailing slash`, verbose);
            return await proxyAuthActionToConvex(request, options);
        }
        logVerbose(`Not proxying auth action to Convex, path ${requestUrl.pathname} does not match ${apiRoute}`, verbose);
        // Refresh tokens, handle code query param
        const authResult = await handleAuthenticationInRequest(request, options);
        // If redirecting, proceed, the middleware will run again on next request
        if (authResult.kind === "redirect") {
            logVerbose(`Redirecting to ${authResult.response.headers.get("Location")}`, verbose);
            return authResult.response;
        }
        let response = null;
        // Forward cookies to request for custom handler
        if (authResult.kind === "refreshTokens" &&
            authResult.refreshTokens !== undefined) {
            logVerbose(`Forwarding cookies to request`, verbose);
            await setAuthCookiesInMiddleware(request, authResult.refreshTokens);
        }
        if (handler === undefined) {
            logVerbose(`No custom handler`, verbose);
            response = NextResponse.next({
                request: {
                    headers: request.headers,
                },
            });
        }
        else {
            // Call the custom handler
            logVerbose(`Calling custom handler`, verbose);
            response =
                (await handler(request, {
                    event,
                    convexAuth: {
                        getToken: async () => {
                            const cookies = await getRequestCookiesInMiddleware(request);
                            return cookies.token ?? undefined;
                        },
                        isAuthenticated: async () => {
                            const cookies = await getRequestCookiesInMiddleware(request);
                            return isAuthenticated(cookies.token, options);
                        },
                    },
                })) ??
                    NextResponse.next({
                        request: {
                            headers: request.headers,
                        },
                    });
        }
        // Port the cookies from the auth middleware to the response
        if (authResult.kind === "refreshTokens" &&
            authResult.refreshTokens !== undefined) {
            const nextResponse = NextResponse.next(response);
            await setAuthCookies(nextResponse, authResult.refreshTokens, cookieConfig);
            return nextResponse;
        }
        return response;
    };
}
export { createRouteMatcher } from "./routeMatcher.js";
/**
 * Helper for redirecting to a different route from
 * a Next.js middleware.
 *
 * ```ts
 * return nextjsMiddlewareRedirect(request, "/login");
 * ```
 */
export function nextjsMiddlewareRedirect(
/**
 * The incoming request handled by the middleware.
 */
request, 
/**
 * The route path to redirect to.
 */
pathname) {
    const url = request.nextUrl.clone();
    url.pathname = pathname;
    return NextResponse.redirect(url);
}
async function convexAuthNextjsServerState() {
    const { token } = await getRequestCookies();
    return {
        // The server doesn't share the refresh token with the client
        // for added security - the client has to use the server
        // to refresh the access token via cookies.
        _state: { token, refreshToken: "dummy" },
        _timeFetched: Date.now(),
    };
}
async function isAuthenticated(token, options) {
    if (!token) {
        return false;
    }
    try {
        return await fetchQuery("auth:isAuthenticated", {}, {
            ...getConvexNextjsOptions(options),
            token: token,
        });
    }
    catch (e) {
        if (e.message.includes("Could not find public function")) {
            throw new Error("Server Error: could not find api.auth.isAuthenticated. convex-auth 0.0.76 introduced a new export in convex/auth.ts. Add `isAuthenticated` to the list of functions returned from convexAuth(). See convex-auth changelog for more https://github.com/get-convex/convex-auth/blob/main/CHANGELOG.md");
        }
        else {
            console.log("Returning false from isAuthenticated because", e);
        }
        return false;
    }
}
//# sourceMappingURL=index.js.map
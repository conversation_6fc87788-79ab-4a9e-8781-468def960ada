{"version": 3, "file": "proxy.js", "sourceRoot": "", "sources": ["../../../src/nextjs/server/proxy.ts"], "names": [], "mappings": "AAAA,OAAO,aAAa,CAAC;AAErB,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAG5C,OAAO,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,MAAM,cAAc,CAAC;AACrE,OAAO,EACL,sBAAsB,EACtB,kBAAkB,EAClB,aAAa,EACb,YAAY,EACZ,UAAU,EACV,cAAc,GACf,MAAM,YAAY,CAAC;AAEpB,MAAM,CAAC,KAAK,UAAU,uBAAuB,CAC3C,OAAoB,EACpB,OAIC;IAED,MAAM,YAAY,GAAG,OAAO,EAAE,YAAY,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;IAC/D,MAAM,OAAO,GAAG,OAAO,EAAE,OAAO,IAAI,KAAK,CAAC;IAC1C,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;QAC9B,OAAO,IAAI,QAAQ,CAAC,gBAAgB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC;IACzD,CAAC;IACD,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE,CAAC;QAC3B,OAAO,IAAI,QAAQ,CAAC,gBAAgB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC;IACzD,CAAC;IACD,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;IAC9C,IAAI,MAAM,KAAK,aAAa,IAAI,MAAM,KAAK,cAAc,EAAE,CAAC;QAC1D,UAAU,CAAC,kBAAkB,MAAM,iBAAiB,EAAE,OAAO,CAAC,CAAC;QAC/D,OAAO,IAAI,QAAQ,CAAC,gBAAgB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC;IACzD,CAAC;IACD,IAAI,KAAyB,CAAC;IAC9B,IAAI,MAAM,KAAK,aAAa,IAAI,IAAI,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;QAChE,4DAA4D;QAC5D,qBAAqB;QACrB,MAAM,YAAY,GAAG,CAAC,MAAM,iBAAiB,EAAE,CAAC,CAAC,YAAY,CAAC;QAC9D,IAAI,YAAY,KAAK,IAAI,EAAE,CAAC;YAC1B,OAAO,CAAC,KAAK,CACX,2EAA2E,CAC5E,CAAC;YACF,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QACxD,CAAC;QACD,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;IACnC,CAAC;SAAM,CAAC;QACN,yDAAyD;QACzD,oDAAoD;QACpD,0BAA0B;QAC1B,KAAK,GAAG,CAAC,MAAM,iBAAiB,EAAE,CAAC,CAAC,KAAK,IAAI,SAAS,CAAC;IACzD,CAAC;IACD,UAAU,CACR,mBAAmB,MAAM,cAAc,IAAI,CAAC,SAAS,CAAC;QACpD,GAAG,IAAI;QACP,YAAY,EAAE,kBAAkB,CAAC,IAAI,EAAE,YAAY,IAAI,EAAE,CAAC;KAC3D,CAAC,EAAE,EACJ,OAAO,CACR,CAAC;IAEF,IAAI,MAAM,KAAK,aAAa,EAAE,CAAC;QAC7B,IAAI,MAAmC,CAAC;QACxC,6EAA6E;QAC7E,8BAA8B;QAC9B,MAAM,sBAAsB,GAC1B,IAAI,CAAC,YAAY,KAAK,SAAS,IAAI,IAAI,CAAC,MAAM,EAAE,IAAI,KAAK,SAAS;YAChE,CAAC,CAAC,EAAE;YACJ,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC;QAChB,IAAI,CAAC;YACH,MAAM,GAAG,MAAM,WAAW,CAAC,MAAM,EAAE,IAAI,EAAE;gBACvC,GAAG,sBAAsB,CAAC,OAAO,CAAC;gBAClC,GAAG,sBAAsB;aAC1B,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0CAA0C,CAAC,CAAC;YAC1D,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACrB,UAAU,CAAC,uBAAuB,EAAE,OAAO,CAAC,CAAC;YAC7C,kEAAkE;YAClE,MAAM,QAAQ,GAAG,YAAY,CAAC,EAAE,KAAK,EAAG,KAAe,CAAC,OAAO,EAAE,EAAE,GAAG,CAAC,CAAC;YACxE,MAAM,cAAc,CAAC,QAAQ,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;YACnD,OAAO,QAAQ,CAAC;QAClB,CAAC;QACD,IAAI,MAAM,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YAClC,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC;YAC5B,MAAM,QAAQ,GAAG,YAAY,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC;YAC5C,CAAC,MAAM,kBAAkB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC,CAAC,QAAQ;gBACzD,MAAM,CAAC,QAAS,CAAC;YACnB,UAAU,CAAC,kBAAkB,QAAQ,EAAE,EAAE,OAAO,CAAC,CAAC;YAClD,OAAO,QAAQ,CAAC;QAClB,CAAC;aAAM,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YACvC,6DAA6D;YAC7D,wDAAwD;YACxD,2CAA2C;YAC3C,UAAU,CACR,MAAM,CAAC,MAAM,KAAK,IAAI;gBACpB,CAAC,CAAC,2CAA2C;gBAC7C,CAAC,CAAC,2CAA2C,EAC/C,OAAO,CACR,CAAC;YACF,MAAM,QAAQ,GAAG,YAAY,CAAC;gBAC5B,MAAM,EACJ,MAAM,CAAC,MAAM,KAAK,IAAI;oBACpB,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,YAAY,EAAE,OAAO,EAAE;oBACvD,CAAC,CAAC,IAAI;aACX,CAAC,CAAC;YACH,MAAM,cAAc,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;YAC5D,OAAO,QAAQ,CAAC;QAClB,CAAC;QACD,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC;IAC9B,CAAC;SAAM,CAAC;QACN,IAAI,CAAC;YACH,MAAM,WAAW,CAAC,MAAM,EAAE,IAAI,EAAE;gBAC9B,GAAG,sBAAsB,CAAC,OAAO,CAAC;gBAClC,KAAK;aACN,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC;YAC3D,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACvB,CAAC;QACD,UAAU,CAAC,uBAAuB,EAAE,OAAO,CAAC,CAAC;QAC7C,MAAM,QAAQ,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;QACpC,MAAM,cAAc,CAAC,QAAQ,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;QACnD,OAAO,QAAQ,CAAC;IAClB,CAAC;AACH,CAAC;AAED,MAAM,UAAU,qBAAqB,CAAC,OAAoB,EAAE,QAAgB;IAC1E,yFAAyF;IACzF,yEAAyE;IACzE,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IACxC,IAAI,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QAC3B,OAAO,CACL,UAAU,CAAC,QAAQ,KAAK,QAAQ;YAChC,UAAU,CAAC,QAAQ,KAAK,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAC9C,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,OAAO,CACL,UAAU,CAAC,QAAQ,KAAK,QAAQ,IAAI,UAAU,CAAC,QAAQ,KAAK,QAAQ,GAAG,GAAG,CAC3E,CAAC;IACJ,CAAC;AACH,CAAC"}
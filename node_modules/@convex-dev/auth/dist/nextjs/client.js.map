{"version": 3, "file": "client.js", "sourceRoot": "", "sources": ["../../src/nextjs/client.tsx"], "names": [], "mappings": "AAAA,YAAY,CAAC;;AAEb,OAAO,EAAa,WAAW,EAAE,OAAO,EAAE,MAAM,OAAO,CAAC;AACxD,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAElD,OAAO,EAAE,eAAe,EAAE,MAAM,6BAA6B,CAAC;AAE9D,MAAM,UAAU,8BAA8B,CAAC,EAC7C,QAAQ,EACR,WAAW,EACX,OAAO,EACP,gBAAgB,EAChB,gBAAgB,EAChB,OAAO,EACP,QAAQ,GAST;IACC,MAAM,IAAI,GAAoC,WAAW,CACvD,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE;QACrB,MAAM,MAAM,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;QAChC,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,QAAQ,IAAI,WAAW,EAAE;YACpD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;YAC5B,MAAM,EAAE,MAAM;SACf,CAAC,CAAC;QACH,yCAAyC;QACzC,IAAI,QAAQ,CAAC,MAAM,IAAI,GAAG,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,CAAC,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;QACjD,CAAC;QACD,OAAO,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;IAC/B,CAAC,EACD,CAAC,QAAQ,CAAC,CACX,CAAC;IACF,MAAM,UAAU,GAAG,OAAO,CACxB,GAAG,EAAE,CAAC,CAAC;QACL,iBAAiB,EAAE,IAAI;QACvB,mBAAmB,EAAE,IAAI;QACzB,OAAO;KACR,CAAC,EACF,CAAC,IAAI,EAAE,OAAO,CAAC,CAChB,CAAC;IACF,OAAO,CACL,KAAC,YAAY,IACX,MAAM,EAAE,UAAU,EAClB,WAAW,EAAE,WAAW,EACxB,QAAQ,EAAE,eAAe,EACzB,OAAO;QACL,2BAA2B;QAC3B,uDAAuD;QACvD,yBAAyB;QACzB,CAAC,OAAO,MAAM,KAAK,WAAW;YAC5B,CAAC,CAAC,SAAS;YACX,CAAC,CAAC,OAAO,KAAK,UAAU;gBACtB,CAAC,CAAC,IAAI;gBACN,CAAC,CAAC,MAAM,CAAC,YAAY,CAAE,EAE7B,gBAAgB,EACd,gBAAgB;YAChB,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,wBAAwB,CAAC,EAE1E,UAAU;QACR,iEAAiE;QACjE,CAAC,GAAG,EAAE,EAAE;YACN,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;QAC3C,CAAC,EAEH,gBAAgB,EAAE,gBAAgB,YAEjC,QAAQ,GACI,CAChB,CAAC;AACJ,CAAC;AAED,SAAS,UAAU,CAAC,KAAyB,EAAE,IAAY;IACzD,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;QACxB,MAAM,IAAI,KAAK,CAAC,kCAAkC,IAAI,IAAI,CAAC,CAAC;IAC9D,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC"}
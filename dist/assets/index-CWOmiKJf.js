(function(){const l=document.createElement("link").relList;if(l&&l.supports&&l.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))u(o);new MutationObserver(o=>{for(const c of o)if(c.type==="childList")for(const d of c.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&u(d)}).observe(document,{childList:!0,subtree:!0});function r(o){const c={};return o.integrity&&(c.integrity=o.integrity),o.referrerPolicy&&(c.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?c.credentials="include":o.crossOrigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function u(o){if(o.ep)return;o.ep=!0;const c=r(o);fetch(o.href,c)}})();function Bp(a){return a&&a.__esModule&&Object.prototype.hasOwnProperty.call(a,"default")?a.default:a}var uc={exports:{}},Nl={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Rg;function O1(){if(Rg)return Nl;Rg=1;var a=Symbol.for("react.transitional.element"),l=Symbol.for("react.fragment");function r(u,o,c){var d=null;if(c!==void 0&&(d=""+c),o.key!==void 0&&(d=""+o.key),"key"in o){c={};for(var p in o)p!=="key"&&(c[p]=o[p])}else c=o;return o=c.ref,{$$typeof:a,type:u,key:d,ref:o!==void 0?o:null,props:c}}return Nl.Fragment=l,Nl.jsx=r,Nl.jsxs=r,Nl}var Tg;function M1(){return Tg||(Tg=1,uc.exports=O1()),uc.exports}var w=M1(),oc={exports:{}},ue={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Cg;function j1(){if(Cg)return ue;Cg=1;var a=Symbol.for("react.transitional.element"),l=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),u=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),c=Symbol.for("react.consumer"),d=Symbol.for("react.context"),p=Symbol.for("react.forward_ref"),h=Symbol.for("react.suspense"),m=Symbol.for("react.memo"),y=Symbol.for("react.lazy"),S=Symbol.iterator;function b(R){return R===null||typeof R!="object"?null:(R=S&&R[S]||R["@@iterator"],typeof R=="function"?R:null)}var x={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},_=Object.assign,M={};function L(R,V,X){this.props=R,this.context=V,this.refs=M,this.updater=X||x}L.prototype.isReactComponent={},L.prototype.setState=function(R,V){if(typeof R!="object"&&typeof R!="function"&&R!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,R,V,"setState")},L.prototype.forceUpdate=function(R){this.updater.enqueueForceUpdate(this,R,"forceUpdate")};function T(){}T.prototype=L.prototype;function P(R,V,X){this.props=R,this.context=V,this.refs=M,this.updater=X||x}var G=P.prototype=new T;G.constructor=P,_(G,L.prototype),G.isPureReactComponent=!0;var J=Array.isArray,Q={H:null,A:null,T:null,S:null,V:null},F=Object.prototype.hasOwnProperty;function I(R,V,X,K,ee,fe){return X=fe.ref,{$$typeof:a,type:R,key:V,ref:X!==void 0?X:null,props:fe}}function $(R,V){return I(R.type,V,void 0,void 0,void 0,R.props)}function re(R){return typeof R=="object"&&R!==null&&R.$$typeof===a}function ne(R){var V={"=":"=0",":":"=2"};return"$"+R.replace(/[=:]/g,function(X){return V[X]})}var pe=/\/+/g;function ge(R,V){return typeof R=="object"&&R!==null&&R.key!=null?ne(""+R.key):V.toString(36)}function Te(){}function Me(R){switch(R.status){case"fulfilled":return R.value;case"rejected":throw R.reason;default:switch(typeof R.status=="string"?R.then(Te,Te):(R.status="pending",R.then(function(V){R.status==="pending"&&(R.status="fulfilled",R.value=V)},function(V){R.status==="pending"&&(R.status="rejected",R.reason=V)})),R.status){case"fulfilled":return R.value;case"rejected":throw R.reason}}throw R}function Se(R,V,X,K,ee){var fe=typeof R;(fe==="undefined"||fe==="boolean")&&(R=null);var le=!1;if(R===null)le=!0;else switch(fe){case"bigint":case"string":case"number":le=!0;break;case"object":switch(R.$$typeof){case a:case l:le=!0;break;case y:return le=R._init,Se(le(R._payload),V,X,K,ee)}}if(le)return ee=ee(R),le=K===""?"."+ge(R,0):K,J(ee)?(X="",le!=null&&(X=le.replace(pe,"$&/")+"/"),Se(ee,V,X,"",function(nn){return nn})):ee!=null&&(re(ee)&&(ee=$(ee,X+(ee.key==null||R&&R.key===ee.key?"":(""+ee.key).replace(pe,"$&/")+"/")+le)),V.push(ee)),1;le=0;var ut=K===""?".":K+":";if(J(R))for(var ke=0;ke<R.length;ke++)K=R[ke],fe=ut+ge(K,ke),le+=Se(K,V,X,fe,ee);else if(ke=b(R),typeof ke=="function")for(R=ke.call(R),ke=0;!(K=R.next()).done;)K=K.value,fe=ut+ge(K,ke++),le+=Se(K,V,X,fe,ee);else if(fe==="object"){if(typeof R.then=="function")return Se(Me(R),V,X,K,ee);throw V=String(R),Error("Objects are not valid as a React child (found: "+(V==="[object Object]"?"object with keys {"+Object.keys(R).join(", ")+"}":V)+"). If you meant to render a collection of children, use an array instead.")}return le}function D(R,V,X){if(R==null)return R;var K=[],ee=0;return Se(R,K,"","",function(fe){return V.call(X,fe,ee++)}),K}function Y(R){if(R._status===-1){var V=R._result;V=V(),V.then(function(X){(R._status===0||R._status===-1)&&(R._status=1,R._result=X)},function(X){(R._status===0||R._status===-1)&&(R._status=2,R._result=X)}),R._status===-1&&(R._status=0,R._result=V)}if(R._status===1)return R._result.default;throw R._result}var W=typeof reportError=="function"?reportError:function(R){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var V=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof R=="object"&&R!==null&&typeof R.message=="string"?String(R.message):String(R),error:R});if(!window.dispatchEvent(V))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",R);return}console.error(R)};function ve(){}return ue.Children={map:D,forEach:function(R,V,X){D(R,function(){V.apply(this,arguments)},X)},count:function(R){var V=0;return D(R,function(){V++}),V},toArray:function(R){return D(R,function(V){return V})||[]},only:function(R){if(!re(R))throw Error("React.Children.only expected to receive a single React element child.");return R}},ue.Component=L,ue.Fragment=r,ue.Profiler=o,ue.PureComponent=P,ue.StrictMode=u,ue.Suspense=h,ue.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=Q,ue.__COMPILER_RUNTIME={__proto__:null,c:function(R){return Q.H.useMemoCache(R)}},ue.cache=function(R){return function(){return R.apply(null,arguments)}},ue.cloneElement=function(R,V,X){if(R==null)throw Error("The argument must be a React element, but you passed "+R+".");var K=_({},R.props),ee=R.key,fe=void 0;if(V!=null)for(le in V.ref!==void 0&&(fe=void 0),V.key!==void 0&&(ee=""+V.key),V)!F.call(V,le)||le==="key"||le==="__self"||le==="__source"||le==="ref"&&V.ref===void 0||(K[le]=V[le]);var le=arguments.length-2;if(le===1)K.children=X;else if(1<le){for(var ut=Array(le),ke=0;ke<le;ke++)ut[ke]=arguments[ke+2];K.children=ut}return I(R.type,ee,void 0,void 0,fe,K)},ue.createContext=function(R){return R={$$typeof:d,_currentValue:R,_currentValue2:R,_threadCount:0,Provider:null,Consumer:null},R.Provider=R,R.Consumer={$$typeof:c,_context:R},R},ue.createElement=function(R,V,X){var K,ee={},fe=null;if(V!=null)for(K in V.key!==void 0&&(fe=""+V.key),V)F.call(V,K)&&K!=="key"&&K!=="__self"&&K!=="__source"&&(ee[K]=V[K]);var le=arguments.length-2;if(le===1)ee.children=X;else if(1<le){for(var ut=Array(le),ke=0;ke<le;ke++)ut[ke]=arguments[ke+2];ee.children=ut}if(R&&R.defaultProps)for(K in le=R.defaultProps,le)ee[K]===void 0&&(ee[K]=le[K]);return I(R,fe,void 0,void 0,null,ee)},ue.createRef=function(){return{current:null}},ue.forwardRef=function(R){return{$$typeof:p,render:R}},ue.isValidElement=re,ue.lazy=function(R){return{$$typeof:y,_payload:{_status:-1,_result:R},_init:Y}},ue.memo=function(R,V){return{$$typeof:m,type:R,compare:V===void 0?null:V}},ue.startTransition=function(R){var V=Q.T,X={};Q.T=X;try{var K=R(),ee=Q.S;ee!==null&&ee(X,K),typeof K=="object"&&K!==null&&typeof K.then=="function"&&K.then(ve,W)}catch(fe){W(fe)}finally{Q.T=V}},ue.unstable_useCacheRefresh=function(){return Q.H.useCacheRefresh()},ue.use=function(R){return Q.H.use(R)},ue.useActionState=function(R,V,X){return Q.H.useActionState(R,V,X)},ue.useCallback=function(R,V){return Q.H.useCallback(R,V)},ue.useContext=function(R){return Q.H.useContext(R)},ue.useDebugValue=function(){},ue.useDeferredValue=function(R,V){return Q.H.useDeferredValue(R,V)},ue.useEffect=function(R,V,X){var K=Q.H;if(typeof X=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return K.useEffect(R,V)},ue.useId=function(){return Q.H.useId()},ue.useImperativeHandle=function(R,V,X){return Q.H.useImperativeHandle(R,V,X)},ue.useInsertionEffect=function(R,V){return Q.H.useInsertionEffect(R,V)},ue.useLayoutEffect=function(R,V){return Q.H.useLayoutEffect(R,V)},ue.useMemo=function(R,V){return Q.H.useMemo(R,V)},ue.useOptimistic=function(R,V){return Q.H.useOptimistic(R,V)},ue.useReducer=function(R,V,X){return Q.H.useReducer(R,V,X)},ue.useRef=function(R){return Q.H.useRef(R)},ue.useState=function(R){return Q.H.useState(R)},ue.useSyncExternalStore=function(R,V,X){return Q.H.useSyncExternalStore(R,V,X)},ue.useTransition=function(){return Q.H.useTransition()},ue.version="19.1.0",ue}var Ag;function ki(){return Ag||(Ag=1,oc.exports=j1()),oc.exports}var A=ki();const q=Bp(A);var cc={exports:{}},Dl={},fc={exports:{}},dc={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Og;function k1(){return Og||(Og=1,function(a){function l(D,Y){var W=D.length;D.push(Y);e:for(;0<W;){var ve=W-1>>>1,R=D[ve];if(0<o(R,Y))D[ve]=Y,D[W]=R,W=ve;else break e}}function r(D){return D.length===0?null:D[0]}function u(D){if(D.length===0)return null;var Y=D[0],W=D.pop();if(W!==Y){D[0]=W;e:for(var ve=0,R=D.length,V=R>>>1;ve<V;){var X=2*(ve+1)-1,K=D[X],ee=X+1,fe=D[ee];if(0>o(K,W))ee<R&&0>o(fe,K)?(D[ve]=fe,D[ee]=W,ve=ee):(D[ve]=K,D[X]=W,ve=X);else if(ee<R&&0>o(fe,W))D[ve]=fe,D[ee]=W,ve=ee;else break e}}return Y}function o(D,Y){var W=D.sortIndex-Y.sortIndex;return W!==0?W:D.id-Y.id}if(a.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var c=performance;a.unstable_now=function(){return c.now()}}else{var d=Date,p=d.now();a.unstable_now=function(){return d.now()-p}}var h=[],m=[],y=1,S=null,b=3,x=!1,_=!1,M=!1,L=!1,T=typeof setTimeout=="function"?setTimeout:null,P=typeof clearTimeout=="function"?clearTimeout:null,G=typeof setImmediate<"u"?setImmediate:null;function J(D){for(var Y=r(m);Y!==null;){if(Y.callback===null)u(m);else if(Y.startTime<=D)u(m),Y.sortIndex=Y.expirationTime,l(h,Y);else break;Y=r(m)}}function Q(D){if(M=!1,J(D),!_)if(r(h)!==null)_=!0,F||(F=!0,ge());else{var Y=r(m);Y!==null&&Se(Q,Y.startTime-D)}}var F=!1,I=-1,$=5,re=-1;function ne(){return L?!0:!(a.unstable_now()-re<$)}function pe(){if(L=!1,F){var D=a.unstable_now();re=D;var Y=!0;try{e:{_=!1,M&&(M=!1,P(I),I=-1),x=!0;var W=b;try{t:{for(J(D),S=r(h);S!==null&&!(S.expirationTime>D&&ne());){var ve=S.callback;if(typeof ve=="function"){S.callback=null,b=S.priorityLevel;var R=ve(S.expirationTime<=D);if(D=a.unstable_now(),typeof R=="function"){S.callback=R,J(D),Y=!0;break t}S===r(h)&&u(h),J(D)}else u(h);S=r(h)}if(S!==null)Y=!0;else{var V=r(m);V!==null&&Se(Q,V.startTime-D),Y=!1}}break e}finally{S=null,b=W,x=!1}Y=void 0}}finally{Y?ge():F=!1}}}var ge;if(typeof G=="function")ge=function(){G(pe)};else if(typeof MessageChannel<"u"){var Te=new MessageChannel,Me=Te.port2;Te.port1.onmessage=pe,ge=function(){Me.postMessage(null)}}else ge=function(){T(pe,0)};function Se(D,Y){I=T(function(){D(a.unstable_now())},Y)}a.unstable_IdlePriority=5,a.unstable_ImmediatePriority=1,a.unstable_LowPriority=4,a.unstable_NormalPriority=3,a.unstable_Profiling=null,a.unstable_UserBlockingPriority=2,a.unstable_cancelCallback=function(D){D.callback=null},a.unstable_forceFrameRate=function(D){0>D||125<D?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):$=0<D?Math.floor(1e3/D):5},a.unstable_getCurrentPriorityLevel=function(){return b},a.unstable_next=function(D){switch(b){case 1:case 2:case 3:var Y=3;break;default:Y=b}var W=b;b=Y;try{return D()}finally{b=W}},a.unstable_requestPaint=function(){L=!0},a.unstable_runWithPriority=function(D,Y){switch(D){case 1:case 2:case 3:case 4:case 5:break;default:D=3}var W=b;b=D;try{return Y()}finally{b=W}},a.unstable_scheduleCallback=function(D,Y,W){var ve=a.unstable_now();switch(typeof W=="object"&&W!==null?(W=W.delay,W=typeof W=="number"&&0<W?ve+W:ve):W=ve,D){case 1:var R=-1;break;case 2:R=250;break;case 5:R=1073741823;break;case 4:R=1e4;break;default:R=5e3}return R=W+R,D={id:y++,callback:Y,priorityLevel:D,startTime:W,expirationTime:R,sortIndex:-1},W>ve?(D.sortIndex=W,l(m,D),r(h)===null&&D===r(m)&&(M?(P(I),I=-1):M=!0,Se(Q,W-ve))):(D.sortIndex=R,l(h,D),_||x||(_=!0,F||(F=!0,ge()))),D},a.unstable_shouldYield=ne,a.unstable_wrapCallback=function(D){var Y=b;return function(){var W=b;b=Y;try{return D.apply(this,arguments)}finally{b=W}}}}(dc)),dc}var Mg;function U1(){return Mg||(Mg=1,fc.exports=k1()),fc.exports}var hc={exports:{}},ft={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var jg;function N1(){if(jg)return ft;jg=1;var a=ki();function l(h){var m="https://react.dev/errors/"+h;if(1<arguments.length){m+="?args[]="+encodeURIComponent(arguments[1]);for(var y=2;y<arguments.length;y++)m+="&args[]="+encodeURIComponent(arguments[y])}return"Minified React error #"+h+"; visit "+m+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function r(){}var u={d:{f:r,r:function(){throw Error(l(522))},D:r,C:r,L:r,m:r,X:r,S:r,M:r},p:0,findDOMNode:null},o=Symbol.for("react.portal");function c(h,m,y){var S=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:o,key:S==null?null:""+S,children:h,containerInfo:m,implementation:y}}var d=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function p(h,m){if(h==="font")return"";if(typeof m=="string")return m==="use-credentials"?m:""}return ft.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=u,ft.createPortal=function(h,m){var y=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!m||m.nodeType!==1&&m.nodeType!==9&&m.nodeType!==11)throw Error(l(299));return c(h,m,null,y)},ft.flushSync=function(h){var m=d.T,y=u.p;try{if(d.T=null,u.p=2,h)return h()}finally{d.T=m,u.p=y,u.d.f()}},ft.preconnect=function(h,m){typeof h=="string"&&(m?(m=m.crossOrigin,m=typeof m=="string"?m==="use-credentials"?m:"":void 0):m=null,u.d.C(h,m))},ft.prefetchDNS=function(h){typeof h=="string"&&u.d.D(h)},ft.preinit=function(h,m){if(typeof h=="string"&&m&&typeof m.as=="string"){var y=m.as,S=p(y,m.crossOrigin),b=typeof m.integrity=="string"?m.integrity:void 0,x=typeof m.fetchPriority=="string"?m.fetchPriority:void 0;y==="style"?u.d.S(h,typeof m.precedence=="string"?m.precedence:void 0,{crossOrigin:S,integrity:b,fetchPriority:x}):y==="script"&&u.d.X(h,{crossOrigin:S,integrity:b,fetchPriority:x,nonce:typeof m.nonce=="string"?m.nonce:void 0})}},ft.preinitModule=function(h,m){if(typeof h=="string")if(typeof m=="object"&&m!==null){if(m.as==null||m.as==="script"){var y=p(m.as,m.crossOrigin);u.d.M(h,{crossOrigin:y,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0})}}else m==null&&u.d.M(h)},ft.preload=function(h,m){if(typeof h=="string"&&typeof m=="object"&&m!==null&&typeof m.as=="string"){var y=m.as,S=p(y,m.crossOrigin);u.d.L(h,y,{crossOrigin:S,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0,type:typeof m.type=="string"?m.type:void 0,fetchPriority:typeof m.fetchPriority=="string"?m.fetchPriority:void 0,referrerPolicy:typeof m.referrerPolicy=="string"?m.referrerPolicy:void 0,imageSrcSet:typeof m.imageSrcSet=="string"?m.imageSrcSet:void 0,imageSizes:typeof m.imageSizes=="string"?m.imageSizes:void 0,media:typeof m.media=="string"?m.media:void 0})}},ft.preloadModule=function(h,m){if(typeof h=="string")if(m){var y=p(m.as,m.crossOrigin);u.d.m(h,{as:typeof m.as=="string"&&m.as!=="script"?m.as:void 0,crossOrigin:y,integrity:typeof m.integrity=="string"?m.integrity:void 0})}else u.d.m(h)},ft.requestFormReset=function(h){u.d.r(h)},ft.unstable_batchedUpdates=function(h,m){return h(m)},ft.useFormState=function(h,m,y){return d.H.useFormState(h,m,y)},ft.useFormStatus=function(){return d.H.useHostTransitionStatus()},ft.version="19.1.0",ft}var kg;function Hp(){if(kg)return hc.exports;kg=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(l){console.error(l)}}return a(),hc.exports=N1(),hc.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ug;function D1(){if(Ug)return Dl;Ug=1;var a=U1(),l=ki(),r=Hp();function u(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function c(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function d(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function p(e){if(c(e)!==e)throw Error(u(188))}function h(e){var t=e.alternate;if(!t){if(t=c(e),t===null)throw Error(u(188));return t!==e?null:e}for(var n=e,i=t;;){var s=n.return;if(s===null)break;var f=s.alternate;if(f===null){if(i=s.return,i!==null){n=i;continue}break}if(s.child===f.child){for(f=s.child;f;){if(f===n)return p(s),e;if(f===i)return p(s),t;f=f.sibling}throw Error(u(188))}if(n.return!==i.return)n=s,i=f;else{for(var g=!1,v=s.child;v;){if(v===n){g=!0,n=s,i=f;break}if(v===i){g=!0,i=s,n=f;break}v=v.sibling}if(!g){for(v=f.child;v;){if(v===n){g=!0,n=f,i=s;break}if(v===i){g=!0,i=f,n=s;break}v=v.sibling}if(!g)throw Error(u(189))}}if(n.alternate!==i)throw Error(u(190))}if(n.tag!==3)throw Error(u(188));return n.stateNode.current===n?e:t}function m(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=m(e),t!==null)return t;e=e.sibling}return null}var y=Object.assign,S=Symbol.for("react.element"),b=Symbol.for("react.transitional.element"),x=Symbol.for("react.portal"),_=Symbol.for("react.fragment"),M=Symbol.for("react.strict_mode"),L=Symbol.for("react.profiler"),T=Symbol.for("react.provider"),P=Symbol.for("react.consumer"),G=Symbol.for("react.context"),J=Symbol.for("react.forward_ref"),Q=Symbol.for("react.suspense"),F=Symbol.for("react.suspense_list"),I=Symbol.for("react.memo"),$=Symbol.for("react.lazy"),re=Symbol.for("react.activity"),ne=Symbol.for("react.memo_cache_sentinel"),pe=Symbol.iterator;function ge(e){return e===null||typeof e!="object"?null:(e=pe&&e[pe]||e["@@iterator"],typeof e=="function"?e:null)}var Te=Symbol.for("react.client.reference");function Me(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===Te?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case _:return"Fragment";case L:return"Profiler";case M:return"StrictMode";case Q:return"Suspense";case F:return"SuspenseList";case re:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case x:return"Portal";case G:return(e.displayName||"Context")+".Provider";case P:return(e._context.displayName||"Context")+".Consumer";case J:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case I:return t=e.displayName||null,t!==null?t:Me(e.type)||"Memo";case $:t=e._payload,e=e._init;try{return Me(e(t))}catch{}}return null}var Se=Array.isArray,D=l.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Y=r.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,W={pending:!1,data:null,method:null,action:null},ve=[],R=-1;function V(e){return{current:e}}function X(e){0>R||(e.current=ve[R],ve[R]=null,R--)}function K(e,t){R++,ve[R]=e.current,e.current=t}var ee=V(null),fe=V(null),le=V(null),ut=V(null);function ke(e,t){switch(K(le,t),K(fe,e),K(ee,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?Fm(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=Fm(t),e=eg(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}X(ee),K(ee,e)}function nn(){X(ee),X(fe),X(le)}function hn(e){e.memoizedState!==null&&K(ut,e);var t=ee.current,n=eg(t,e.type);t!==n&&(K(fe,e),K(ee,n))}function ga(e){fe.current===e&&(X(ee),X(fe)),ut.current===e&&(X(ut),Ol._currentValue=W)}var ar=Object.prototype.hasOwnProperty,_e=a.unstable_scheduleCallback,xe=a.unstable_cancelCallback,We=a.unstable_shouldYield,Le=a.unstable_requestPaint,Be=a.unstable_now,zn=a.unstable_getCurrentPriorityLevel,Ge=a.unstable_ImmediatePriority,Je=a.unstable_UserBlockingPriority,yt=a.unstable_NormalPriority,ir=a.unstable_LowPriority,zi=a.unstable_IdlePriority,lr=a.log,bt=a.unstable_setDisableYieldValue,mt=null,at=null;function an(e){if(typeof lr=="function"&&bt(e),at&&typeof at.setStrictMode=="function")try{at.setStrictMode(mt,e)}catch{}}var it=Math.clz32?Math.clz32:gy,hy=Math.log,my=Math.LN2;function gy(e){return e>>>=0,e===0?32:31-(hy(e)/my|0)|0}var rr=256,sr=4194304;function pa(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function ur(e,t,n){var i=e.pendingLanes;if(i===0)return 0;var s=0,f=e.suspendedLanes,g=e.pingedLanes;e=e.warmLanes;var v=i&134217727;return v!==0?(i=v&~f,i!==0?s=pa(i):(g&=v,g!==0?s=pa(g):n||(n=v&~e,n!==0&&(s=pa(n))))):(v=i&~f,v!==0?s=pa(v):g!==0?s=pa(g):n||(n=i&~e,n!==0&&(s=pa(n)))),s===0?0:t!==0&&t!==s&&(t&f)===0&&(f=s&-s,n=t&-t,f>=n||f===32&&(n&4194048)!==0)?t:s}function qi(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function py(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Lf(){var e=rr;return rr<<=1,(rr&4194048)===0&&(rr=256),e}function zf(){var e=sr;return sr<<=1,(sr&62914560)===0&&(sr=4194304),e}function Zs(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Bi(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function vy(e,t,n,i,s,f){var g=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var v=e.entanglements,E=e.expirationTimes,k=e.hiddenUpdates;for(n=g&~n;0<n;){var z=31-it(n),H=1<<z;v[z]=0,E[z]=-1;var U=k[z];if(U!==null)for(k[z]=null,z=0;z<U.length;z++){var N=U[z];N!==null&&(N.lane&=-536870913)}n&=~H}i!==0&&qf(e,i,0),f!==0&&s===0&&e.tag!==0&&(e.suspendedLanes|=f&~(g&~t))}function qf(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var i=31-it(t);e.entangledLanes|=t,e.entanglements[i]=e.entanglements[i]|1073741824|n&4194090}function Bf(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var i=31-it(n),s=1<<i;s&t|e[i]&t&&(e[i]|=t),n&=~s}}function Ws(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Js(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Hf(){var e=Y.p;return e!==0?e:(e=window.event,e===void 0?32:bg(e.type))}function yy(e,t){var n=Y.p;try{return Y.p=e,t()}finally{Y.p=n}}var qn=Math.random().toString(36).slice(2),ot="__reactFiber$"+qn,St="__reactProps$"+qn,qa="__reactContainer$"+qn,Fs="__reactEvents$"+qn,by="__reactListeners$"+qn,Sy="__reactHandles$"+qn,Vf="__reactResources$"+qn,Hi="__reactMarker$"+qn;function eu(e){delete e[ot],delete e[St],delete e[Fs],delete e[by],delete e[Sy]}function Ba(e){var t=e[ot];if(t)return t;for(var n=e.parentNode;n;){if(t=n[qa]||n[ot]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=ig(e);e!==null;){if(n=e[ot])return n;e=ig(e)}return t}e=n,n=e.parentNode}return null}function Ha(e){if(e=e[ot]||e[qa]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function Vi(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(u(33))}function Va(e){var t=e[Vf];return t||(t=e[Vf]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Fe(e){e[Hi]=!0}var Pf=new Set,Qf={};function va(e,t){Pa(e,t),Pa(e+"Capture",t)}function Pa(e,t){for(Qf[e]=t,e=0;e<t.length;e++)Pf.add(t[e])}var Ey=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Yf={},Gf={};function xy(e){return ar.call(Gf,e)?!0:ar.call(Yf,e)?!1:Ey.test(e)?Gf[e]=!0:(Yf[e]=!0,!1)}function or(e,t,n){if(xy(t))if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var i=t.toLowerCase().slice(0,5);if(i!=="data-"&&i!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+n)}}function cr(e,t,n){if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+n)}}function mn(e,t,n,i){if(i===null)e.removeAttribute(n);else{switch(typeof i){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(n);return}e.setAttributeNS(t,n,""+i)}}var tu,Xf;function Qa(e){if(tu===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);tu=t&&t[1]||"",Xf=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+tu+e+Xf}var nu=!1;function au(e,t){if(!e||nu)return"";nu=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var i={DetermineComponentFrameRoot:function(){try{if(t){var H=function(){throw Error()};if(Object.defineProperty(H.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(H,[])}catch(N){var U=N}Reflect.construct(e,[],H)}else{try{H.call()}catch(N){U=N}e.call(H.prototype)}}else{try{throw Error()}catch(N){U=N}(H=e())&&typeof H.catch=="function"&&H.catch(function(){})}}catch(N){if(N&&U&&typeof N.stack=="string")return[N.stack,U.stack]}return[null,null]}};i.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var s=Object.getOwnPropertyDescriptor(i.DetermineComponentFrameRoot,"name");s&&s.configurable&&Object.defineProperty(i.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var f=i.DetermineComponentFrameRoot(),g=f[0],v=f[1];if(g&&v){var E=g.split(`
`),k=v.split(`
`);for(s=i=0;i<E.length&&!E[i].includes("DetermineComponentFrameRoot");)i++;for(;s<k.length&&!k[s].includes("DetermineComponentFrameRoot");)s++;if(i===E.length||s===k.length)for(i=E.length-1,s=k.length-1;1<=i&&0<=s&&E[i]!==k[s];)s--;for(;1<=i&&0<=s;i--,s--)if(E[i]!==k[s]){if(i!==1||s!==1)do if(i--,s--,0>s||E[i]!==k[s]){var z=`
`+E[i].replace(" at new "," at ");return e.displayName&&z.includes("<anonymous>")&&(z=z.replace("<anonymous>",e.displayName)),z}while(1<=i&&0<=s);break}}}finally{nu=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?Qa(n):""}function _y(e){switch(e.tag){case 26:case 27:case 5:return Qa(e.type);case 16:return Qa("Lazy");case 13:return Qa("Suspense");case 19:return Qa("SuspenseList");case 0:case 15:return au(e.type,!1);case 11:return au(e.type.render,!1);case 1:return au(e.type,!0);case 31:return Qa("Activity");default:return""}}function $f(e){try{var t="";do t+=_y(e),e=e.return;while(e);return t}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function Dt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Kf(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function wy(e){var t=Kf(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),i=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var s=n.get,f=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return s.call(this)},set:function(g){i=""+g,f.call(this,g)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return i},setValue:function(g){i=""+g},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function fr(e){e._valueTracker||(e._valueTracker=wy(e))}function If(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),i="";return e&&(i=Kf(e)?e.checked?"true":"false":e.value),e=i,e!==n?(t.setValue(e),!0):!1}function dr(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var Ry=/[\n"\\]/g;function Lt(e){return e.replace(Ry,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function iu(e,t,n,i,s,f,g,v){e.name="",g!=null&&typeof g!="function"&&typeof g!="symbol"&&typeof g!="boolean"?e.type=g:e.removeAttribute("type"),t!=null?g==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+Dt(t)):e.value!==""+Dt(t)&&(e.value=""+Dt(t)):g!=="submit"&&g!=="reset"||e.removeAttribute("value"),t!=null?lu(e,g,Dt(t)):n!=null?lu(e,g,Dt(n)):i!=null&&e.removeAttribute("value"),s==null&&f!=null&&(e.defaultChecked=!!f),s!=null&&(e.checked=s&&typeof s!="function"&&typeof s!="symbol"),v!=null&&typeof v!="function"&&typeof v!="symbol"&&typeof v!="boolean"?e.name=""+Dt(v):e.removeAttribute("name")}function Zf(e,t,n,i,s,f,g,v){if(f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(e.type=f),t!=null||n!=null){if(!(f!=="submit"&&f!=="reset"||t!=null))return;n=n!=null?""+Dt(n):"",t=t!=null?""+Dt(t):n,v||t===e.value||(e.value=t),e.defaultValue=t}i=i??s,i=typeof i!="function"&&typeof i!="symbol"&&!!i,e.checked=v?e.checked:!!i,e.defaultChecked=!!i,g!=null&&typeof g!="function"&&typeof g!="symbol"&&typeof g!="boolean"&&(e.name=g)}function lu(e,t,n){t==="number"&&dr(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function Ya(e,t,n,i){if(e=e.options,t){t={};for(var s=0;s<n.length;s++)t["$"+n[s]]=!0;for(n=0;n<e.length;n++)s=t.hasOwnProperty("$"+e[n].value),e[n].selected!==s&&(e[n].selected=s),s&&i&&(e[n].defaultSelected=!0)}else{for(n=""+Dt(n),t=null,s=0;s<e.length;s++){if(e[s].value===n){e[s].selected=!0,i&&(e[s].defaultSelected=!0);return}t!==null||e[s].disabled||(t=e[s])}t!==null&&(t.selected=!0)}}function Wf(e,t,n){if(t!=null&&(t=""+Dt(t),t!==e.value&&(e.value=t),n==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=n!=null?""+Dt(n):""}function Jf(e,t,n,i){if(t==null){if(i!=null){if(n!=null)throw Error(u(92));if(Se(i)){if(1<i.length)throw Error(u(93));i=i[0]}n=i}n==null&&(n=""),t=n}n=Dt(t),e.defaultValue=n,i=e.textContent,i===n&&i!==""&&i!==null&&(e.value=i)}function Ga(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Ty=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Ff(e,t,n){var i=t.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?i?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":i?e.setProperty(t,n):typeof n!="number"||n===0||Ty.has(t)?t==="float"?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function ed(e,t,n){if(t!=null&&typeof t!="object")throw Error(u(62));if(e=e.style,n!=null){for(var i in n)!n.hasOwnProperty(i)||t!=null&&t.hasOwnProperty(i)||(i.indexOf("--")===0?e.setProperty(i,""):i==="float"?e.cssFloat="":e[i]="");for(var s in t)i=t[s],t.hasOwnProperty(s)&&n[s]!==i&&Ff(e,s,i)}else for(var f in t)t.hasOwnProperty(f)&&Ff(e,f,t[f])}function ru(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Cy=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Ay=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function hr(e){return Ay.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var su=null;function uu(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Xa=null,$a=null;function td(e){var t=Ha(e);if(t&&(e=t.stateNode)){var n=e[St]||null;e:switch(e=t.stateNode,t.type){case"input":if(iu(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+Lt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var i=n[t];if(i!==e&&i.form===e.form){var s=i[St]||null;if(!s)throw Error(u(90));iu(i,s.value,s.defaultValue,s.defaultValue,s.checked,s.defaultChecked,s.type,s.name)}}for(t=0;t<n.length;t++)i=n[t],i.form===e.form&&If(i)}break e;case"textarea":Wf(e,n.value,n.defaultValue);break e;case"select":t=n.value,t!=null&&Ya(e,!!n.multiple,t,!1)}}}var ou=!1;function nd(e,t,n){if(ou)return e(t,n);ou=!0;try{var i=e(t);return i}finally{if(ou=!1,(Xa!==null||$a!==null)&&(Jr(),Xa&&(t=Xa,e=$a,$a=Xa=null,td(t),e)))for(t=0;t<e.length;t++)td(e[t])}}function Pi(e,t){var n=e.stateNode;if(n===null)return null;var i=n[St]||null;if(i===null)return null;n=i[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(i=!i.disabled)||(e=e.type,i=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!i;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(u(231,t,typeof n));return n}var gn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),cu=!1;if(gn)try{var Qi={};Object.defineProperty(Qi,"passive",{get:function(){cu=!0}}),window.addEventListener("test",Qi,Qi),window.removeEventListener("test",Qi,Qi)}catch{cu=!1}var Bn=null,fu=null,mr=null;function ad(){if(mr)return mr;var e,t=fu,n=t.length,i,s="value"in Bn?Bn.value:Bn.textContent,f=s.length;for(e=0;e<n&&t[e]===s[e];e++);var g=n-e;for(i=1;i<=g&&t[n-i]===s[f-i];i++);return mr=s.slice(e,1<i?1-i:void 0)}function gr(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function pr(){return!0}function id(){return!1}function Et(e){function t(n,i,s,f,g){this._reactName=n,this._targetInst=s,this.type=i,this.nativeEvent=f,this.target=g,this.currentTarget=null;for(var v in e)e.hasOwnProperty(v)&&(n=e[v],this[v]=n?n(f):f[v]);return this.isDefaultPrevented=(f.defaultPrevented!=null?f.defaultPrevented:f.returnValue===!1)?pr:id,this.isPropagationStopped=id,this}return y(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=pr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=pr)},persist:function(){},isPersistent:pr}),t}var ya={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},vr=Et(ya),Yi=y({},ya,{view:0,detail:0}),Oy=Et(Yi),du,hu,Gi,yr=y({},Yi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:gu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Gi&&(Gi&&e.type==="mousemove"?(du=e.screenX-Gi.screenX,hu=e.screenY-Gi.screenY):hu=du=0,Gi=e),du)},movementY:function(e){return"movementY"in e?e.movementY:hu}}),ld=Et(yr),My=y({},yr,{dataTransfer:0}),jy=Et(My),ky=y({},Yi,{relatedTarget:0}),mu=Et(ky),Uy=y({},ya,{animationName:0,elapsedTime:0,pseudoElement:0}),Ny=Et(Uy),Dy=y({},ya,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Ly=Et(Dy),zy=y({},ya,{data:0}),rd=Et(zy),qy={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},By={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Hy={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Vy(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Hy[e])?!!t[e]:!1}function gu(){return Vy}var Py=y({},Yi,{key:function(e){if(e.key){var t=qy[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=gr(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?By[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:gu,charCode:function(e){return e.type==="keypress"?gr(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?gr(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Qy=Et(Py),Yy=y({},yr,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),sd=Et(Yy),Gy=y({},Yi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:gu}),Xy=Et(Gy),$y=y({},ya,{propertyName:0,elapsedTime:0,pseudoElement:0}),Ky=Et($y),Iy=y({},yr,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Zy=Et(Iy),Wy=y({},ya,{newState:0,oldState:0}),Jy=Et(Wy),Fy=[9,13,27,32],pu=gn&&"CompositionEvent"in window,Xi=null;gn&&"documentMode"in document&&(Xi=document.documentMode);var e0=gn&&"TextEvent"in window&&!Xi,ud=gn&&(!pu||Xi&&8<Xi&&11>=Xi),od=" ",cd=!1;function fd(e,t){switch(e){case"keyup":return Fy.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function dd(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Ka=!1;function t0(e,t){switch(e){case"compositionend":return dd(t);case"keypress":return t.which!==32?null:(cd=!0,od);case"textInput":return e=t.data,e===od&&cd?null:e;default:return null}}function n0(e,t){if(Ka)return e==="compositionend"||!pu&&fd(e,t)?(e=ad(),mr=fu=Bn=null,Ka=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return ud&&t.locale!=="ko"?null:t.data;default:return null}}var a0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function hd(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!a0[e.type]:t==="textarea"}function md(e,t,n,i){Xa?$a?$a.push(i):$a=[i]:Xa=i,t=is(t,"onChange"),0<t.length&&(n=new vr("onChange","change",null,n,i),e.push({event:n,listeners:t}))}var $i=null,Ki=null;function i0(e){Km(e,0)}function br(e){var t=Vi(e);if(If(t))return e}function gd(e,t){if(e==="change")return t}var pd=!1;if(gn){var vu;if(gn){var yu="oninput"in document;if(!yu){var vd=document.createElement("div");vd.setAttribute("oninput","return;"),yu=typeof vd.oninput=="function"}vu=yu}else vu=!1;pd=vu&&(!document.documentMode||9<document.documentMode)}function yd(){$i&&($i.detachEvent("onpropertychange",bd),Ki=$i=null)}function bd(e){if(e.propertyName==="value"&&br(Ki)){var t=[];md(t,Ki,e,uu(e)),nd(i0,t)}}function l0(e,t,n){e==="focusin"?(yd(),$i=t,Ki=n,$i.attachEvent("onpropertychange",bd)):e==="focusout"&&yd()}function r0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return br(Ki)}function s0(e,t){if(e==="click")return br(t)}function u0(e,t){if(e==="input"||e==="change")return br(t)}function o0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ct=typeof Object.is=="function"?Object.is:o0;function Ii(e,t){if(Ct(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),i=Object.keys(t);if(n.length!==i.length)return!1;for(i=0;i<n.length;i++){var s=n[i];if(!ar.call(t,s)||!Ct(e[s],t[s]))return!1}return!0}function Sd(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Ed(e,t){var n=Sd(e);e=0;for(var i;n;){if(n.nodeType===3){if(i=e+n.textContent.length,e<=t&&i>=t)return{node:n,offset:t-e};e=i}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Sd(n)}}function xd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?xd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function _d(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=dr(e.document);t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=dr(e.document)}return t}function bu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var c0=gn&&"documentMode"in document&&11>=document.documentMode,Ia=null,Su=null,Zi=null,Eu=!1;function wd(e,t,n){var i=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Eu||Ia==null||Ia!==dr(i)||(i=Ia,"selectionStart"in i&&bu(i)?i={start:i.selectionStart,end:i.selectionEnd}:(i=(i.ownerDocument&&i.ownerDocument.defaultView||window).getSelection(),i={anchorNode:i.anchorNode,anchorOffset:i.anchorOffset,focusNode:i.focusNode,focusOffset:i.focusOffset}),Zi&&Ii(Zi,i)||(Zi=i,i=is(Su,"onSelect"),0<i.length&&(t=new vr("onSelect","select",null,t,n),e.push({event:t,listeners:i}),t.target=Ia)))}function ba(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Za={animationend:ba("Animation","AnimationEnd"),animationiteration:ba("Animation","AnimationIteration"),animationstart:ba("Animation","AnimationStart"),transitionrun:ba("Transition","TransitionRun"),transitionstart:ba("Transition","TransitionStart"),transitioncancel:ba("Transition","TransitionCancel"),transitionend:ba("Transition","TransitionEnd")},xu={},Rd={};gn&&(Rd=document.createElement("div").style,"AnimationEvent"in window||(delete Za.animationend.animation,delete Za.animationiteration.animation,delete Za.animationstart.animation),"TransitionEvent"in window||delete Za.transitionend.transition);function Sa(e){if(xu[e])return xu[e];if(!Za[e])return e;var t=Za[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Rd)return xu[e]=t[n];return e}var Td=Sa("animationend"),Cd=Sa("animationiteration"),Ad=Sa("animationstart"),f0=Sa("transitionrun"),d0=Sa("transitionstart"),h0=Sa("transitioncancel"),Od=Sa("transitionend"),Md=new Map,_u="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");_u.push("scrollEnd");function Kt(e,t){Md.set(e,t),va(t,[e])}var jd=new WeakMap;function zt(e,t){if(typeof e=="object"&&e!==null){var n=jd.get(e);return n!==void 0?n:(t={value:e,source:t,stack:$f(t)},jd.set(e,t),t)}return{value:e,source:t,stack:$f(t)}}var qt=[],Wa=0,wu=0;function Sr(){for(var e=Wa,t=wu=Wa=0;t<e;){var n=qt[t];qt[t++]=null;var i=qt[t];qt[t++]=null;var s=qt[t];qt[t++]=null;var f=qt[t];if(qt[t++]=null,i!==null&&s!==null){var g=i.pending;g===null?s.next=s:(s.next=g.next,g.next=s),i.pending=s}f!==0&&kd(n,s,f)}}function Er(e,t,n,i){qt[Wa++]=e,qt[Wa++]=t,qt[Wa++]=n,qt[Wa++]=i,wu|=i,e.lanes|=i,e=e.alternate,e!==null&&(e.lanes|=i)}function Ru(e,t,n,i){return Er(e,t,n,i),xr(e)}function Ja(e,t){return Er(e,null,null,t),xr(e)}function kd(e,t,n){e.lanes|=n;var i=e.alternate;i!==null&&(i.lanes|=n);for(var s=!1,f=e.return;f!==null;)f.childLanes|=n,i=f.alternate,i!==null&&(i.childLanes|=n),f.tag===22&&(e=f.stateNode,e===null||e._visibility&1||(s=!0)),e=f,f=f.return;return e.tag===3?(f=e.stateNode,s&&t!==null&&(s=31-it(n),e=f.hiddenUpdates,i=e[s],i===null?e[s]=[t]:i.push(t),t.lane=n|536870912),f):null}function xr(e){if(50<El)throw El=0,ko=null,Error(u(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Fa={};function m0(e,t,n,i){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=i,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function At(e,t,n,i){return new m0(e,t,n,i)}function Tu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function pn(e,t){var n=e.alternate;return n===null?(n=At(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&65011712,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Ud(e,t){e.flags&=65011714;var n=e.alternate;return n===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function _r(e,t,n,i,s,f){var g=0;if(i=e,typeof e=="function")Tu(e)&&(g=1);else if(typeof e=="string")g=p1(e,n,ee.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case re:return e=At(31,n,t,s),e.elementType=re,e.lanes=f,e;case _:return Ea(n.children,s,f,t);case M:g=8,s|=24;break;case L:return e=At(12,n,t,s|2),e.elementType=L,e.lanes=f,e;case Q:return e=At(13,n,t,s),e.elementType=Q,e.lanes=f,e;case F:return e=At(19,n,t,s),e.elementType=F,e.lanes=f,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case T:case G:g=10;break e;case P:g=9;break e;case J:g=11;break e;case I:g=14;break e;case $:g=16,i=null;break e}g=29,n=Error(u(130,e===null?"null":typeof e,"")),i=null}return t=At(g,n,t,s),t.elementType=e,t.type=i,t.lanes=f,t}function Ea(e,t,n,i){return e=At(7,e,i,t),e.lanes=n,e}function Cu(e,t,n){return e=At(6,e,null,t),e.lanes=n,e}function Au(e,t,n){return t=At(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var ei=[],ti=0,wr=null,Rr=0,Bt=[],Ht=0,xa=null,vn=1,yn="";function _a(e,t){ei[ti++]=Rr,ei[ti++]=wr,wr=e,Rr=t}function Nd(e,t,n){Bt[Ht++]=vn,Bt[Ht++]=yn,Bt[Ht++]=xa,xa=e;var i=vn;e=yn;var s=32-it(i)-1;i&=~(1<<s),n+=1;var f=32-it(t)+s;if(30<f){var g=s-s%5;f=(i&(1<<g)-1).toString(32),i>>=g,s-=g,vn=1<<32-it(t)+s|n<<s|i,yn=f+e}else vn=1<<f|n<<s|i,yn=e}function Ou(e){e.return!==null&&(_a(e,1),Nd(e,1,0))}function Mu(e){for(;e===wr;)wr=ei[--ti],ei[ti]=null,Rr=ei[--ti],ei[ti]=null;for(;e===xa;)xa=Bt[--Ht],Bt[Ht]=null,yn=Bt[--Ht],Bt[Ht]=null,vn=Bt[--Ht],Bt[Ht]=null}var gt=null,He=null,Ee=!1,wa=null,ln=!1,ju=Error(u(519));function Ra(e){var t=Error(u(418,""));throw Fi(zt(t,e)),ju}function Dd(e){var t=e.stateNode,n=e.type,i=e.memoizedProps;switch(t[ot]=e,t[St]=i,n){case"dialog":me("cancel",t),me("close",t);break;case"iframe":case"object":case"embed":me("load",t);break;case"video":case"audio":for(n=0;n<_l.length;n++)me(_l[n],t);break;case"source":me("error",t);break;case"img":case"image":case"link":me("error",t),me("load",t);break;case"details":me("toggle",t);break;case"input":me("invalid",t),Zf(t,i.value,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name,!0),fr(t);break;case"select":me("invalid",t);break;case"textarea":me("invalid",t),Jf(t,i.value,i.defaultValue,i.children),fr(t)}n=i.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||t.textContent===""+n||i.suppressHydrationWarning===!0||Jm(t.textContent,n)?(i.popover!=null&&(me("beforetoggle",t),me("toggle",t)),i.onScroll!=null&&me("scroll",t),i.onScrollEnd!=null&&me("scrollend",t),i.onClick!=null&&(t.onclick=ls),t=!0):t=!1,t||Ra(e)}function Ld(e){for(gt=e.return;gt;)switch(gt.tag){case 5:case 13:ln=!1;return;case 27:case 3:ln=!0;return;default:gt=gt.return}}function Wi(e){if(e!==gt)return!1;if(!Ee)return Ld(e),Ee=!0,!1;var t=e.tag,n;if((n=t!==3&&t!==27)&&((n=t===5)&&(n=e.type,n=!(n!=="form"&&n!=="button")||Ko(e.type,e.memoizedProps)),n=!n),n&&He&&Ra(e),Ld(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(u(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(n=e.data,n==="/$"){if(t===0){He=Zt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++;e=e.nextSibling}He=null}}else t===27?(t=He,ta(e.type)?(e=Jo,Jo=null,He=e):He=t):He=gt?Zt(e.stateNode.nextSibling):null;return!0}function Ji(){He=gt=null,Ee=!1}function zd(){var e=wa;return e!==null&&(wt===null?wt=e:wt.push.apply(wt,e),wa=null),e}function Fi(e){wa===null?wa=[e]:wa.push(e)}var ku=V(null),Ta=null,bn=null;function Hn(e,t,n){K(ku,t._currentValue),t._currentValue=n}function Sn(e){e._currentValue=ku.current,X(ku)}function Uu(e,t,n){for(;e!==null;){var i=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,i!==null&&(i.childLanes|=t)):i!==null&&(i.childLanes&t)!==t&&(i.childLanes|=t),e===n)break;e=e.return}}function Nu(e,t,n,i){var s=e.child;for(s!==null&&(s.return=e);s!==null;){var f=s.dependencies;if(f!==null){var g=s.child;f=f.firstContext;e:for(;f!==null;){var v=f;f=s;for(var E=0;E<t.length;E++)if(v.context===t[E]){f.lanes|=n,v=f.alternate,v!==null&&(v.lanes|=n),Uu(f.return,n,e),i||(g=null);break e}f=v.next}}else if(s.tag===18){if(g=s.return,g===null)throw Error(u(341));g.lanes|=n,f=g.alternate,f!==null&&(f.lanes|=n),Uu(g,n,e),g=null}else g=s.child;if(g!==null)g.return=s;else for(g=s;g!==null;){if(g===e){g=null;break}if(s=g.sibling,s!==null){s.return=g.return,g=s;break}g=g.return}s=g}}function el(e,t,n,i){e=null;for(var s=t,f=!1;s!==null;){if(!f){if((s.flags&524288)!==0)f=!0;else if((s.flags&262144)!==0)break}if(s.tag===10){var g=s.alternate;if(g===null)throw Error(u(387));if(g=g.memoizedProps,g!==null){var v=s.type;Ct(s.pendingProps.value,g.value)||(e!==null?e.push(v):e=[v])}}else if(s===ut.current){if(g=s.alternate,g===null)throw Error(u(387));g.memoizedState.memoizedState!==s.memoizedState.memoizedState&&(e!==null?e.push(Ol):e=[Ol])}s=s.return}e!==null&&Nu(t,e,n,i),t.flags|=262144}function Tr(e){for(e=e.firstContext;e!==null;){if(!Ct(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Ca(e){Ta=e,bn=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function ct(e){return qd(Ta,e)}function Cr(e,t){return Ta===null&&Ca(e),qd(e,t)}function qd(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},bn===null){if(e===null)throw Error(u(308));bn=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else bn=bn.next=t;return n}var g0=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(n,i){e.push(i)}};this.abort=function(){t.aborted=!0,e.forEach(function(n){return n()})}},p0=a.unstable_scheduleCallback,v0=a.unstable_NormalPriority,Ke={$$typeof:G,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Du(){return{controller:new g0,data:new Map,refCount:0}}function tl(e){e.refCount--,e.refCount===0&&p0(v0,function(){e.controller.abort()})}var nl=null,Lu=0,ni=0,ai=null;function y0(e,t){if(nl===null){var n=nl=[];Lu=0,ni=Bo(),ai={status:"pending",value:void 0,then:function(i){n.push(i)}}}return Lu++,t.then(Bd,Bd),t}function Bd(){if(--Lu===0&&nl!==null){ai!==null&&(ai.status="fulfilled");var e=nl;nl=null,ni=0,ai=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function b0(e,t){var n=[],i={status:"pending",value:null,reason:null,then:function(s){n.push(s)}};return e.then(function(){i.status="fulfilled",i.value=t;for(var s=0;s<n.length;s++)(0,n[s])(t)},function(s){for(i.status="rejected",i.reason=s,s=0;s<n.length;s++)(0,n[s])(void 0)}),i}var Hd=D.S;D.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&y0(e,t),Hd!==null&&Hd(e,t)};var Aa=V(null);function zu(){var e=Aa.current;return e!==null?e:Ue.pooledCache}function Ar(e,t){t===null?K(Aa,Aa.current):K(Aa,t.pool)}function Vd(){var e=zu();return e===null?null:{parent:Ke._currentValue,pool:e}}var al=Error(u(460)),Pd=Error(u(474)),Or=Error(u(542)),qu={then:function(){}};function Qd(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Mr(){}function Yd(e,t,n){switch(n=e[n],n===void 0?e.push(t):n!==t&&(t.then(Mr,Mr),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Xd(e),e;default:if(typeof t.status=="string")t.then(Mr,Mr);else{if(e=Ue,e!==null&&100<e.shellSuspendCounter)throw Error(u(482));e=t,e.status="pending",e.then(function(i){if(t.status==="pending"){var s=t;s.status="fulfilled",s.value=i}},function(i){if(t.status==="pending"){var s=t;s.status="rejected",s.reason=i}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Xd(e),e}throw il=t,al}}var il=null;function Gd(){if(il===null)throw Error(u(459));var e=il;return il=null,e}function Xd(e){if(e===al||e===Or)throw Error(u(483))}var Vn=!1;function Bu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Hu(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Pn(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Qn(e,t,n){var i=e.updateQueue;if(i===null)return null;if(i=i.shared,(we&2)!==0){var s=i.pending;return s===null?t.next=t:(t.next=s.next,s.next=t),i.pending=t,t=xr(e),kd(e,null,n),t}return Er(e,i,t,n),xr(e)}function ll(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194048)!==0)){var i=t.lanes;i&=e.pendingLanes,n|=i,t.lanes=n,Bf(e,n)}}function Vu(e,t){var n=e.updateQueue,i=e.alternate;if(i!==null&&(i=i.updateQueue,n===i)){var s=null,f=null;if(n=n.firstBaseUpdate,n!==null){do{var g={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};f===null?s=f=g:f=f.next=g,n=n.next}while(n!==null);f===null?s=f=t:f=f.next=t}else s=f=t;n={baseState:i.baseState,firstBaseUpdate:s,lastBaseUpdate:f,shared:i.shared,callbacks:i.callbacks},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var Pu=!1;function rl(){if(Pu){var e=ai;if(e!==null)throw e}}function sl(e,t,n,i){Pu=!1;var s=e.updateQueue;Vn=!1;var f=s.firstBaseUpdate,g=s.lastBaseUpdate,v=s.shared.pending;if(v!==null){s.shared.pending=null;var E=v,k=E.next;E.next=null,g===null?f=k:g.next=k,g=E;var z=e.alternate;z!==null&&(z=z.updateQueue,v=z.lastBaseUpdate,v!==g&&(v===null?z.firstBaseUpdate=k:v.next=k,z.lastBaseUpdate=E))}if(f!==null){var H=s.baseState;g=0,z=k=E=null,v=f;do{var U=v.lane&-536870913,N=U!==v.lane;if(N?(ye&U)===U:(i&U)===U){U!==0&&U===ni&&(Pu=!0),z!==null&&(z=z.next={lane:0,tag:v.tag,payload:v.payload,callback:null,next:null});e:{var se=e,ae=v;U=t;var Oe=n;switch(ae.tag){case 1:if(se=ae.payload,typeof se=="function"){H=se.call(Oe,H,U);break e}H=se;break e;case 3:se.flags=se.flags&-65537|128;case 0:if(se=ae.payload,U=typeof se=="function"?se.call(Oe,H,U):se,U==null)break e;H=y({},H,U);break e;case 2:Vn=!0}}U=v.callback,U!==null&&(e.flags|=64,N&&(e.flags|=8192),N=s.callbacks,N===null?s.callbacks=[U]:N.push(U))}else N={lane:U,tag:v.tag,payload:v.payload,callback:v.callback,next:null},z===null?(k=z=N,E=H):z=z.next=N,g|=U;if(v=v.next,v===null){if(v=s.shared.pending,v===null)break;N=v,v=N.next,N.next=null,s.lastBaseUpdate=N,s.shared.pending=null}}while(!0);z===null&&(E=H),s.baseState=E,s.firstBaseUpdate=k,s.lastBaseUpdate=z,f===null&&(s.shared.lanes=0),Wn|=g,e.lanes=g,e.memoizedState=H}}function $d(e,t){if(typeof e!="function")throw Error(u(191,e));e.call(t)}function Kd(e,t){var n=e.callbacks;if(n!==null)for(e.callbacks=null,e=0;e<n.length;e++)$d(n[e],t)}var ii=V(null),jr=V(0);function Id(e,t){e=Cn,K(jr,e),K(ii,t),Cn=e|t.baseLanes}function Qu(){K(jr,Cn),K(ii,ii.current)}function Yu(){Cn=jr.current,X(ii),X(jr)}var Yn=0,ce=null,Ce=null,Xe=null,kr=!1,li=!1,Oa=!1,Ur=0,ul=0,ri=null,S0=0;function Qe(){throw Error(u(321))}function Gu(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Ct(e[n],t[n]))return!1;return!0}function Xu(e,t,n,i,s,f){return Yn=f,ce=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,D.H=e===null||e.memoizedState===null?kh:Uh,Oa=!1,f=n(i,s),Oa=!1,li&&(f=Wd(t,n,i,s)),Zd(e),f}function Zd(e){D.H=Br;var t=Ce!==null&&Ce.next!==null;if(Yn=0,Xe=Ce=ce=null,kr=!1,ul=0,ri=null,t)throw Error(u(300));e===null||et||(e=e.dependencies,e!==null&&Tr(e)&&(et=!0))}function Wd(e,t,n,i){ce=e;var s=0;do{if(li&&(ri=null),ul=0,li=!1,25<=s)throw Error(u(301));if(s+=1,Xe=Ce=null,e.updateQueue!=null){var f=e.updateQueue;f.lastEffect=null,f.events=null,f.stores=null,f.memoCache!=null&&(f.memoCache.index=0)}D.H=C0,f=t(n,i)}while(li);return f}function E0(){var e=D.H,t=e.useState()[0];return t=typeof t.then=="function"?ol(t):t,e=e.useState()[0],(Ce!==null?Ce.memoizedState:null)!==e&&(ce.flags|=1024),t}function $u(){var e=Ur!==0;return Ur=0,e}function Ku(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Iu(e){if(kr){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}kr=!1}Yn=0,Xe=Ce=ce=null,li=!1,ul=Ur=0,ri=null}function xt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Xe===null?ce.memoizedState=Xe=e:Xe=Xe.next=e,Xe}function $e(){if(Ce===null){var e=ce.alternate;e=e!==null?e.memoizedState:null}else e=Ce.next;var t=Xe===null?ce.memoizedState:Xe.next;if(t!==null)Xe=t,Ce=e;else{if(e===null)throw ce.alternate===null?Error(u(467)):Error(u(310));Ce=e,e={memoizedState:Ce.memoizedState,baseState:Ce.baseState,baseQueue:Ce.baseQueue,queue:Ce.queue,next:null},Xe===null?ce.memoizedState=Xe=e:Xe=Xe.next=e}return Xe}function Zu(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function ol(e){var t=ul;return ul+=1,ri===null&&(ri=[]),e=Yd(ri,e,t),t=ce,(Xe===null?t.memoizedState:Xe.next)===null&&(t=t.alternate,D.H=t===null||t.memoizedState===null?kh:Uh),e}function Nr(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return ol(e);if(e.$$typeof===G)return ct(e)}throw Error(u(438,String(e)))}function Wu(e){var t=null,n=ce.updateQueue;if(n!==null&&(t=n.memoCache),t==null){var i=ce.alternate;i!==null&&(i=i.updateQueue,i!==null&&(i=i.memoCache,i!=null&&(t={data:i.data.map(function(s){return s.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),n===null&&(n=Zu(),ce.updateQueue=n),n.memoCache=t,n=t.data[t.index],n===void 0)for(n=t.data[t.index]=Array(e),i=0;i<e;i++)n[i]=ne;return t.index++,n}function En(e,t){return typeof t=="function"?t(e):t}function Dr(e){var t=$e();return Ju(t,Ce,e)}function Ju(e,t,n){var i=e.queue;if(i===null)throw Error(u(311));i.lastRenderedReducer=n;var s=e.baseQueue,f=i.pending;if(f!==null){if(s!==null){var g=s.next;s.next=f.next,f.next=g}t.baseQueue=s=f,i.pending=null}if(f=e.baseState,s===null)e.memoizedState=f;else{t=s.next;var v=g=null,E=null,k=t,z=!1;do{var H=k.lane&-536870913;if(H!==k.lane?(ye&H)===H:(Yn&H)===H){var U=k.revertLane;if(U===0)E!==null&&(E=E.next={lane:0,revertLane:0,action:k.action,hasEagerState:k.hasEagerState,eagerState:k.eagerState,next:null}),H===ni&&(z=!0);else if((Yn&U)===U){k=k.next,U===ni&&(z=!0);continue}else H={lane:0,revertLane:k.revertLane,action:k.action,hasEagerState:k.hasEagerState,eagerState:k.eagerState,next:null},E===null?(v=E=H,g=f):E=E.next=H,ce.lanes|=U,Wn|=U;H=k.action,Oa&&n(f,H),f=k.hasEagerState?k.eagerState:n(f,H)}else U={lane:H,revertLane:k.revertLane,action:k.action,hasEagerState:k.hasEagerState,eagerState:k.eagerState,next:null},E===null?(v=E=U,g=f):E=E.next=U,ce.lanes|=H,Wn|=H;k=k.next}while(k!==null&&k!==t);if(E===null?g=f:E.next=v,!Ct(f,e.memoizedState)&&(et=!0,z&&(n=ai,n!==null)))throw n;e.memoizedState=f,e.baseState=g,e.baseQueue=E,i.lastRenderedState=f}return s===null&&(i.lanes=0),[e.memoizedState,i.dispatch]}function Fu(e){var t=$e(),n=t.queue;if(n===null)throw Error(u(311));n.lastRenderedReducer=e;var i=n.dispatch,s=n.pending,f=t.memoizedState;if(s!==null){n.pending=null;var g=s=s.next;do f=e(f,g.action),g=g.next;while(g!==s);Ct(f,t.memoizedState)||(et=!0),t.memoizedState=f,t.baseQueue===null&&(t.baseState=f),n.lastRenderedState=f}return[f,i]}function Jd(e,t,n){var i=ce,s=$e(),f=Ee;if(f){if(n===void 0)throw Error(u(407));n=n()}else n=t();var g=!Ct((Ce||s).memoizedState,n);g&&(s.memoizedState=n,et=!0),s=s.queue;var v=th.bind(null,i,s,e);if(cl(2048,8,v,[e]),s.getSnapshot!==t||g||Xe!==null&&Xe.memoizedState.tag&1){if(i.flags|=2048,si(9,Lr(),eh.bind(null,i,s,n,t),null),Ue===null)throw Error(u(349));f||(Yn&124)!==0||Fd(i,t,n)}return n}function Fd(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ce.updateQueue,t===null?(t=Zu(),ce.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function eh(e,t,n,i){t.value=n,t.getSnapshot=i,nh(t)&&ah(e)}function th(e,t,n){return n(function(){nh(t)&&ah(e)})}function nh(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Ct(e,n)}catch{return!0}}function ah(e){var t=Ja(e,2);t!==null&&Ut(t,e,2)}function eo(e){var t=xt();if(typeof e=="function"){var n=e;if(e=n(),Oa){an(!0);try{n()}finally{an(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:En,lastRenderedState:e},t}function ih(e,t,n,i){return e.baseState=n,Ju(e,Ce,typeof i=="function"?i:En)}function x0(e,t,n,i,s){if(qr(e))throw Error(u(485));if(e=t.action,e!==null){var f={payload:s,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(g){f.listeners.push(g)}};D.T!==null?n(!0):f.isTransition=!1,i(f),n=t.pending,n===null?(f.next=t.pending=f,lh(t,f)):(f.next=n.next,t.pending=n.next=f)}}function lh(e,t){var n=t.action,i=t.payload,s=e.state;if(t.isTransition){var f=D.T,g={};D.T=g;try{var v=n(s,i),E=D.S;E!==null&&E(g,v),rh(e,t,v)}catch(k){to(e,t,k)}finally{D.T=f}}else try{f=n(s,i),rh(e,t,f)}catch(k){to(e,t,k)}}function rh(e,t,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(i){sh(e,t,i)},function(i){return to(e,t,i)}):sh(e,t,n)}function sh(e,t,n){t.status="fulfilled",t.value=n,uh(t),e.state=n,t=e.pending,t!==null&&(n=t.next,n===t?e.pending=null:(n=n.next,t.next=n,lh(e,n)))}function to(e,t,n){var i=e.pending;if(e.pending=null,i!==null){i=i.next;do t.status="rejected",t.reason=n,uh(t),t=t.next;while(t!==i)}e.action=null}function uh(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function oh(e,t){return t}function ch(e,t){if(Ee){var n=Ue.formState;if(n!==null){e:{var i=ce;if(Ee){if(He){t:{for(var s=He,f=ln;s.nodeType!==8;){if(!f){s=null;break t}if(s=Zt(s.nextSibling),s===null){s=null;break t}}f=s.data,s=f==="F!"||f==="F"?s:null}if(s){He=Zt(s.nextSibling),i=s.data==="F!";break e}}Ra(i)}i=!1}i&&(t=n[0])}}return n=xt(),n.memoizedState=n.baseState=t,i={pending:null,lanes:0,dispatch:null,lastRenderedReducer:oh,lastRenderedState:t},n.queue=i,n=Oh.bind(null,ce,i),i.dispatch=n,i=eo(!1),f=ro.bind(null,ce,!1,i.queue),i=xt(),s={state:t,dispatch:null,action:e,pending:null},i.queue=s,n=x0.bind(null,ce,s,f,n),s.dispatch=n,i.memoizedState=e,[t,n,!1]}function fh(e){var t=$e();return dh(t,Ce,e)}function dh(e,t,n){if(t=Ju(e,t,oh)[0],e=Dr(En)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var i=ol(t)}catch(g){throw g===al?Or:g}else i=t;t=$e();var s=t.queue,f=s.dispatch;return n!==t.memoizedState&&(ce.flags|=2048,si(9,Lr(),_0.bind(null,s,n),null)),[i,f,e]}function _0(e,t){e.action=t}function hh(e){var t=$e(),n=Ce;if(n!==null)return dh(t,n,e);$e(),t=t.memoizedState,n=$e();var i=n.queue.dispatch;return n.memoizedState=e,[t,i,!1]}function si(e,t,n,i){return e={tag:e,create:n,deps:i,inst:t,next:null},t=ce.updateQueue,t===null&&(t=Zu(),ce.updateQueue=t),n=t.lastEffect,n===null?t.lastEffect=e.next=e:(i=n.next,n.next=e,e.next=i,t.lastEffect=e),e}function Lr(){return{destroy:void 0,resource:void 0}}function mh(){return $e().memoizedState}function zr(e,t,n,i){var s=xt();i=i===void 0?null:i,ce.flags|=e,s.memoizedState=si(1|t,Lr(),n,i)}function cl(e,t,n,i){var s=$e();i=i===void 0?null:i;var f=s.memoizedState.inst;Ce!==null&&i!==null&&Gu(i,Ce.memoizedState.deps)?s.memoizedState=si(t,f,n,i):(ce.flags|=e,s.memoizedState=si(1|t,f,n,i))}function gh(e,t){zr(8390656,8,e,t)}function ph(e,t){cl(2048,8,e,t)}function vh(e,t){return cl(4,2,e,t)}function yh(e,t){return cl(4,4,e,t)}function bh(e,t){if(typeof t=="function"){e=e();var n=t(e);return function(){typeof n=="function"?n():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Sh(e,t,n){n=n!=null?n.concat([e]):null,cl(4,4,bh.bind(null,t,e),n)}function no(){}function Eh(e,t){var n=$e();t=t===void 0?null:t;var i=n.memoizedState;return t!==null&&Gu(t,i[1])?i[0]:(n.memoizedState=[e,t],e)}function xh(e,t){var n=$e();t=t===void 0?null:t;var i=n.memoizedState;if(t!==null&&Gu(t,i[1]))return i[0];if(i=e(),Oa){an(!0);try{e()}finally{an(!1)}}return n.memoizedState=[i,t],i}function ao(e,t,n){return n===void 0||(Yn&1073741824)!==0?e.memoizedState=t:(e.memoizedState=n,e=Rm(),ce.lanes|=e,Wn|=e,n)}function _h(e,t,n,i){return Ct(n,t)?n:ii.current!==null?(e=ao(e,n,i),Ct(e,t)||(et=!0),e):(Yn&42)===0?(et=!0,e.memoizedState=n):(e=Rm(),ce.lanes|=e,Wn|=e,t)}function wh(e,t,n,i,s){var f=Y.p;Y.p=f!==0&&8>f?f:8;var g=D.T,v={};D.T=v,ro(e,!1,t,n);try{var E=s(),k=D.S;if(k!==null&&k(v,E),E!==null&&typeof E=="object"&&typeof E.then=="function"){var z=b0(E,i);fl(e,t,z,kt(e))}else fl(e,t,i,kt(e))}catch(H){fl(e,t,{then:function(){},status:"rejected",reason:H},kt())}finally{Y.p=f,D.T=g}}function w0(){}function io(e,t,n,i){if(e.tag!==5)throw Error(u(476));var s=Rh(e).queue;wh(e,s,t,W,n===null?w0:function(){return Th(e),n(i)})}function Rh(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:W,baseState:W,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:En,lastRenderedState:W},next:null};var n={};return t.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:En,lastRenderedState:n},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function Th(e){var t=Rh(e).next.queue;fl(e,t,{},kt())}function lo(){return ct(Ol)}function Ch(){return $e().memoizedState}function Ah(){return $e().memoizedState}function R0(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var n=kt();e=Pn(n);var i=Qn(t,e,n);i!==null&&(Ut(i,t,n),ll(i,t,n)),t={cache:Du()},e.payload=t;return}t=t.return}}function T0(e,t,n){var i=kt();n={lane:i,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},qr(e)?Mh(t,n):(n=Ru(e,t,n,i),n!==null&&(Ut(n,e,i),jh(n,t,i)))}function Oh(e,t,n){var i=kt();fl(e,t,n,i)}function fl(e,t,n,i){var s={lane:i,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(qr(e))Mh(t,s);else{var f=e.alternate;if(e.lanes===0&&(f===null||f.lanes===0)&&(f=t.lastRenderedReducer,f!==null))try{var g=t.lastRenderedState,v=f(g,n);if(s.hasEagerState=!0,s.eagerState=v,Ct(v,g))return Er(e,t,s,0),Ue===null&&Sr(),!1}catch{}finally{}if(n=Ru(e,t,s,i),n!==null)return Ut(n,e,i),jh(n,t,i),!0}return!1}function ro(e,t,n,i){if(i={lane:2,revertLane:Bo(),action:i,hasEagerState:!1,eagerState:null,next:null},qr(e)){if(t)throw Error(u(479))}else t=Ru(e,n,i,2),t!==null&&Ut(t,e,2)}function qr(e){var t=e.alternate;return e===ce||t!==null&&t===ce}function Mh(e,t){li=kr=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function jh(e,t,n){if((n&4194048)!==0){var i=t.lanes;i&=e.pendingLanes,n|=i,t.lanes=n,Bf(e,n)}}var Br={readContext:ct,use:Nr,useCallback:Qe,useContext:Qe,useEffect:Qe,useImperativeHandle:Qe,useLayoutEffect:Qe,useInsertionEffect:Qe,useMemo:Qe,useReducer:Qe,useRef:Qe,useState:Qe,useDebugValue:Qe,useDeferredValue:Qe,useTransition:Qe,useSyncExternalStore:Qe,useId:Qe,useHostTransitionStatus:Qe,useFormState:Qe,useActionState:Qe,useOptimistic:Qe,useMemoCache:Qe,useCacheRefresh:Qe},kh={readContext:ct,use:Nr,useCallback:function(e,t){return xt().memoizedState=[e,t===void 0?null:t],e},useContext:ct,useEffect:gh,useImperativeHandle:function(e,t,n){n=n!=null?n.concat([e]):null,zr(4194308,4,bh.bind(null,t,e),n)},useLayoutEffect:function(e,t){return zr(4194308,4,e,t)},useInsertionEffect:function(e,t){zr(4,2,e,t)},useMemo:function(e,t){var n=xt();t=t===void 0?null:t;var i=e();if(Oa){an(!0);try{e()}finally{an(!1)}}return n.memoizedState=[i,t],i},useReducer:function(e,t,n){var i=xt();if(n!==void 0){var s=n(t);if(Oa){an(!0);try{n(t)}finally{an(!1)}}}else s=t;return i.memoizedState=i.baseState=s,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:s},i.queue=e,e=e.dispatch=T0.bind(null,ce,e),[i.memoizedState,e]},useRef:function(e){var t=xt();return e={current:e},t.memoizedState=e},useState:function(e){e=eo(e);var t=e.queue,n=Oh.bind(null,ce,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:no,useDeferredValue:function(e,t){var n=xt();return ao(n,e,t)},useTransition:function(){var e=eo(!1);return e=wh.bind(null,ce,e.queue,!0,!1),xt().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var i=ce,s=xt();if(Ee){if(n===void 0)throw Error(u(407));n=n()}else{if(n=t(),Ue===null)throw Error(u(349));(ye&124)!==0||Fd(i,t,n)}s.memoizedState=n;var f={value:n,getSnapshot:t};return s.queue=f,gh(th.bind(null,i,f,e),[e]),i.flags|=2048,si(9,Lr(),eh.bind(null,i,f,n,t),null),n},useId:function(){var e=xt(),t=Ue.identifierPrefix;if(Ee){var n=yn,i=vn;n=(i&~(1<<32-it(i)-1)).toString(32)+n,t="«"+t+"R"+n,n=Ur++,0<n&&(t+="H"+n.toString(32)),t+="»"}else n=S0++,t="«"+t+"r"+n.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:lo,useFormState:ch,useActionState:ch,useOptimistic:function(e){var t=xt();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=ro.bind(null,ce,!0,n),n.dispatch=t,[e,t]},useMemoCache:Wu,useCacheRefresh:function(){return xt().memoizedState=R0.bind(null,ce)}},Uh={readContext:ct,use:Nr,useCallback:Eh,useContext:ct,useEffect:ph,useImperativeHandle:Sh,useInsertionEffect:vh,useLayoutEffect:yh,useMemo:xh,useReducer:Dr,useRef:mh,useState:function(){return Dr(En)},useDebugValue:no,useDeferredValue:function(e,t){var n=$e();return _h(n,Ce.memoizedState,e,t)},useTransition:function(){var e=Dr(En)[0],t=$e().memoizedState;return[typeof e=="boolean"?e:ol(e),t]},useSyncExternalStore:Jd,useId:Ch,useHostTransitionStatus:lo,useFormState:fh,useActionState:fh,useOptimistic:function(e,t){var n=$e();return ih(n,Ce,e,t)},useMemoCache:Wu,useCacheRefresh:Ah},C0={readContext:ct,use:Nr,useCallback:Eh,useContext:ct,useEffect:ph,useImperativeHandle:Sh,useInsertionEffect:vh,useLayoutEffect:yh,useMemo:xh,useReducer:Fu,useRef:mh,useState:function(){return Fu(En)},useDebugValue:no,useDeferredValue:function(e,t){var n=$e();return Ce===null?ao(n,e,t):_h(n,Ce.memoizedState,e,t)},useTransition:function(){var e=Fu(En)[0],t=$e().memoizedState;return[typeof e=="boolean"?e:ol(e),t]},useSyncExternalStore:Jd,useId:Ch,useHostTransitionStatus:lo,useFormState:hh,useActionState:hh,useOptimistic:function(e,t){var n=$e();return Ce!==null?ih(n,Ce,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Wu,useCacheRefresh:Ah},ui=null,dl=0;function Hr(e){var t=dl;return dl+=1,ui===null&&(ui=[]),Yd(ui,e,t)}function hl(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Vr(e,t){throw t.$$typeof===S?Error(u(525)):(e=Object.prototype.toString.call(t),Error(u(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function Nh(e){var t=e._init;return t(e._payload)}function Dh(e){function t(O,C){if(e){var j=O.deletions;j===null?(O.deletions=[C],O.flags|=16):j.push(C)}}function n(O,C){if(!e)return null;for(;C!==null;)t(O,C),C=C.sibling;return null}function i(O){for(var C=new Map;O!==null;)O.key!==null?C.set(O.key,O):C.set(O.index,O),O=O.sibling;return C}function s(O,C){return O=pn(O,C),O.index=0,O.sibling=null,O}function f(O,C,j){return O.index=j,e?(j=O.alternate,j!==null?(j=j.index,j<C?(O.flags|=67108866,C):j):(O.flags|=67108866,C)):(O.flags|=1048576,C)}function g(O){return e&&O.alternate===null&&(O.flags|=67108866),O}function v(O,C,j,B){return C===null||C.tag!==6?(C=Cu(j,O.mode,B),C.return=O,C):(C=s(C,j),C.return=O,C)}function E(O,C,j,B){var Z=j.type;return Z===_?z(O,C,j.props.children,B,j.key):C!==null&&(C.elementType===Z||typeof Z=="object"&&Z!==null&&Z.$$typeof===$&&Nh(Z)===C.type)?(C=s(C,j.props),hl(C,j),C.return=O,C):(C=_r(j.type,j.key,j.props,null,O.mode,B),hl(C,j),C.return=O,C)}function k(O,C,j,B){return C===null||C.tag!==4||C.stateNode.containerInfo!==j.containerInfo||C.stateNode.implementation!==j.implementation?(C=Au(j,O.mode,B),C.return=O,C):(C=s(C,j.children||[]),C.return=O,C)}function z(O,C,j,B,Z){return C===null||C.tag!==7?(C=Ea(j,O.mode,B,Z),C.return=O,C):(C=s(C,j),C.return=O,C)}function H(O,C,j){if(typeof C=="string"&&C!==""||typeof C=="number"||typeof C=="bigint")return C=Cu(""+C,O.mode,j),C.return=O,C;if(typeof C=="object"&&C!==null){switch(C.$$typeof){case b:return j=_r(C.type,C.key,C.props,null,O.mode,j),hl(j,C),j.return=O,j;case x:return C=Au(C,O.mode,j),C.return=O,C;case $:var B=C._init;return C=B(C._payload),H(O,C,j)}if(Se(C)||ge(C))return C=Ea(C,O.mode,j,null),C.return=O,C;if(typeof C.then=="function")return H(O,Hr(C),j);if(C.$$typeof===G)return H(O,Cr(O,C),j);Vr(O,C)}return null}function U(O,C,j,B){var Z=C!==null?C.key:null;if(typeof j=="string"&&j!==""||typeof j=="number"||typeof j=="bigint")return Z!==null?null:v(O,C,""+j,B);if(typeof j=="object"&&j!==null){switch(j.$$typeof){case b:return j.key===Z?E(O,C,j,B):null;case x:return j.key===Z?k(O,C,j,B):null;case $:return Z=j._init,j=Z(j._payload),U(O,C,j,B)}if(Se(j)||ge(j))return Z!==null?null:z(O,C,j,B,null);if(typeof j.then=="function")return U(O,C,Hr(j),B);if(j.$$typeof===G)return U(O,C,Cr(O,j),B);Vr(O,j)}return null}function N(O,C,j,B,Z){if(typeof B=="string"&&B!==""||typeof B=="number"||typeof B=="bigint")return O=O.get(j)||null,v(C,O,""+B,Z);if(typeof B=="object"&&B!==null){switch(B.$$typeof){case b:return O=O.get(B.key===null?j:B.key)||null,E(C,O,B,Z);case x:return O=O.get(B.key===null?j:B.key)||null,k(C,O,B,Z);case $:var de=B._init;return B=de(B._payload),N(O,C,j,B,Z)}if(Se(B)||ge(B))return O=O.get(j)||null,z(C,O,B,Z,null);if(typeof B.then=="function")return N(O,C,j,Hr(B),Z);if(B.$$typeof===G)return N(O,C,j,Cr(C,B),Z);Vr(C,B)}return null}function se(O,C,j,B){for(var Z=null,de=null,te=C,ie=C=0,nt=null;te!==null&&ie<j.length;ie++){te.index>ie?(nt=te,te=null):nt=te.sibling;var be=U(O,te,j[ie],B);if(be===null){te===null&&(te=nt);break}e&&te&&be.alternate===null&&t(O,te),C=f(be,C,ie),de===null?Z=be:de.sibling=be,de=be,te=nt}if(ie===j.length)return n(O,te),Ee&&_a(O,ie),Z;if(te===null){for(;ie<j.length;ie++)te=H(O,j[ie],B),te!==null&&(C=f(te,C,ie),de===null?Z=te:de.sibling=te,de=te);return Ee&&_a(O,ie),Z}for(te=i(te);ie<j.length;ie++)nt=N(te,O,ie,j[ie],B),nt!==null&&(e&&nt.alternate!==null&&te.delete(nt.key===null?ie:nt.key),C=f(nt,C,ie),de===null?Z=nt:de.sibling=nt,de=nt);return e&&te.forEach(function(ra){return t(O,ra)}),Ee&&_a(O,ie),Z}function ae(O,C,j,B){if(j==null)throw Error(u(151));for(var Z=null,de=null,te=C,ie=C=0,nt=null,be=j.next();te!==null&&!be.done;ie++,be=j.next()){te.index>ie?(nt=te,te=null):nt=te.sibling;var ra=U(O,te,be.value,B);if(ra===null){te===null&&(te=nt);break}e&&te&&ra.alternate===null&&t(O,te),C=f(ra,C,ie),de===null?Z=ra:de.sibling=ra,de=ra,te=nt}if(be.done)return n(O,te),Ee&&_a(O,ie),Z;if(te===null){for(;!be.done;ie++,be=j.next())be=H(O,be.value,B),be!==null&&(C=f(be,C,ie),de===null?Z=be:de.sibling=be,de=be);return Ee&&_a(O,ie),Z}for(te=i(te);!be.done;ie++,be=j.next())be=N(te,O,ie,be.value,B),be!==null&&(e&&be.alternate!==null&&te.delete(be.key===null?ie:be.key),C=f(be,C,ie),de===null?Z=be:de.sibling=be,de=be);return e&&te.forEach(function(A1){return t(O,A1)}),Ee&&_a(O,ie),Z}function Oe(O,C,j,B){if(typeof j=="object"&&j!==null&&j.type===_&&j.key===null&&(j=j.props.children),typeof j=="object"&&j!==null){switch(j.$$typeof){case b:e:{for(var Z=j.key;C!==null;){if(C.key===Z){if(Z=j.type,Z===_){if(C.tag===7){n(O,C.sibling),B=s(C,j.props.children),B.return=O,O=B;break e}}else if(C.elementType===Z||typeof Z=="object"&&Z!==null&&Z.$$typeof===$&&Nh(Z)===C.type){n(O,C.sibling),B=s(C,j.props),hl(B,j),B.return=O,O=B;break e}n(O,C);break}else t(O,C);C=C.sibling}j.type===_?(B=Ea(j.props.children,O.mode,B,j.key),B.return=O,O=B):(B=_r(j.type,j.key,j.props,null,O.mode,B),hl(B,j),B.return=O,O=B)}return g(O);case x:e:{for(Z=j.key;C!==null;){if(C.key===Z)if(C.tag===4&&C.stateNode.containerInfo===j.containerInfo&&C.stateNode.implementation===j.implementation){n(O,C.sibling),B=s(C,j.children||[]),B.return=O,O=B;break e}else{n(O,C);break}else t(O,C);C=C.sibling}B=Au(j,O.mode,B),B.return=O,O=B}return g(O);case $:return Z=j._init,j=Z(j._payload),Oe(O,C,j,B)}if(Se(j))return se(O,C,j,B);if(ge(j)){if(Z=ge(j),typeof Z!="function")throw Error(u(150));return j=Z.call(j),ae(O,C,j,B)}if(typeof j.then=="function")return Oe(O,C,Hr(j),B);if(j.$$typeof===G)return Oe(O,C,Cr(O,j),B);Vr(O,j)}return typeof j=="string"&&j!==""||typeof j=="number"||typeof j=="bigint"?(j=""+j,C!==null&&C.tag===6?(n(O,C.sibling),B=s(C,j),B.return=O,O=B):(n(O,C),B=Cu(j,O.mode,B),B.return=O,O=B),g(O)):n(O,C)}return function(O,C,j,B){try{dl=0;var Z=Oe(O,C,j,B);return ui=null,Z}catch(te){if(te===al||te===Or)throw te;var de=At(29,te,null,O.mode);return de.lanes=B,de.return=O,de}finally{}}}var oi=Dh(!0),Lh=Dh(!1),Vt=V(null),rn=null;function Gn(e){var t=e.alternate;K(Ie,Ie.current&1),K(Vt,e),rn===null&&(t===null||ii.current!==null||t.memoizedState!==null)&&(rn=e)}function zh(e){if(e.tag===22){if(K(Ie,Ie.current),K(Vt,e),rn===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(rn=e)}}else Xn()}function Xn(){K(Ie,Ie.current),K(Vt,Vt.current)}function xn(e){X(Vt),rn===e&&(rn=null),X(Ie)}var Ie=V(0);function Pr(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||Wo(n)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function so(e,t,n,i){t=e.memoizedState,n=n(i,t),n=n==null?t:y({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var uo={enqueueSetState:function(e,t,n){e=e._reactInternals;var i=kt(),s=Pn(i);s.payload=t,n!=null&&(s.callback=n),t=Qn(e,s,i),t!==null&&(Ut(t,e,i),ll(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var i=kt(),s=Pn(i);s.tag=1,s.payload=t,n!=null&&(s.callback=n),t=Qn(e,s,i),t!==null&&(Ut(t,e,i),ll(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=kt(),i=Pn(n);i.tag=2,t!=null&&(i.callback=t),t=Qn(e,i,n),t!==null&&(Ut(t,e,n),ll(t,e,n))}};function qh(e,t,n,i,s,f,g){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(i,f,g):t.prototype&&t.prototype.isPureReactComponent?!Ii(n,i)||!Ii(s,f):!0}function Bh(e,t,n,i){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,i),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,i),t.state!==e&&uo.enqueueReplaceState(t,t.state,null)}function Ma(e,t){var n=t;if("ref"in t){n={};for(var i in t)i!=="ref"&&(n[i]=t[i])}if(e=e.defaultProps){n===t&&(n=y({},n));for(var s in e)n[s]===void 0&&(n[s]=e[s])}return n}var Qr=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Hh(e){Qr(e)}function Vh(e){console.error(e)}function Ph(e){Qr(e)}function Yr(e,t){try{var n=e.onUncaughtError;n(t.value,{componentStack:t.stack})}catch(i){setTimeout(function(){throw i})}}function Qh(e,t,n){try{var i=e.onCaughtError;i(n.value,{componentStack:n.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(s){setTimeout(function(){throw s})}}function oo(e,t,n){return n=Pn(n),n.tag=3,n.payload={element:null},n.callback=function(){Yr(e,t)},n}function Yh(e){return e=Pn(e),e.tag=3,e}function Gh(e,t,n,i){var s=n.type.getDerivedStateFromError;if(typeof s=="function"){var f=i.value;e.payload=function(){return s(f)},e.callback=function(){Qh(t,n,i)}}var g=n.stateNode;g!==null&&typeof g.componentDidCatch=="function"&&(e.callback=function(){Qh(t,n,i),typeof s!="function"&&(Jn===null?Jn=new Set([this]):Jn.add(this));var v=i.stack;this.componentDidCatch(i.value,{componentStack:v!==null?v:""})})}function A0(e,t,n,i,s){if(n.flags|=32768,i!==null&&typeof i=="object"&&typeof i.then=="function"){if(t=n.alternate,t!==null&&el(t,n,s,!0),n=Vt.current,n!==null){switch(n.tag){case 13:return rn===null?No():n.alternate===null&&Ve===0&&(Ve=3),n.flags&=-257,n.flags|=65536,n.lanes=s,i===qu?n.flags|=16384:(t=n.updateQueue,t===null?n.updateQueue=new Set([i]):t.add(i),Lo(e,i,s)),!1;case 22:return n.flags|=65536,i===qu?n.flags|=16384:(t=n.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([i])},n.updateQueue=t):(n=t.retryQueue,n===null?t.retryQueue=new Set([i]):n.add(i)),Lo(e,i,s)),!1}throw Error(u(435,n.tag))}return Lo(e,i,s),No(),!1}if(Ee)return t=Vt.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=s,i!==ju&&(e=Error(u(422),{cause:i}),Fi(zt(e,n)))):(i!==ju&&(t=Error(u(423),{cause:i}),Fi(zt(t,n))),e=e.current.alternate,e.flags|=65536,s&=-s,e.lanes|=s,i=zt(i,n),s=oo(e.stateNode,i,s),Vu(e,s),Ve!==4&&(Ve=2)),!1;var f=Error(u(520),{cause:i});if(f=zt(f,n),Sl===null?Sl=[f]:Sl.push(f),Ve!==4&&(Ve=2),t===null)return!0;i=zt(i,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=s&-s,n.lanes|=e,e=oo(n.stateNode,i,e),Vu(n,e),!1;case 1:if(t=n.type,f=n.stateNode,(n.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||f!==null&&typeof f.componentDidCatch=="function"&&(Jn===null||!Jn.has(f))))return n.flags|=65536,s&=-s,n.lanes|=s,s=Yh(s),Gh(s,e,n,i),Vu(n,s),!1}n=n.return}while(n!==null);return!1}var Xh=Error(u(461)),et=!1;function lt(e,t,n,i){t.child=e===null?Lh(t,null,n,i):oi(t,e.child,n,i)}function $h(e,t,n,i,s){n=n.render;var f=t.ref;if("ref"in i){var g={};for(var v in i)v!=="ref"&&(g[v]=i[v])}else g=i;return Ca(t),i=Xu(e,t,n,g,f,s),v=$u(),e!==null&&!et?(Ku(e,t,s),_n(e,t,s)):(Ee&&v&&Ou(t),t.flags|=1,lt(e,t,i,s),t.child)}function Kh(e,t,n,i,s){if(e===null){var f=n.type;return typeof f=="function"&&!Tu(f)&&f.defaultProps===void 0&&n.compare===null?(t.tag=15,t.type=f,Ih(e,t,f,i,s)):(e=_r(n.type,null,i,t,t.mode,s),e.ref=t.ref,e.return=t,t.child=e)}if(f=e.child,!yo(e,s)){var g=f.memoizedProps;if(n=n.compare,n=n!==null?n:Ii,n(g,i)&&e.ref===t.ref)return _n(e,t,s)}return t.flags|=1,e=pn(f,i),e.ref=t.ref,e.return=t,t.child=e}function Ih(e,t,n,i,s){if(e!==null){var f=e.memoizedProps;if(Ii(f,i)&&e.ref===t.ref)if(et=!1,t.pendingProps=i=f,yo(e,s))(e.flags&131072)!==0&&(et=!0);else return t.lanes=e.lanes,_n(e,t,s)}return co(e,t,n,i,s)}function Zh(e,t,n){var i=t.pendingProps,s=i.children,f=e!==null?e.memoizedState:null;if(i.mode==="hidden"){if((t.flags&128)!==0){if(i=f!==null?f.baseLanes|n:n,e!==null){for(s=t.child=e.child,f=0;s!==null;)f=f|s.lanes|s.childLanes,s=s.sibling;t.childLanes=f&~i}else t.childLanes=0,t.child=null;return Wh(e,t,i,n)}if((n&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&Ar(t,f!==null?f.cachePool:null),f!==null?Id(t,f):Qu(),zh(t);else return t.lanes=t.childLanes=536870912,Wh(e,t,f!==null?f.baseLanes|n:n,n)}else f!==null?(Ar(t,f.cachePool),Id(t,f),Xn(),t.memoizedState=null):(e!==null&&Ar(t,null),Qu(),Xn());return lt(e,t,s,n),t.child}function Wh(e,t,n,i){var s=zu();return s=s===null?null:{parent:Ke._currentValue,pool:s},t.memoizedState={baseLanes:n,cachePool:s},e!==null&&Ar(t,null),Qu(),zh(t),e!==null&&el(e,t,i,!0),null}function Gr(e,t){var n=t.ref;if(n===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(u(284));(e===null||e.ref!==n)&&(t.flags|=4194816)}}function co(e,t,n,i,s){return Ca(t),n=Xu(e,t,n,i,void 0,s),i=$u(),e!==null&&!et?(Ku(e,t,s),_n(e,t,s)):(Ee&&i&&Ou(t),t.flags|=1,lt(e,t,n,s),t.child)}function Jh(e,t,n,i,s,f){return Ca(t),t.updateQueue=null,n=Wd(t,i,n,s),Zd(e),i=$u(),e!==null&&!et?(Ku(e,t,f),_n(e,t,f)):(Ee&&i&&Ou(t),t.flags|=1,lt(e,t,n,f),t.child)}function Fh(e,t,n,i,s){if(Ca(t),t.stateNode===null){var f=Fa,g=n.contextType;typeof g=="object"&&g!==null&&(f=ct(g)),f=new n(i,f),t.memoizedState=f.state!==null&&f.state!==void 0?f.state:null,f.updater=uo,t.stateNode=f,f._reactInternals=t,f=t.stateNode,f.props=i,f.state=t.memoizedState,f.refs={},Bu(t),g=n.contextType,f.context=typeof g=="object"&&g!==null?ct(g):Fa,f.state=t.memoizedState,g=n.getDerivedStateFromProps,typeof g=="function"&&(so(t,n,g,i),f.state=t.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof f.getSnapshotBeforeUpdate=="function"||typeof f.UNSAFE_componentWillMount!="function"&&typeof f.componentWillMount!="function"||(g=f.state,typeof f.componentWillMount=="function"&&f.componentWillMount(),typeof f.UNSAFE_componentWillMount=="function"&&f.UNSAFE_componentWillMount(),g!==f.state&&uo.enqueueReplaceState(f,f.state,null),sl(t,i,f,s),rl(),f.state=t.memoizedState),typeof f.componentDidMount=="function"&&(t.flags|=4194308),i=!0}else if(e===null){f=t.stateNode;var v=t.memoizedProps,E=Ma(n,v);f.props=E;var k=f.context,z=n.contextType;g=Fa,typeof z=="object"&&z!==null&&(g=ct(z));var H=n.getDerivedStateFromProps;z=typeof H=="function"||typeof f.getSnapshotBeforeUpdate=="function",v=t.pendingProps!==v,z||typeof f.UNSAFE_componentWillReceiveProps!="function"&&typeof f.componentWillReceiveProps!="function"||(v||k!==g)&&Bh(t,f,i,g),Vn=!1;var U=t.memoizedState;f.state=U,sl(t,i,f,s),rl(),k=t.memoizedState,v||U!==k||Vn?(typeof H=="function"&&(so(t,n,H,i),k=t.memoizedState),(E=Vn||qh(t,n,E,i,U,k,g))?(z||typeof f.UNSAFE_componentWillMount!="function"&&typeof f.componentWillMount!="function"||(typeof f.componentWillMount=="function"&&f.componentWillMount(),typeof f.UNSAFE_componentWillMount=="function"&&f.UNSAFE_componentWillMount()),typeof f.componentDidMount=="function"&&(t.flags|=4194308)):(typeof f.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=i,t.memoizedState=k),f.props=i,f.state=k,f.context=g,i=E):(typeof f.componentDidMount=="function"&&(t.flags|=4194308),i=!1)}else{f=t.stateNode,Hu(e,t),g=t.memoizedProps,z=Ma(n,g),f.props=z,H=t.pendingProps,U=f.context,k=n.contextType,E=Fa,typeof k=="object"&&k!==null&&(E=ct(k)),v=n.getDerivedStateFromProps,(k=typeof v=="function"||typeof f.getSnapshotBeforeUpdate=="function")||typeof f.UNSAFE_componentWillReceiveProps!="function"&&typeof f.componentWillReceiveProps!="function"||(g!==H||U!==E)&&Bh(t,f,i,E),Vn=!1,U=t.memoizedState,f.state=U,sl(t,i,f,s),rl();var N=t.memoizedState;g!==H||U!==N||Vn||e!==null&&e.dependencies!==null&&Tr(e.dependencies)?(typeof v=="function"&&(so(t,n,v,i),N=t.memoizedState),(z=Vn||qh(t,n,z,i,U,N,E)||e!==null&&e.dependencies!==null&&Tr(e.dependencies))?(k||typeof f.UNSAFE_componentWillUpdate!="function"&&typeof f.componentWillUpdate!="function"||(typeof f.componentWillUpdate=="function"&&f.componentWillUpdate(i,N,E),typeof f.UNSAFE_componentWillUpdate=="function"&&f.UNSAFE_componentWillUpdate(i,N,E)),typeof f.componentDidUpdate=="function"&&(t.flags|=4),typeof f.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof f.componentDidUpdate!="function"||g===e.memoizedProps&&U===e.memoizedState||(t.flags|=4),typeof f.getSnapshotBeforeUpdate!="function"||g===e.memoizedProps&&U===e.memoizedState||(t.flags|=1024),t.memoizedProps=i,t.memoizedState=N),f.props=i,f.state=N,f.context=E,i=z):(typeof f.componentDidUpdate!="function"||g===e.memoizedProps&&U===e.memoizedState||(t.flags|=4),typeof f.getSnapshotBeforeUpdate!="function"||g===e.memoizedProps&&U===e.memoizedState||(t.flags|=1024),i=!1)}return f=i,Gr(e,t),i=(t.flags&128)!==0,f||i?(f=t.stateNode,n=i&&typeof n.getDerivedStateFromError!="function"?null:f.render(),t.flags|=1,e!==null&&i?(t.child=oi(t,e.child,null,s),t.child=oi(t,null,n,s)):lt(e,t,n,s),t.memoizedState=f.state,e=t.child):e=_n(e,t,s),e}function em(e,t,n,i){return Ji(),t.flags|=256,lt(e,t,n,i),t.child}var fo={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function ho(e){return{baseLanes:e,cachePool:Vd()}}function mo(e,t,n){return e=e!==null?e.childLanes&~n:0,t&&(e|=Pt),e}function tm(e,t,n){var i=t.pendingProps,s=!1,f=(t.flags&128)!==0,g;if((g=f)||(g=e!==null&&e.memoizedState===null?!1:(Ie.current&2)!==0),g&&(s=!0,t.flags&=-129),g=(t.flags&32)!==0,t.flags&=-33,e===null){if(Ee){if(s?Gn(t):Xn(),Ee){var v=He,E;if(E=v){e:{for(E=v,v=ln;E.nodeType!==8;){if(!v){v=null;break e}if(E=Zt(E.nextSibling),E===null){v=null;break e}}v=E}v!==null?(t.memoizedState={dehydrated:v,treeContext:xa!==null?{id:vn,overflow:yn}:null,retryLane:536870912,hydrationErrors:null},E=At(18,null,null,0),E.stateNode=v,E.return=t,t.child=E,gt=t,He=null,E=!0):E=!1}E||Ra(t)}if(v=t.memoizedState,v!==null&&(v=v.dehydrated,v!==null))return Wo(v)?t.lanes=32:t.lanes=536870912,null;xn(t)}return v=i.children,i=i.fallback,s?(Xn(),s=t.mode,v=Xr({mode:"hidden",children:v},s),i=Ea(i,s,n,null),v.return=t,i.return=t,v.sibling=i,t.child=v,s=t.child,s.memoizedState=ho(n),s.childLanes=mo(e,g,n),t.memoizedState=fo,i):(Gn(t),go(t,v))}if(E=e.memoizedState,E!==null&&(v=E.dehydrated,v!==null)){if(f)t.flags&256?(Gn(t),t.flags&=-257,t=po(e,t,n)):t.memoizedState!==null?(Xn(),t.child=e.child,t.flags|=128,t=null):(Xn(),s=i.fallback,v=t.mode,i=Xr({mode:"visible",children:i.children},v),s=Ea(s,v,n,null),s.flags|=2,i.return=t,s.return=t,i.sibling=s,t.child=i,oi(t,e.child,null,n),i=t.child,i.memoizedState=ho(n),i.childLanes=mo(e,g,n),t.memoizedState=fo,t=s);else if(Gn(t),Wo(v)){if(g=v.nextSibling&&v.nextSibling.dataset,g)var k=g.dgst;g=k,i=Error(u(419)),i.stack="",i.digest=g,Fi({value:i,source:null,stack:null}),t=po(e,t,n)}else if(et||el(e,t,n,!1),g=(n&e.childLanes)!==0,et||g){if(g=Ue,g!==null&&(i=n&-n,i=(i&42)!==0?1:Ws(i),i=(i&(g.suspendedLanes|n))!==0?0:i,i!==0&&i!==E.retryLane))throw E.retryLane=i,Ja(e,i),Ut(g,e,i),Xh;v.data==="$?"||No(),t=po(e,t,n)}else v.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=E.treeContext,He=Zt(v.nextSibling),gt=t,Ee=!0,wa=null,ln=!1,e!==null&&(Bt[Ht++]=vn,Bt[Ht++]=yn,Bt[Ht++]=xa,vn=e.id,yn=e.overflow,xa=t),t=go(t,i.children),t.flags|=4096);return t}return s?(Xn(),s=i.fallback,v=t.mode,E=e.child,k=E.sibling,i=pn(E,{mode:"hidden",children:i.children}),i.subtreeFlags=E.subtreeFlags&65011712,k!==null?s=pn(k,s):(s=Ea(s,v,n,null),s.flags|=2),s.return=t,i.return=t,i.sibling=s,t.child=i,i=s,s=t.child,v=e.child.memoizedState,v===null?v=ho(n):(E=v.cachePool,E!==null?(k=Ke._currentValue,E=E.parent!==k?{parent:k,pool:k}:E):E=Vd(),v={baseLanes:v.baseLanes|n,cachePool:E}),s.memoizedState=v,s.childLanes=mo(e,g,n),t.memoizedState=fo,i):(Gn(t),n=e.child,e=n.sibling,n=pn(n,{mode:"visible",children:i.children}),n.return=t,n.sibling=null,e!==null&&(g=t.deletions,g===null?(t.deletions=[e],t.flags|=16):g.push(e)),t.child=n,t.memoizedState=null,n)}function go(e,t){return t=Xr({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function Xr(e,t){return e=At(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function po(e,t,n){return oi(t,e.child,null,n),e=go(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function nm(e,t,n){e.lanes|=t;var i=e.alternate;i!==null&&(i.lanes|=t),Uu(e.return,t,n)}function vo(e,t,n,i,s){var f=e.memoizedState;f===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:i,tail:n,tailMode:s}:(f.isBackwards=t,f.rendering=null,f.renderingStartTime=0,f.last=i,f.tail=n,f.tailMode=s)}function am(e,t,n){var i=t.pendingProps,s=i.revealOrder,f=i.tail;if(lt(e,t,i.children,n),i=Ie.current,(i&2)!==0)i=i&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&nm(e,n,t);else if(e.tag===19)nm(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}i&=1}switch(K(Ie,i),s){case"forwards":for(n=t.child,s=null;n!==null;)e=n.alternate,e!==null&&Pr(e)===null&&(s=n),n=n.sibling;n=s,n===null?(s=t.child,t.child=null):(s=n.sibling,n.sibling=null),vo(t,!1,s,n,f);break;case"backwards":for(n=null,s=t.child,t.child=null;s!==null;){if(e=s.alternate,e!==null&&Pr(e)===null){t.child=s;break}e=s.sibling,s.sibling=n,n=s,s=e}vo(t,!0,n,null,f);break;case"together":vo(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function _n(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Wn|=t.lanes,(n&t.childLanes)===0)if(e!==null){if(el(e,t,n,!1),(n&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(u(153));if(t.child!==null){for(e=t.child,n=pn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=pn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function yo(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&Tr(e)))}function O0(e,t,n){switch(t.tag){case 3:ke(t,t.stateNode.containerInfo),Hn(t,Ke,e.memoizedState.cache),Ji();break;case 27:case 5:hn(t);break;case 4:ke(t,t.stateNode.containerInfo);break;case 10:Hn(t,t.type,t.memoizedProps.value);break;case 13:var i=t.memoizedState;if(i!==null)return i.dehydrated!==null?(Gn(t),t.flags|=128,null):(n&t.child.childLanes)!==0?tm(e,t,n):(Gn(t),e=_n(e,t,n),e!==null?e.sibling:null);Gn(t);break;case 19:var s=(e.flags&128)!==0;if(i=(n&t.childLanes)!==0,i||(el(e,t,n,!1),i=(n&t.childLanes)!==0),s){if(i)return am(e,t,n);t.flags|=128}if(s=t.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),K(Ie,Ie.current),i)break;return null;case 22:case 23:return t.lanes=0,Zh(e,t,n);case 24:Hn(t,Ke,e.memoizedState.cache)}return _n(e,t,n)}function im(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps)et=!0;else{if(!yo(e,n)&&(t.flags&128)===0)return et=!1,O0(e,t,n);et=(e.flags&131072)!==0}else et=!1,Ee&&(t.flags&1048576)!==0&&Nd(t,Rr,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var i=t.elementType,s=i._init;if(i=s(i._payload),t.type=i,typeof i=="function")Tu(i)?(e=Ma(i,e),t.tag=1,t=Fh(null,t,i,e,n)):(t.tag=0,t=co(null,t,i,e,n));else{if(i!=null){if(s=i.$$typeof,s===J){t.tag=11,t=$h(null,t,i,e,n);break e}else if(s===I){t.tag=14,t=Kh(null,t,i,e,n);break e}}throw t=Me(i)||i,Error(u(306,t,""))}}return t;case 0:return co(e,t,t.type,t.pendingProps,n);case 1:return i=t.type,s=Ma(i,t.pendingProps),Fh(e,t,i,s,n);case 3:e:{if(ke(t,t.stateNode.containerInfo),e===null)throw Error(u(387));i=t.pendingProps;var f=t.memoizedState;s=f.element,Hu(e,t),sl(t,i,null,n);var g=t.memoizedState;if(i=g.cache,Hn(t,Ke,i),i!==f.cache&&Nu(t,[Ke],n,!0),rl(),i=g.element,f.isDehydrated)if(f={element:i,isDehydrated:!1,cache:g.cache},t.updateQueue.baseState=f,t.memoizedState=f,t.flags&256){t=em(e,t,i,n);break e}else if(i!==s){s=zt(Error(u(424)),t),Fi(s),t=em(e,t,i,n);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(He=Zt(e.firstChild),gt=t,Ee=!0,wa=null,ln=!0,n=Lh(t,null,i,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(Ji(),i===s){t=_n(e,t,n);break e}lt(e,t,i,n)}t=t.child}return t;case 26:return Gr(e,t),e===null?(n=ug(t.type,null,t.pendingProps,null))?t.memoizedState=n:Ee||(n=t.type,e=t.pendingProps,i=rs(le.current).createElement(n),i[ot]=t,i[St]=e,st(i,n,e),Fe(i),t.stateNode=i):t.memoizedState=ug(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return hn(t),e===null&&Ee&&(i=t.stateNode=lg(t.type,t.pendingProps,le.current),gt=t,ln=!0,s=He,ta(t.type)?(Jo=s,He=Zt(i.firstChild)):He=s),lt(e,t,t.pendingProps.children,n),Gr(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&Ee&&((s=i=He)&&(i=a1(i,t.type,t.pendingProps,ln),i!==null?(t.stateNode=i,gt=t,He=Zt(i.firstChild),ln=!1,s=!0):s=!1),s||Ra(t)),hn(t),s=t.type,f=t.pendingProps,g=e!==null?e.memoizedProps:null,i=f.children,Ko(s,f)?i=null:g!==null&&Ko(s,g)&&(t.flags|=32),t.memoizedState!==null&&(s=Xu(e,t,E0,null,null,n),Ol._currentValue=s),Gr(e,t),lt(e,t,i,n),t.child;case 6:return e===null&&Ee&&((e=n=He)&&(n=i1(n,t.pendingProps,ln),n!==null?(t.stateNode=n,gt=t,He=null,e=!0):e=!1),e||Ra(t)),null;case 13:return tm(e,t,n);case 4:return ke(t,t.stateNode.containerInfo),i=t.pendingProps,e===null?t.child=oi(t,null,i,n):lt(e,t,i,n),t.child;case 11:return $h(e,t,t.type,t.pendingProps,n);case 7:return lt(e,t,t.pendingProps,n),t.child;case 8:return lt(e,t,t.pendingProps.children,n),t.child;case 12:return lt(e,t,t.pendingProps.children,n),t.child;case 10:return i=t.pendingProps,Hn(t,t.type,i.value),lt(e,t,i.children,n),t.child;case 9:return s=t.type._context,i=t.pendingProps.children,Ca(t),s=ct(s),i=i(s),t.flags|=1,lt(e,t,i,n),t.child;case 14:return Kh(e,t,t.type,t.pendingProps,n);case 15:return Ih(e,t,t.type,t.pendingProps,n);case 19:return am(e,t,n);case 31:return i=t.pendingProps,n=t.mode,i={mode:i.mode,children:i.children},e===null?(n=Xr(i,n),n.ref=t.ref,t.child=n,n.return=t,t=n):(n=pn(e.child,i),n.ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return Zh(e,t,n);case 24:return Ca(t),i=ct(Ke),e===null?(s=zu(),s===null&&(s=Ue,f=Du(),s.pooledCache=f,f.refCount++,f!==null&&(s.pooledCacheLanes|=n),s=f),t.memoizedState={parent:i,cache:s},Bu(t),Hn(t,Ke,s)):((e.lanes&n)!==0&&(Hu(e,t),sl(t,null,null,n),rl()),s=e.memoizedState,f=t.memoizedState,s.parent!==i?(s={parent:i,cache:i},t.memoizedState=s,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=s),Hn(t,Ke,i)):(i=f.cache,Hn(t,Ke,i),i!==s.cache&&Nu(t,[Ke],n,!0))),lt(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(u(156,t.tag))}function wn(e){e.flags|=4}function lm(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!hg(t)){if(t=Vt.current,t!==null&&((ye&4194048)===ye?rn!==null:(ye&62914560)!==ye&&(ye&536870912)===0||t!==rn))throw il=qu,Pd;e.flags|=8192}}function $r(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?zf():536870912,e.lanes|=t,hi|=t)}function ml(e,t){if(!Ee)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var i=null;n!==null;)n.alternate!==null&&(i=n),n=n.sibling;i===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:i.sibling=null}}function ze(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,i=0;if(t)for(var s=e.child;s!==null;)n|=s.lanes|s.childLanes,i|=s.subtreeFlags&65011712,i|=s.flags&65011712,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)n|=s.lanes|s.childLanes,i|=s.subtreeFlags,i|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=i,e.childLanes=n,t}function M0(e,t,n){var i=t.pendingProps;switch(Mu(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ze(t),null;case 1:return ze(t),null;case 3:return n=t.stateNode,i=null,e!==null&&(i=e.memoizedState.cache),t.memoizedState.cache!==i&&(t.flags|=2048),Sn(Ke),nn(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(Wi(t)?wn(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,zd())),ze(t),null;case 26:return n=t.memoizedState,e===null?(wn(t),n!==null?(ze(t),lm(t,n)):(ze(t),t.flags&=-16777217)):n?n!==e.memoizedState?(wn(t),ze(t),lm(t,n)):(ze(t),t.flags&=-16777217):(e.memoizedProps!==i&&wn(t),ze(t),t.flags&=-16777217),null;case 27:ga(t),n=le.current;var s=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==i&&wn(t);else{if(!i){if(t.stateNode===null)throw Error(u(166));return ze(t),null}e=ee.current,Wi(t)?Dd(t):(e=lg(s,i,n),t.stateNode=e,wn(t))}return ze(t),null;case 5:if(ga(t),n=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==i&&wn(t);else{if(!i){if(t.stateNode===null)throw Error(u(166));return ze(t),null}if(e=ee.current,Wi(t))Dd(t);else{switch(s=rs(le.current),e){case 1:e=s.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=s.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=s.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=s.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof i.is=="string"?s.createElement("select",{is:i.is}):s.createElement("select"),i.multiple?e.multiple=!0:i.size&&(e.size=i.size);break;default:e=typeof i.is=="string"?s.createElement(n,{is:i.is}):s.createElement(n)}}e[ot]=t,e[St]=i;e:for(s=t.child;s!==null;){if(s.tag===5||s.tag===6)e.appendChild(s.stateNode);else if(s.tag!==4&&s.tag!==27&&s.child!==null){s.child.return=s,s=s.child;continue}if(s===t)break e;for(;s.sibling===null;){if(s.return===null||s.return===t)break e;s=s.return}s.sibling.return=s.return,s=s.sibling}t.stateNode=e;e:switch(st(e,n,i),n){case"button":case"input":case"select":case"textarea":e=!!i.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&wn(t)}}return ze(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==i&&wn(t);else{if(typeof i!="string"&&t.stateNode===null)throw Error(u(166));if(e=le.current,Wi(t)){if(e=t.stateNode,n=t.memoizedProps,i=null,s=gt,s!==null)switch(s.tag){case 27:case 5:i=s.memoizedProps}e[ot]=t,e=!!(e.nodeValue===n||i!==null&&i.suppressHydrationWarning===!0||Jm(e.nodeValue,n)),e||Ra(t)}else e=rs(e).createTextNode(i),e[ot]=t,t.stateNode=e}return ze(t),null;case 13:if(i=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(s=Wi(t),i!==null&&i.dehydrated!==null){if(e===null){if(!s)throw Error(u(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(u(317));s[ot]=t}else Ji(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;ze(t),s=!1}else s=zd(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=s),s=!0;if(!s)return t.flags&256?(xn(t),t):(xn(t),null)}if(xn(t),(t.flags&128)!==0)return t.lanes=n,t;if(n=i!==null,e=e!==null&&e.memoizedState!==null,n){i=t.child,s=null,i.alternate!==null&&i.alternate.memoizedState!==null&&i.alternate.memoizedState.cachePool!==null&&(s=i.alternate.memoizedState.cachePool.pool);var f=null;i.memoizedState!==null&&i.memoizedState.cachePool!==null&&(f=i.memoizedState.cachePool.pool),f!==s&&(i.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),$r(t,t.updateQueue),ze(t),null;case 4:return nn(),e===null&&Qo(t.stateNode.containerInfo),ze(t),null;case 10:return Sn(t.type),ze(t),null;case 19:if(X(Ie),s=t.memoizedState,s===null)return ze(t),null;if(i=(t.flags&128)!==0,f=s.rendering,f===null)if(i)ml(s,!1);else{if(Ve!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(f=Pr(e),f!==null){for(t.flags|=128,ml(s,!1),e=f.updateQueue,t.updateQueue=e,$r(t,e),t.subtreeFlags=0,e=n,n=t.child;n!==null;)Ud(n,e),n=n.sibling;return K(Ie,Ie.current&1|2),t.child}e=e.sibling}s.tail!==null&&Be()>Zr&&(t.flags|=128,i=!0,ml(s,!1),t.lanes=4194304)}else{if(!i)if(e=Pr(f),e!==null){if(t.flags|=128,i=!0,e=e.updateQueue,t.updateQueue=e,$r(t,e),ml(s,!0),s.tail===null&&s.tailMode==="hidden"&&!f.alternate&&!Ee)return ze(t),null}else 2*Be()-s.renderingStartTime>Zr&&n!==536870912&&(t.flags|=128,i=!0,ml(s,!1),t.lanes=4194304);s.isBackwards?(f.sibling=t.child,t.child=f):(e=s.last,e!==null?e.sibling=f:t.child=f,s.last=f)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=Be(),t.sibling=null,e=Ie.current,K(Ie,i?e&1|2:e&1),t):(ze(t),null);case 22:case 23:return xn(t),Yu(),i=t.memoizedState!==null,e!==null?e.memoizedState!==null!==i&&(t.flags|=8192):i&&(t.flags|=8192),i?(n&536870912)!==0&&(t.flags&128)===0&&(ze(t),t.subtreeFlags&6&&(t.flags|=8192)):ze(t),n=t.updateQueue,n!==null&&$r(t,n.retryQueue),n=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),i=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(i=t.memoizedState.cachePool.pool),i!==n&&(t.flags|=2048),e!==null&&X(Aa),null;case 24:return n=null,e!==null&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),Sn(Ke),ze(t),null;case 25:return null;case 30:return null}throw Error(u(156,t.tag))}function j0(e,t){switch(Mu(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Sn(Ke),nn(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return ga(t),null;case 13:if(xn(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(u(340));Ji()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return X(Ie),null;case 4:return nn(),null;case 10:return Sn(t.type),null;case 22:case 23:return xn(t),Yu(),e!==null&&X(Aa),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return Sn(Ke),null;case 25:return null;default:return null}}function rm(e,t){switch(Mu(t),t.tag){case 3:Sn(Ke),nn();break;case 26:case 27:case 5:ga(t);break;case 4:nn();break;case 13:xn(t);break;case 19:X(Ie);break;case 10:Sn(t.type);break;case 22:case 23:xn(t),Yu(),e!==null&&X(Aa);break;case 24:Sn(Ke)}}function gl(e,t){try{var n=t.updateQueue,i=n!==null?n.lastEffect:null;if(i!==null){var s=i.next;n=s;do{if((n.tag&e)===e){i=void 0;var f=n.create,g=n.inst;i=f(),g.destroy=i}n=n.next}while(n!==s)}}catch(v){je(t,t.return,v)}}function $n(e,t,n){try{var i=t.updateQueue,s=i!==null?i.lastEffect:null;if(s!==null){var f=s.next;i=f;do{if((i.tag&e)===e){var g=i.inst,v=g.destroy;if(v!==void 0){g.destroy=void 0,s=t;var E=n,k=v;try{k()}catch(z){je(s,E,z)}}}i=i.next}while(i!==f)}}catch(z){je(t,t.return,z)}}function sm(e){var t=e.updateQueue;if(t!==null){var n=e.stateNode;try{Kd(t,n)}catch(i){je(e,e.return,i)}}}function um(e,t,n){n.props=Ma(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(i){je(e,t,i)}}function pl(e,t){try{var n=e.ref;if(n!==null){switch(e.tag){case 26:case 27:case 5:var i=e.stateNode;break;case 30:i=e.stateNode;break;default:i=e.stateNode}typeof n=="function"?e.refCleanup=n(i):n.current=i}}catch(s){je(e,t,s)}}function sn(e,t){var n=e.ref,i=e.refCleanup;if(n!==null)if(typeof i=="function")try{i()}catch(s){je(e,t,s)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(s){je(e,t,s)}else n.current=null}function om(e){var t=e.type,n=e.memoizedProps,i=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&i.focus();break e;case"img":n.src?i.src=n.src:n.srcSet&&(i.srcset=n.srcSet)}}catch(s){je(e,e.return,s)}}function bo(e,t,n){try{var i=e.stateNode;J0(i,e.type,n,t),i[St]=t}catch(s){je(e,e.return,s)}}function cm(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&ta(e.type)||e.tag===4}function So(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||cm(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&ta(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Eo(e,t,n){var i=e.tag;if(i===5||i===6)e=e.stateNode,t?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(e,t):(t=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,t.appendChild(e),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=ls));else if(i!==4&&(i===27&&ta(e.type)&&(n=e.stateNode,t=null),e=e.child,e!==null))for(Eo(e,t,n),e=e.sibling;e!==null;)Eo(e,t,n),e=e.sibling}function Kr(e,t,n){var i=e.tag;if(i===5||i===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(i!==4&&(i===27&&ta(e.type)&&(n=e.stateNode),e=e.child,e!==null))for(Kr(e,t,n),e=e.sibling;e!==null;)Kr(e,t,n),e=e.sibling}function fm(e){var t=e.stateNode,n=e.memoizedProps;try{for(var i=e.type,s=t.attributes;s.length;)t.removeAttributeNode(s[0]);st(t,i,n),t[ot]=e,t[St]=n}catch(f){je(e,e.return,f)}}var Rn=!1,Ye=!1,xo=!1,dm=typeof WeakSet=="function"?WeakSet:Set,tt=null;function k0(e,t){if(e=e.containerInfo,Xo=ds,e=_d(e),bu(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var i=n.getSelection&&n.getSelection();if(i&&i.rangeCount!==0){n=i.anchorNode;var s=i.anchorOffset,f=i.focusNode;i=i.focusOffset;try{n.nodeType,f.nodeType}catch{n=null;break e}var g=0,v=-1,E=-1,k=0,z=0,H=e,U=null;t:for(;;){for(var N;H!==n||s!==0&&H.nodeType!==3||(v=g+s),H!==f||i!==0&&H.nodeType!==3||(E=g+i),H.nodeType===3&&(g+=H.nodeValue.length),(N=H.firstChild)!==null;)U=H,H=N;for(;;){if(H===e)break t;if(U===n&&++k===s&&(v=g),U===f&&++z===i&&(E=g),(N=H.nextSibling)!==null)break;H=U,U=H.parentNode}H=N}n=v===-1||E===-1?null:{start:v,end:E}}else n=null}n=n||{start:0,end:0}}else n=null;for($o={focusedElem:e,selectionRange:n},ds=!1,tt=t;tt!==null;)if(t=tt,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,tt=e;else for(;tt!==null;){switch(t=tt,f=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&f!==null){e=void 0,n=t,s=f.memoizedProps,f=f.memoizedState,i=n.stateNode;try{var se=Ma(n.type,s,n.elementType===n.type);e=i.getSnapshotBeforeUpdate(se,f),i.__reactInternalSnapshotBeforeUpdate=e}catch(ae){je(n,n.return,ae)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,n=e.nodeType,n===9)Zo(e);else if(n===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Zo(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(u(163))}if(e=t.sibling,e!==null){e.return=t.return,tt=e;break}tt=t.return}}function hm(e,t,n){var i=n.flags;switch(n.tag){case 0:case 11:case 15:Kn(e,n),i&4&&gl(5,n);break;case 1:if(Kn(e,n),i&4)if(e=n.stateNode,t===null)try{e.componentDidMount()}catch(g){je(n,n.return,g)}else{var s=Ma(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(s,t,e.__reactInternalSnapshotBeforeUpdate)}catch(g){je(n,n.return,g)}}i&64&&sm(n),i&512&&pl(n,n.return);break;case 3:if(Kn(e,n),i&64&&(e=n.updateQueue,e!==null)){if(t=null,n.child!==null)switch(n.child.tag){case 27:case 5:t=n.child.stateNode;break;case 1:t=n.child.stateNode}try{Kd(e,t)}catch(g){je(n,n.return,g)}}break;case 27:t===null&&i&4&&fm(n);case 26:case 5:Kn(e,n),t===null&&i&4&&om(n),i&512&&pl(n,n.return);break;case 12:Kn(e,n);break;case 13:Kn(e,n),i&4&&pm(e,n),i&64&&(e=n.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(n=V0.bind(null,n),l1(e,n))));break;case 22:if(i=n.memoizedState!==null||Rn,!i){t=t!==null&&t.memoizedState!==null||Ye,s=Rn;var f=Ye;Rn=i,(Ye=t)&&!f?In(e,n,(n.subtreeFlags&8772)!==0):Kn(e,n),Rn=s,Ye=f}break;case 30:break;default:Kn(e,n)}}function mm(e){var t=e.alternate;t!==null&&(e.alternate=null,mm(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&eu(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Ne=null,_t=!1;function Tn(e,t,n){for(n=n.child;n!==null;)gm(e,t,n),n=n.sibling}function gm(e,t,n){if(at&&typeof at.onCommitFiberUnmount=="function")try{at.onCommitFiberUnmount(mt,n)}catch{}switch(n.tag){case 26:Ye||sn(n,t),Tn(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:Ye||sn(n,t);var i=Ne,s=_t;ta(n.type)&&(Ne=n.stateNode,_t=!1),Tn(e,t,n),Rl(n.stateNode),Ne=i,_t=s;break;case 5:Ye||sn(n,t);case 6:if(i=Ne,s=_t,Ne=null,Tn(e,t,n),Ne=i,_t=s,Ne!==null)if(_t)try{(Ne.nodeType===9?Ne.body:Ne.nodeName==="HTML"?Ne.ownerDocument.body:Ne).removeChild(n.stateNode)}catch(f){je(n,t,f)}else try{Ne.removeChild(n.stateNode)}catch(f){je(n,t,f)}break;case 18:Ne!==null&&(_t?(e=Ne,ag(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,n.stateNode),Ul(e)):ag(Ne,n.stateNode));break;case 4:i=Ne,s=_t,Ne=n.stateNode.containerInfo,_t=!0,Tn(e,t,n),Ne=i,_t=s;break;case 0:case 11:case 14:case 15:Ye||$n(2,n,t),Ye||$n(4,n,t),Tn(e,t,n);break;case 1:Ye||(sn(n,t),i=n.stateNode,typeof i.componentWillUnmount=="function"&&um(n,t,i)),Tn(e,t,n);break;case 21:Tn(e,t,n);break;case 22:Ye=(i=Ye)||n.memoizedState!==null,Tn(e,t,n),Ye=i;break;default:Tn(e,t,n)}}function pm(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{Ul(e)}catch(n){je(t,t.return,n)}}function U0(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new dm),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new dm),t;default:throw Error(u(435,e.tag))}}function _o(e,t){var n=U0(e);t.forEach(function(i){var s=P0.bind(null,e,i);n.has(i)||(n.add(i),i.then(s,s))})}function Ot(e,t){var n=t.deletions;if(n!==null)for(var i=0;i<n.length;i++){var s=n[i],f=e,g=t,v=g;e:for(;v!==null;){switch(v.tag){case 27:if(ta(v.type)){Ne=v.stateNode,_t=!1;break e}break;case 5:Ne=v.stateNode,_t=!1;break e;case 3:case 4:Ne=v.stateNode.containerInfo,_t=!0;break e}v=v.return}if(Ne===null)throw Error(u(160));gm(f,g,s),Ne=null,_t=!1,f=s.alternate,f!==null&&(f.return=null),s.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)vm(t,e),t=t.sibling}var It=null;function vm(e,t){var n=e.alternate,i=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Ot(t,e),Mt(e),i&4&&($n(3,e,e.return),gl(3,e),$n(5,e,e.return));break;case 1:Ot(t,e),Mt(e),i&512&&(Ye||n===null||sn(n,n.return)),i&64&&Rn&&(e=e.updateQueue,e!==null&&(i=e.callbacks,i!==null&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=n===null?i:n.concat(i))));break;case 26:var s=It;if(Ot(t,e),Mt(e),i&512&&(Ye||n===null||sn(n,n.return)),i&4){var f=n!==null?n.memoizedState:null;if(i=e.memoizedState,n===null)if(i===null)if(e.stateNode===null){e:{i=e.type,n=e.memoizedProps,s=s.ownerDocument||s;t:switch(i){case"title":f=s.getElementsByTagName("title")[0],(!f||f[Hi]||f[ot]||f.namespaceURI==="http://www.w3.org/2000/svg"||f.hasAttribute("itemprop"))&&(f=s.createElement(i),s.head.insertBefore(f,s.querySelector("head > title"))),st(f,i,n),f[ot]=e,Fe(f),i=f;break e;case"link":var g=fg("link","href",s).get(i+(n.href||""));if(g){for(var v=0;v<g.length;v++)if(f=g[v],f.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&f.getAttribute("rel")===(n.rel==null?null:n.rel)&&f.getAttribute("title")===(n.title==null?null:n.title)&&f.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){g.splice(v,1);break t}}f=s.createElement(i),st(f,i,n),s.head.appendChild(f);break;case"meta":if(g=fg("meta","content",s).get(i+(n.content||""))){for(v=0;v<g.length;v++)if(f=g[v],f.getAttribute("content")===(n.content==null?null:""+n.content)&&f.getAttribute("name")===(n.name==null?null:n.name)&&f.getAttribute("property")===(n.property==null?null:n.property)&&f.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&f.getAttribute("charset")===(n.charSet==null?null:n.charSet)){g.splice(v,1);break t}}f=s.createElement(i),st(f,i,n),s.head.appendChild(f);break;default:throw Error(u(468,i))}f[ot]=e,Fe(f),i=f}e.stateNode=i}else dg(s,e.type,e.stateNode);else e.stateNode=cg(s,i,e.memoizedProps);else f!==i?(f===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):f.count--,i===null?dg(s,e.type,e.stateNode):cg(s,i,e.memoizedProps)):i===null&&e.stateNode!==null&&bo(e,e.memoizedProps,n.memoizedProps)}break;case 27:Ot(t,e),Mt(e),i&512&&(Ye||n===null||sn(n,n.return)),n!==null&&i&4&&bo(e,e.memoizedProps,n.memoizedProps);break;case 5:if(Ot(t,e),Mt(e),i&512&&(Ye||n===null||sn(n,n.return)),e.flags&32){s=e.stateNode;try{Ga(s,"")}catch(N){je(e,e.return,N)}}i&4&&e.stateNode!=null&&(s=e.memoizedProps,bo(e,s,n!==null?n.memoizedProps:s)),i&1024&&(xo=!0);break;case 6:if(Ot(t,e),Mt(e),i&4){if(e.stateNode===null)throw Error(u(162));i=e.memoizedProps,n=e.stateNode;try{n.nodeValue=i}catch(N){je(e,e.return,N)}}break;case 3:if(os=null,s=It,It=ss(t.containerInfo),Ot(t,e),It=s,Mt(e),i&4&&n!==null&&n.memoizedState.isDehydrated)try{Ul(t.containerInfo)}catch(N){je(e,e.return,N)}xo&&(xo=!1,ym(e));break;case 4:i=It,It=ss(e.stateNode.containerInfo),Ot(t,e),Mt(e),It=i;break;case 12:Ot(t,e),Mt(e);break;case 13:Ot(t,e),Mt(e),e.child.flags&8192&&e.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(Oo=Be()),i&4&&(i=e.updateQueue,i!==null&&(e.updateQueue=null,_o(e,i)));break;case 22:s=e.memoizedState!==null;var E=n!==null&&n.memoizedState!==null,k=Rn,z=Ye;if(Rn=k||s,Ye=z||E,Ot(t,e),Ye=z,Rn=k,Mt(e),i&8192)e:for(t=e.stateNode,t._visibility=s?t._visibility&-2:t._visibility|1,s&&(n===null||E||Rn||Ye||ja(e)),n=null,t=e;;){if(t.tag===5||t.tag===26){if(n===null){E=n=t;try{if(f=E.stateNode,s)g=f.style,typeof g.setProperty=="function"?g.setProperty("display","none","important"):g.display="none";else{v=E.stateNode;var H=E.memoizedProps.style,U=H!=null&&H.hasOwnProperty("display")?H.display:null;v.style.display=U==null||typeof U=="boolean"?"":(""+U).trim()}}catch(N){je(E,E.return,N)}}}else if(t.tag===6){if(n===null){E=t;try{E.stateNode.nodeValue=s?"":E.memoizedProps}catch(N){je(E,E.return,N)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}i&4&&(i=e.updateQueue,i!==null&&(n=i.retryQueue,n!==null&&(i.retryQueue=null,_o(e,n))));break;case 19:Ot(t,e),Mt(e),i&4&&(i=e.updateQueue,i!==null&&(e.updateQueue=null,_o(e,i)));break;case 30:break;case 21:break;default:Ot(t,e),Mt(e)}}function Mt(e){var t=e.flags;if(t&2){try{for(var n,i=e.return;i!==null;){if(cm(i)){n=i;break}i=i.return}if(n==null)throw Error(u(160));switch(n.tag){case 27:var s=n.stateNode,f=So(e);Kr(e,f,s);break;case 5:var g=n.stateNode;n.flags&32&&(Ga(g,""),n.flags&=-33);var v=So(e);Kr(e,v,g);break;case 3:case 4:var E=n.stateNode.containerInfo,k=So(e);Eo(e,k,E);break;default:throw Error(u(161))}}catch(z){je(e,e.return,z)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function ym(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;ym(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function Kn(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)hm(e,t.alternate,t),t=t.sibling}function ja(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:$n(4,t,t.return),ja(t);break;case 1:sn(t,t.return);var n=t.stateNode;typeof n.componentWillUnmount=="function"&&um(t,t.return,n),ja(t);break;case 27:Rl(t.stateNode);case 26:case 5:sn(t,t.return),ja(t);break;case 22:t.memoizedState===null&&ja(t);break;case 30:ja(t);break;default:ja(t)}e=e.sibling}}function In(e,t,n){for(n=n&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var i=t.alternate,s=e,f=t,g=f.flags;switch(f.tag){case 0:case 11:case 15:In(s,f,n),gl(4,f);break;case 1:if(In(s,f,n),i=f,s=i.stateNode,typeof s.componentDidMount=="function")try{s.componentDidMount()}catch(k){je(i,i.return,k)}if(i=f,s=i.updateQueue,s!==null){var v=i.stateNode;try{var E=s.shared.hiddenCallbacks;if(E!==null)for(s.shared.hiddenCallbacks=null,s=0;s<E.length;s++)$d(E[s],v)}catch(k){je(i,i.return,k)}}n&&g&64&&sm(f),pl(f,f.return);break;case 27:fm(f);case 26:case 5:In(s,f,n),n&&i===null&&g&4&&om(f),pl(f,f.return);break;case 12:In(s,f,n);break;case 13:In(s,f,n),n&&g&4&&pm(s,f);break;case 22:f.memoizedState===null&&In(s,f,n),pl(f,f.return);break;case 30:break;default:In(s,f,n)}t=t.sibling}}function wo(e,t){var n=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==n&&(e!=null&&e.refCount++,n!=null&&tl(n))}function Ro(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&tl(e))}function un(e,t,n,i){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)bm(e,t,n,i),t=t.sibling}function bm(e,t,n,i){var s=t.flags;switch(t.tag){case 0:case 11:case 15:un(e,t,n,i),s&2048&&gl(9,t);break;case 1:un(e,t,n,i);break;case 3:un(e,t,n,i),s&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&tl(e)));break;case 12:if(s&2048){un(e,t,n,i),e=t.stateNode;try{var f=t.memoizedProps,g=f.id,v=f.onPostCommit;typeof v=="function"&&v(g,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(E){je(t,t.return,E)}}else un(e,t,n,i);break;case 13:un(e,t,n,i);break;case 23:break;case 22:f=t.stateNode,g=t.alternate,t.memoizedState!==null?f._visibility&2?un(e,t,n,i):vl(e,t):f._visibility&2?un(e,t,n,i):(f._visibility|=2,ci(e,t,n,i,(t.subtreeFlags&10256)!==0)),s&2048&&wo(g,t);break;case 24:un(e,t,n,i),s&2048&&Ro(t.alternate,t);break;default:un(e,t,n,i)}}function ci(e,t,n,i,s){for(s=s&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var f=e,g=t,v=n,E=i,k=g.flags;switch(g.tag){case 0:case 11:case 15:ci(f,g,v,E,s),gl(8,g);break;case 23:break;case 22:var z=g.stateNode;g.memoizedState!==null?z._visibility&2?ci(f,g,v,E,s):vl(f,g):(z._visibility|=2,ci(f,g,v,E,s)),s&&k&2048&&wo(g.alternate,g);break;case 24:ci(f,g,v,E,s),s&&k&2048&&Ro(g.alternate,g);break;default:ci(f,g,v,E,s)}t=t.sibling}}function vl(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var n=e,i=t,s=i.flags;switch(i.tag){case 22:vl(n,i),s&2048&&wo(i.alternate,i);break;case 24:vl(n,i),s&2048&&Ro(i.alternate,i);break;default:vl(n,i)}t=t.sibling}}var yl=8192;function fi(e){if(e.subtreeFlags&yl)for(e=e.child;e!==null;)Sm(e),e=e.sibling}function Sm(e){switch(e.tag){case 26:fi(e),e.flags&yl&&e.memoizedState!==null&&y1(It,e.memoizedState,e.memoizedProps);break;case 5:fi(e);break;case 3:case 4:var t=It;It=ss(e.stateNode.containerInfo),fi(e),It=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=yl,yl=16777216,fi(e),yl=t):fi(e));break;default:fi(e)}}function Em(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function bl(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var i=t[n];tt=i,_m(i,e)}Em(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)xm(e),e=e.sibling}function xm(e){switch(e.tag){case 0:case 11:case 15:bl(e),e.flags&2048&&$n(9,e,e.return);break;case 3:bl(e);break;case 12:bl(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,Ir(e)):bl(e);break;default:bl(e)}}function Ir(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var i=t[n];tt=i,_m(i,e)}Em(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:$n(8,t,t.return),Ir(t);break;case 22:n=t.stateNode,n._visibility&2&&(n._visibility&=-3,Ir(t));break;default:Ir(t)}e=e.sibling}}function _m(e,t){for(;tt!==null;){var n=tt;switch(n.tag){case 0:case 11:case 15:$n(8,n,t);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var i=n.memoizedState.cachePool.pool;i!=null&&i.refCount++}break;case 24:tl(n.memoizedState.cache)}if(i=n.child,i!==null)i.return=n,tt=i;else e:for(n=e;tt!==null;){i=tt;var s=i.sibling,f=i.return;if(mm(i),i===n){tt=null;break e}if(s!==null){s.return=f,tt=s;break e}tt=f}}}var N0={getCacheForType:function(e){var t=ct(Ke),n=t.data.get(e);return n===void 0&&(n=e(),t.data.set(e,n)),n}},D0=typeof WeakMap=="function"?WeakMap:Map,we=0,Ue=null,he=null,ye=0,Re=0,jt=null,Zn=!1,di=!1,To=!1,Cn=0,Ve=0,Wn=0,ka=0,Co=0,Pt=0,hi=0,Sl=null,wt=null,Ao=!1,Oo=0,Zr=1/0,Wr=null,Jn=null,rt=0,Fn=null,mi=null,gi=0,Mo=0,jo=null,wm=null,El=0,ko=null;function kt(){if((we&2)!==0&&ye!==0)return ye&-ye;if(D.T!==null){var e=ni;return e!==0?e:Bo()}return Hf()}function Rm(){Pt===0&&(Pt=(ye&536870912)===0||Ee?Lf():536870912);var e=Vt.current;return e!==null&&(e.flags|=32),Pt}function Ut(e,t,n){(e===Ue&&(Re===2||Re===9)||e.cancelPendingCommit!==null)&&(pi(e,0),ea(e,ye,Pt,!1)),Bi(e,n),((we&2)===0||e!==Ue)&&(e===Ue&&((we&2)===0&&(ka|=n),Ve===4&&ea(e,ye,Pt,!1)),on(e))}function Tm(e,t,n){if((we&6)!==0)throw Error(u(327));var i=!n&&(t&124)===0&&(t&e.expiredLanes)===0||qi(e,t),s=i?q0(e,t):Do(e,t,!0),f=i;do{if(s===0){di&&!i&&ea(e,t,0,!1);break}else{if(n=e.current.alternate,f&&!L0(n)){s=Do(e,t,!1),f=!1;continue}if(s===2){if(f=t,e.errorRecoveryDisabledLanes&f)var g=0;else g=e.pendingLanes&-536870913,g=g!==0?g:g&536870912?536870912:0;if(g!==0){t=g;e:{var v=e;s=Sl;var E=v.current.memoizedState.isDehydrated;if(E&&(pi(v,g).flags|=256),g=Do(v,g,!1),g!==2){if(To&&!E){v.errorRecoveryDisabledLanes|=f,ka|=f,s=4;break e}f=wt,wt=s,f!==null&&(wt===null?wt=f:wt.push.apply(wt,f))}s=g}if(f=!1,s!==2)continue}}if(s===1){pi(e,0),ea(e,t,0,!0);break}e:{switch(i=e,f=s,f){case 0:case 1:throw Error(u(345));case 4:if((t&4194048)!==t)break;case 6:ea(i,t,Pt,!Zn);break e;case 2:wt=null;break;case 3:case 5:break;default:throw Error(u(329))}if((t&62914560)===t&&(s=Oo+300-Be(),10<s)){if(ea(i,t,Pt,!Zn),ur(i,0,!0)!==0)break e;i.timeoutHandle=tg(Cm.bind(null,i,n,wt,Wr,Ao,t,Pt,ka,hi,Zn,f,2,-0,0),s);break e}Cm(i,n,wt,Wr,Ao,t,Pt,ka,hi,Zn,f,0,-0,0)}}break}while(!0);on(e)}function Cm(e,t,n,i,s,f,g,v,E,k,z,H,U,N){if(e.timeoutHandle=-1,H=t.subtreeFlags,(H&8192||(H&16785408)===16785408)&&(Al={stylesheets:null,count:0,unsuspend:v1},Sm(t),H=b1(),H!==null)){e.cancelPendingCommit=H(Nm.bind(null,e,t,f,n,i,s,g,v,E,z,1,U,N)),ea(e,f,g,!k);return}Nm(e,t,f,n,i,s,g,v,E)}function L0(e){for(var t=e;;){var n=t.tag;if((n===0||n===11||n===15)&&t.flags&16384&&(n=t.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var i=0;i<n.length;i++){var s=n[i],f=s.getSnapshot;s=s.value;try{if(!Ct(f(),s))return!1}catch{return!1}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function ea(e,t,n,i){t&=~Co,t&=~ka,e.suspendedLanes|=t,e.pingedLanes&=~t,i&&(e.warmLanes|=t),i=e.expirationTimes;for(var s=t;0<s;){var f=31-it(s),g=1<<f;i[f]=-1,s&=~g}n!==0&&qf(e,n,t)}function Jr(){return(we&6)===0?(xl(0),!1):!0}function Uo(){if(he!==null){if(Re===0)var e=he.return;else e=he,bn=Ta=null,Iu(e),ui=null,dl=0,e=he;for(;e!==null;)rm(e.alternate,e),e=e.return;he=null}}function pi(e,t){var n=e.timeoutHandle;n!==-1&&(e.timeoutHandle=-1,e1(n)),n=e.cancelPendingCommit,n!==null&&(e.cancelPendingCommit=null,n()),Uo(),Ue=e,he=n=pn(e.current,null),ye=t,Re=0,jt=null,Zn=!1,di=qi(e,t),To=!1,hi=Pt=Co=ka=Wn=Ve=0,wt=Sl=null,Ao=!1,(t&8)!==0&&(t|=t&32);var i=e.entangledLanes;if(i!==0)for(e=e.entanglements,i&=t;0<i;){var s=31-it(i),f=1<<s;t|=e[s],i&=~f}return Cn=t,Sr(),n}function Am(e,t){ce=null,D.H=Br,t===al||t===Or?(t=Gd(),Re=3):t===Pd?(t=Gd(),Re=4):Re=t===Xh?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,jt=t,he===null&&(Ve=1,Yr(e,zt(t,e.current)))}function Om(){var e=D.H;return D.H=Br,e===null?Br:e}function Mm(){var e=D.A;return D.A=N0,e}function No(){Ve=4,Zn||(ye&4194048)!==ye&&Vt.current!==null||(di=!0),(Wn&134217727)===0&&(ka&134217727)===0||Ue===null||ea(Ue,ye,Pt,!1)}function Do(e,t,n){var i=we;we|=2;var s=Om(),f=Mm();(Ue!==e||ye!==t)&&(Wr=null,pi(e,t)),t=!1;var g=Ve;e:do try{if(Re!==0&&he!==null){var v=he,E=jt;switch(Re){case 8:Uo(),g=6;break e;case 3:case 2:case 9:case 6:Vt.current===null&&(t=!0);var k=Re;if(Re=0,jt=null,vi(e,v,E,k),n&&di){g=0;break e}break;default:k=Re,Re=0,jt=null,vi(e,v,E,k)}}z0(),g=Ve;break}catch(z){Am(e,z)}while(!0);return t&&e.shellSuspendCounter++,bn=Ta=null,we=i,D.H=s,D.A=f,he===null&&(Ue=null,ye=0,Sr()),g}function z0(){for(;he!==null;)jm(he)}function q0(e,t){var n=we;we|=2;var i=Om(),s=Mm();Ue!==e||ye!==t?(Wr=null,Zr=Be()+500,pi(e,t)):di=qi(e,t);e:do try{if(Re!==0&&he!==null){t=he;var f=jt;t:switch(Re){case 1:Re=0,jt=null,vi(e,t,f,1);break;case 2:case 9:if(Qd(f)){Re=0,jt=null,km(t);break}t=function(){Re!==2&&Re!==9||Ue!==e||(Re=7),on(e)},f.then(t,t);break e;case 3:Re=7;break e;case 4:Re=5;break e;case 7:Qd(f)?(Re=0,jt=null,km(t)):(Re=0,jt=null,vi(e,t,f,7));break;case 5:var g=null;switch(he.tag){case 26:g=he.memoizedState;case 5:case 27:var v=he;if(!g||hg(g)){Re=0,jt=null;var E=v.sibling;if(E!==null)he=E;else{var k=v.return;k!==null?(he=k,Fr(k)):he=null}break t}}Re=0,jt=null,vi(e,t,f,5);break;case 6:Re=0,jt=null,vi(e,t,f,6);break;case 8:Uo(),Ve=6;break e;default:throw Error(u(462))}}B0();break}catch(z){Am(e,z)}while(!0);return bn=Ta=null,D.H=i,D.A=s,we=n,he!==null?0:(Ue=null,ye=0,Sr(),Ve)}function B0(){for(;he!==null&&!We();)jm(he)}function jm(e){var t=im(e.alternate,e,Cn);e.memoizedProps=e.pendingProps,t===null?Fr(e):he=t}function km(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Jh(n,t,t.pendingProps,t.type,void 0,ye);break;case 11:t=Jh(n,t,t.pendingProps,t.type.render,t.ref,ye);break;case 5:Iu(t);default:rm(n,t),t=he=Ud(t,Cn),t=im(n,t,Cn)}e.memoizedProps=e.pendingProps,t===null?Fr(e):he=t}function vi(e,t,n,i){bn=Ta=null,Iu(t),ui=null,dl=0;var s=t.return;try{if(A0(e,s,t,n,ye)){Ve=1,Yr(e,zt(n,e.current)),he=null;return}}catch(f){if(s!==null)throw he=s,f;Ve=1,Yr(e,zt(n,e.current)),he=null;return}t.flags&32768?(Ee||i===1?e=!0:di||(ye&536870912)!==0?e=!1:(Zn=e=!0,(i===2||i===9||i===3||i===6)&&(i=Vt.current,i!==null&&i.tag===13&&(i.flags|=16384))),Um(t,e)):Fr(t)}function Fr(e){var t=e;do{if((t.flags&32768)!==0){Um(t,Zn);return}e=t.return;var n=M0(t.alternate,t,Cn);if(n!==null){he=n;return}if(t=t.sibling,t!==null){he=t;return}he=t=e}while(t!==null);Ve===0&&(Ve=5)}function Um(e,t){do{var n=j0(e.alternate,e);if(n!==null){n.flags&=32767,he=n;return}if(n=e.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&(e=e.sibling,e!==null)){he=e;return}he=e=n}while(e!==null);Ve=6,he=null}function Nm(e,t,n,i,s,f,g,v,E){e.cancelPendingCommit=null;do es();while(rt!==0);if((we&6)!==0)throw Error(u(327));if(t!==null){if(t===e.current)throw Error(u(177));if(f=t.lanes|t.childLanes,f|=wu,vy(e,n,f,g,v,E),e===Ue&&(he=Ue=null,ye=0),mi=t,Fn=e,gi=n,Mo=f,jo=s,wm=i,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,Q0(yt,function(){return Bm(),null})):(e.callbackNode=null,e.callbackPriority=0),i=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||i){i=D.T,D.T=null,s=Y.p,Y.p=2,g=we,we|=4;try{k0(e,t,n)}finally{we=g,Y.p=s,D.T=i}}rt=1,Dm(),Lm(),zm()}}function Dm(){if(rt===1){rt=0;var e=Fn,t=mi,n=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||n){n=D.T,D.T=null;var i=Y.p;Y.p=2;var s=we;we|=4;try{vm(t,e);var f=$o,g=_d(e.containerInfo),v=f.focusedElem,E=f.selectionRange;if(g!==v&&v&&v.ownerDocument&&xd(v.ownerDocument.documentElement,v)){if(E!==null&&bu(v)){var k=E.start,z=E.end;if(z===void 0&&(z=k),"selectionStart"in v)v.selectionStart=k,v.selectionEnd=Math.min(z,v.value.length);else{var H=v.ownerDocument||document,U=H&&H.defaultView||window;if(U.getSelection){var N=U.getSelection(),se=v.textContent.length,ae=Math.min(E.start,se),Oe=E.end===void 0?ae:Math.min(E.end,se);!N.extend&&ae>Oe&&(g=Oe,Oe=ae,ae=g);var O=Ed(v,ae),C=Ed(v,Oe);if(O&&C&&(N.rangeCount!==1||N.anchorNode!==O.node||N.anchorOffset!==O.offset||N.focusNode!==C.node||N.focusOffset!==C.offset)){var j=H.createRange();j.setStart(O.node,O.offset),N.removeAllRanges(),ae>Oe?(N.addRange(j),N.extend(C.node,C.offset)):(j.setEnd(C.node,C.offset),N.addRange(j))}}}}for(H=[],N=v;N=N.parentNode;)N.nodeType===1&&H.push({element:N,left:N.scrollLeft,top:N.scrollTop});for(typeof v.focus=="function"&&v.focus(),v=0;v<H.length;v++){var B=H[v];B.element.scrollLeft=B.left,B.element.scrollTop=B.top}}ds=!!Xo,$o=Xo=null}finally{we=s,Y.p=i,D.T=n}}e.current=t,rt=2}}function Lm(){if(rt===2){rt=0;var e=Fn,t=mi,n=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||n){n=D.T,D.T=null;var i=Y.p;Y.p=2;var s=we;we|=4;try{hm(e,t.alternate,t)}finally{we=s,Y.p=i,D.T=n}}rt=3}}function zm(){if(rt===4||rt===3){rt=0,Le();var e=Fn,t=mi,n=gi,i=wm;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?rt=5:(rt=0,mi=Fn=null,qm(e,e.pendingLanes));var s=e.pendingLanes;if(s===0&&(Jn=null),Js(n),t=t.stateNode,at&&typeof at.onCommitFiberRoot=="function")try{at.onCommitFiberRoot(mt,t,void 0,(t.current.flags&128)===128)}catch{}if(i!==null){t=D.T,s=Y.p,Y.p=2,D.T=null;try{for(var f=e.onRecoverableError,g=0;g<i.length;g++){var v=i[g];f(v.value,{componentStack:v.stack})}}finally{D.T=t,Y.p=s}}(gi&3)!==0&&es(),on(e),s=e.pendingLanes,(n&4194090)!==0&&(s&42)!==0?e===ko?El++:(El=0,ko=e):El=0,xl(0)}}function qm(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,tl(t)))}function es(e){return Dm(),Lm(),zm(),Bm()}function Bm(){if(rt!==5)return!1;var e=Fn,t=Mo;Mo=0;var n=Js(gi),i=D.T,s=Y.p;try{Y.p=32>n?32:n,D.T=null,n=jo,jo=null;var f=Fn,g=gi;if(rt=0,mi=Fn=null,gi=0,(we&6)!==0)throw Error(u(331));var v=we;if(we|=4,xm(f.current),bm(f,f.current,g,n),we=v,xl(0,!1),at&&typeof at.onPostCommitFiberRoot=="function")try{at.onPostCommitFiberRoot(mt,f)}catch{}return!0}finally{Y.p=s,D.T=i,qm(e,t)}}function Hm(e,t,n){t=zt(n,t),t=oo(e.stateNode,t,2),e=Qn(e,t,2),e!==null&&(Bi(e,2),on(e))}function je(e,t,n){if(e.tag===3)Hm(e,e,n);else for(;t!==null;){if(t.tag===3){Hm(t,e,n);break}else if(t.tag===1){var i=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof i.componentDidCatch=="function"&&(Jn===null||!Jn.has(i))){e=zt(n,e),n=Yh(2),i=Qn(t,n,2),i!==null&&(Gh(n,i,t,e),Bi(i,2),on(i));break}}t=t.return}}function Lo(e,t,n){var i=e.pingCache;if(i===null){i=e.pingCache=new D0;var s=new Set;i.set(t,s)}else s=i.get(t),s===void 0&&(s=new Set,i.set(t,s));s.has(n)||(To=!0,s.add(n),e=H0.bind(null,e,t,n),t.then(e,e))}function H0(e,t,n){var i=e.pingCache;i!==null&&i.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,Ue===e&&(ye&n)===n&&(Ve===4||Ve===3&&(ye&62914560)===ye&&300>Be()-Oo?(we&2)===0&&pi(e,0):Co|=n,hi===ye&&(hi=0)),on(e)}function Vm(e,t){t===0&&(t=zf()),e=Ja(e,t),e!==null&&(Bi(e,t),on(e))}function V0(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Vm(e,n)}function P0(e,t){var n=0;switch(e.tag){case 13:var i=e.stateNode,s=e.memoizedState;s!==null&&(n=s.retryLane);break;case 19:i=e.stateNode;break;case 22:i=e.stateNode._retryCache;break;default:throw Error(u(314))}i!==null&&i.delete(t),Vm(e,n)}function Q0(e,t){return _e(e,t)}var ts=null,yi=null,zo=!1,ns=!1,qo=!1,Ua=0;function on(e){e!==yi&&e.next===null&&(yi===null?ts=yi=e:yi=yi.next=e),ns=!0,zo||(zo=!0,G0())}function xl(e,t){if(!qo&&ns){qo=!0;do for(var n=!1,i=ts;i!==null;){if(e!==0){var s=i.pendingLanes;if(s===0)var f=0;else{var g=i.suspendedLanes,v=i.pingedLanes;f=(1<<31-it(42|e)+1)-1,f&=s&~(g&~v),f=f&201326741?f&201326741|1:f?f|2:0}f!==0&&(n=!0,Gm(i,f))}else f=ye,f=ur(i,i===Ue?f:0,i.cancelPendingCommit!==null||i.timeoutHandle!==-1),(f&3)===0||qi(i,f)||(n=!0,Gm(i,f));i=i.next}while(n);qo=!1}}function Y0(){Pm()}function Pm(){ns=zo=!1;var e=0;Ua!==0&&(F0()&&(e=Ua),Ua=0);for(var t=Be(),n=null,i=ts;i!==null;){var s=i.next,f=Qm(i,t);f===0?(i.next=null,n===null?ts=s:n.next=s,s===null&&(yi=n)):(n=i,(e!==0||(f&3)!==0)&&(ns=!0)),i=s}xl(e)}function Qm(e,t){for(var n=e.suspendedLanes,i=e.pingedLanes,s=e.expirationTimes,f=e.pendingLanes&-62914561;0<f;){var g=31-it(f),v=1<<g,E=s[g];E===-1?((v&n)===0||(v&i)!==0)&&(s[g]=py(v,t)):E<=t&&(e.expiredLanes|=v),f&=~v}if(t=Ue,n=ye,n=ur(e,e===t?n:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),i=e.callbackNode,n===0||e===t&&(Re===2||Re===9)||e.cancelPendingCommit!==null)return i!==null&&i!==null&&xe(i),e.callbackNode=null,e.callbackPriority=0;if((n&3)===0||qi(e,n)){if(t=n&-n,t===e.callbackPriority)return t;switch(i!==null&&xe(i),Js(n)){case 2:case 8:n=Je;break;case 32:n=yt;break;case 268435456:n=zi;break;default:n=yt}return i=Ym.bind(null,e),n=_e(n,i),e.callbackPriority=t,e.callbackNode=n,t}return i!==null&&i!==null&&xe(i),e.callbackPriority=2,e.callbackNode=null,2}function Ym(e,t){if(rt!==0&&rt!==5)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(es()&&e.callbackNode!==n)return null;var i=ye;return i=ur(e,e===Ue?i:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),i===0?null:(Tm(e,i,t),Qm(e,Be()),e.callbackNode!=null&&e.callbackNode===n?Ym.bind(null,e):null)}function Gm(e,t){if(es())return null;Tm(e,t,!0)}function G0(){t1(function(){(we&6)!==0?_e(Ge,Y0):Pm()})}function Bo(){return Ua===0&&(Ua=Lf()),Ua}function Xm(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:hr(""+e)}function $m(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}function X0(e,t,n,i,s){if(t==="submit"&&n&&n.stateNode===s){var f=Xm((s[St]||null).action),g=i.submitter;g&&(t=(t=g[St]||null)?Xm(t.formAction):g.getAttribute("formAction"),t!==null&&(f=t,g=null));var v=new vr("action","action",null,i,s);e.push({event:v,listeners:[{instance:null,listener:function(){if(i.defaultPrevented){if(Ua!==0){var E=g?$m(s,g):new FormData(s);io(n,{pending:!0,data:E,method:s.method,action:f},null,E)}}else typeof f=="function"&&(v.preventDefault(),E=g?$m(s,g):new FormData(s),io(n,{pending:!0,data:E,method:s.method,action:f},f,E))},currentTarget:s}]})}}for(var Ho=0;Ho<_u.length;Ho++){var Vo=_u[Ho],$0=Vo.toLowerCase(),K0=Vo[0].toUpperCase()+Vo.slice(1);Kt($0,"on"+K0)}Kt(Td,"onAnimationEnd"),Kt(Cd,"onAnimationIteration"),Kt(Ad,"onAnimationStart"),Kt("dblclick","onDoubleClick"),Kt("focusin","onFocus"),Kt("focusout","onBlur"),Kt(f0,"onTransitionRun"),Kt(d0,"onTransitionStart"),Kt(h0,"onTransitionCancel"),Kt(Od,"onTransitionEnd"),Pa("onMouseEnter",["mouseout","mouseover"]),Pa("onMouseLeave",["mouseout","mouseover"]),Pa("onPointerEnter",["pointerout","pointerover"]),Pa("onPointerLeave",["pointerout","pointerover"]),va("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),va("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),va("onBeforeInput",["compositionend","keypress","textInput","paste"]),va("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),va("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),va("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var _l="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),I0=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(_l));function Km(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var i=e[n],s=i.event;i=i.listeners;e:{var f=void 0;if(t)for(var g=i.length-1;0<=g;g--){var v=i[g],E=v.instance,k=v.currentTarget;if(v=v.listener,E!==f&&s.isPropagationStopped())break e;f=v,s.currentTarget=k;try{f(s)}catch(z){Qr(z)}s.currentTarget=null,f=E}else for(g=0;g<i.length;g++){if(v=i[g],E=v.instance,k=v.currentTarget,v=v.listener,E!==f&&s.isPropagationStopped())break e;f=v,s.currentTarget=k;try{f(s)}catch(z){Qr(z)}s.currentTarget=null,f=E}}}}function me(e,t){var n=t[Fs];n===void 0&&(n=t[Fs]=new Set);var i=e+"__bubble";n.has(i)||(Im(t,e,2,!1),n.add(i))}function Po(e,t,n){var i=0;t&&(i|=4),Im(n,e,i,t)}var as="_reactListening"+Math.random().toString(36).slice(2);function Qo(e){if(!e[as]){e[as]=!0,Pf.forEach(function(n){n!=="selectionchange"&&(I0.has(n)||Po(n,!1,e),Po(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[as]||(t[as]=!0,Po("selectionchange",!1,t))}}function Im(e,t,n,i){switch(bg(t)){case 2:var s=x1;break;case 8:s=_1;break;default:s=ac}n=s.bind(null,t,n,e),s=void 0,!cu||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(s=!0),i?s!==void 0?e.addEventListener(t,n,{capture:!0,passive:s}):e.addEventListener(t,n,!0):s!==void 0?e.addEventListener(t,n,{passive:s}):e.addEventListener(t,n,!1)}function Yo(e,t,n,i,s){var f=i;if((t&1)===0&&(t&2)===0&&i!==null)e:for(;;){if(i===null)return;var g=i.tag;if(g===3||g===4){var v=i.stateNode.containerInfo;if(v===s)break;if(g===4)for(g=i.return;g!==null;){var E=g.tag;if((E===3||E===4)&&g.stateNode.containerInfo===s)return;g=g.return}for(;v!==null;){if(g=Ba(v),g===null)return;if(E=g.tag,E===5||E===6||E===26||E===27){i=f=g;continue e}v=v.parentNode}}i=i.return}nd(function(){var k=f,z=uu(n),H=[];e:{var U=Md.get(e);if(U!==void 0){var N=vr,se=e;switch(e){case"keypress":if(gr(n)===0)break e;case"keydown":case"keyup":N=Qy;break;case"focusin":se="focus",N=mu;break;case"focusout":se="blur",N=mu;break;case"beforeblur":case"afterblur":N=mu;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":N=ld;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":N=jy;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":N=Xy;break;case Td:case Cd:case Ad:N=Ny;break;case Od:N=Ky;break;case"scroll":case"scrollend":N=Oy;break;case"wheel":N=Zy;break;case"copy":case"cut":case"paste":N=Ly;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":N=sd;break;case"toggle":case"beforetoggle":N=Jy}var ae=(t&4)!==0,Oe=!ae&&(e==="scroll"||e==="scrollend"),O=ae?U!==null?U+"Capture":null:U;ae=[];for(var C=k,j;C!==null;){var B=C;if(j=B.stateNode,B=B.tag,B!==5&&B!==26&&B!==27||j===null||O===null||(B=Pi(C,O),B!=null&&ae.push(wl(C,B,j))),Oe)break;C=C.return}0<ae.length&&(U=new N(U,se,null,n,z),H.push({event:U,listeners:ae}))}}if((t&7)===0){e:{if(U=e==="mouseover"||e==="pointerover",N=e==="mouseout"||e==="pointerout",U&&n!==su&&(se=n.relatedTarget||n.fromElement)&&(Ba(se)||se[qa]))break e;if((N||U)&&(U=z.window===z?z:(U=z.ownerDocument)?U.defaultView||U.parentWindow:window,N?(se=n.relatedTarget||n.toElement,N=k,se=se?Ba(se):null,se!==null&&(Oe=c(se),ae=se.tag,se!==Oe||ae!==5&&ae!==27&&ae!==6)&&(se=null)):(N=null,se=k),N!==se)){if(ae=ld,B="onMouseLeave",O="onMouseEnter",C="mouse",(e==="pointerout"||e==="pointerover")&&(ae=sd,B="onPointerLeave",O="onPointerEnter",C="pointer"),Oe=N==null?U:Vi(N),j=se==null?U:Vi(se),U=new ae(B,C+"leave",N,n,z),U.target=Oe,U.relatedTarget=j,B=null,Ba(z)===k&&(ae=new ae(O,C+"enter",se,n,z),ae.target=j,ae.relatedTarget=Oe,B=ae),Oe=B,N&&se)t:{for(ae=N,O=se,C=0,j=ae;j;j=bi(j))C++;for(j=0,B=O;B;B=bi(B))j++;for(;0<C-j;)ae=bi(ae),C--;for(;0<j-C;)O=bi(O),j--;for(;C--;){if(ae===O||O!==null&&ae===O.alternate)break t;ae=bi(ae),O=bi(O)}ae=null}else ae=null;N!==null&&Zm(H,U,N,ae,!1),se!==null&&Oe!==null&&Zm(H,Oe,se,ae,!0)}}e:{if(U=k?Vi(k):window,N=U.nodeName&&U.nodeName.toLowerCase(),N==="select"||N==="input"&&U.type==="file")var Z=gd;else if(hd(U))if(pd)Z=u0;else{Z=r0;var de=l0}else N=U.nodeName,!N||N.toLowerCase()!=="input"||U.type!=="checkbox"&&U.type!=="radio"?k&&ru(k.elementType)&&(Z=gd):Z=s0;if(Z&&(Z=Z(e,k))){md(H,Z,n,z);break e}de&&de(e,U,k),e==="focusout"&&k&&U.type==="number"&&k.memoizedProps.value!=null&&lu(U,"number",U.value)}switch(de=k?Vi(k):window,e){case"focusin":(hd(de)||de.contentEditable==="true")&&(Ia=de,Su=k,Zi=null);break;case"focusout":Zi=Su=Ia=null;break;case"mousedown":Eu=!0;break;case"contextmenu":case"mouseup":case"dragend":Eu=!1,wd(H,n,z);break;case"selectionchange":if(c0)break;case"keydown":case"keyup":wd(H,n,z)}var te;if(pu)e:{switch(e){case"compositionstart":var ie="onCompositionStart";break e;case"compositionend":ie="onCompositionEnd";break e;case"compositionupdate":ie="onCompositionUpdate";break e}ie=void 0}else Ka?fd(e,n)&&(ie="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(ie="onCompositionStart");ie&&(ud&&n.locale!=="ko"&&(Ka||ie!=="onCompositionStart"?ie==="onCompositionEnd"&&Ka&&(te=ad()):(Bn=z,fu="value"in Bn?Bn.value:Bn.textContent,Ka=!0)),de=is(k,ie),0<de.length&&(ie=new rd(ie,e,null,n,z),H.push({event:ie,listeners:de}),te?ie.data=te:(te=dd(n),te!==null&&(ie.data=te)))),(te=e0?t0(e,n):n0(e,n))&&(ie=is(k,"onBeforeInput"),0<ie.length&&(de=new rd("onBeforeInput","beforeinput",null,n,z),H.push({event:de,listeners:ie}),de.data=te)),X0(H,e,k,n,z)}Km(H,t)})}function wl(e,t,n){return{instance:e,listener:t,currentTarget:n}}function is(e,t){for(var n=t+"Capture",i=[];e!==null;){var s=e,f=s.stateNode;if(s=s.tag,s!==5&&s!==26&&s!==27||f===null||(s=Pi(e,n),s!=null&&i.unshift(wl(e,s,f)),s=Pi(e,t),s!=null&&i.push(wl(e,s,f))),e.tag===3)return i;e=e.return}return[]}function bi(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function Zm(e,t,n,i,s){for(var f=t._reactName,g=[];n!==null&&n!==i;){var v=n,E=v.alternate,k=v.stateNode;if(v=v.tag,E!==null&&E===i)break;v!==5&&v!==26&&v!==27||k===null||(E=k,s?(k=Pi(n,f),k!=null&&g.unshift(wl(n,k,E))):s||(k=Pi(n,f),k!=null&&g.push(wl(n,k,E)))),n=n.return}g.length!==0&&e.push({event:t,listeners:g})}var Z0=/\r\n?/g,W0=/\u0000|\uFFFD/g;function Wm(e){return(typeof e=="string"?e:""+e).replace(Z0,`
`).replace(W0,"")}function Jm(e,t){return t=Wm(t),Wm(e)===t}function ls(){}function Ae(e,t,n,i,s,f){switch(n){case"children":typeof i=="string"?t==="body"||t==="textarea"&&i===""||Ga(e,i):(typeof i=="number"||typeof i=="bigint")&&t!=="body"&&Ga(e,""+i);break;case"className":cr(e,"class",i);break;case"tabIndex":cr(e,"tabindex",i);break;case"dir":case"role":case"viewBox":case"width":case"height":cr(e,n,i);break;case"style":ed(e,i,f);break;case"data":if(t!=="object"){cr(e,"data",i);break}case"src":case"href":if(i===""&&(t!=="a"||n!=="href")){e.removeAttribute(n);break}if(i==null||typeof i=="function"||typeof i=="symbol"||typeof i=="boolean"){e.removeAttribute(n);break}i=hr(""+i),e.setAttribute(n,i);break;case"action":case"formAction":if(typeof i=="function"){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof f=="function"&&(n==="formAction"?(t!=="input"&&Ae(e,t,"name",s.name,s,null),Ae(e,t,"formEncType",s.formEncType,s,null),Ae(e,t,"formMethod",s.formMethod,s,null),Ae(e,t,"formTarget",s.formTarget,s,null)):(Ae(e,t,"encType",s.encType,s,null),Ae(e,t,"method",s.method,s,null),Ae(e,t,"target",s.target,s,null)));if(i==null||typeof i=="symbol"||typeof i=="boolean"){e.removeAttribute(n);break}i=hr(""+i),e.setAttribute(n,i);break;case"onClick":i!=null&&(e.onclick=ls);break;case"onScroll":i!=null&&me("scroll",e);break;case"onScrollEnd":i!=null&&me("scrollend",e);break;case"dangerouslySetInnerHTML":if(i!=null){if(typeof i!="object"||!("__html"in i))throw Error(u(61));if(n=i.__html,n!=null){if(s.children!=null)throw Error(u(60));e.innerHTML=n}}break;case"multiple":e.multiple=i&&typeof i!="function"&&typeof i!="symbol";break;case"muted":e.muted=i&&typeof i!="function"&&typeof i!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(i==null||typeof i=="function"||typeof i=="boolean"||typeof i=="symbol"){e.removeAttribute("xlink:href");break}n=hr(""+i),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":i!=null&&typeof i!="function"&&typeof i!="symbol"?e.setAttribute(n,""+i):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":i&&typeof i!="function"&&typeof i!="symbol"?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":i===!0?e.setAttribute(n,""):i!==!1&&i!=null&&typeof i!="function"&&typeof i!="symbol"?e.setAttribute(n,i):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":i!=null&&typeof i!="function"&&typeof i!="symbol"&&!isNaN(i)&&1<=i?e.setAttribute(n,i):e.removeAttribute(n);break;case"rowSpan":case"start":i==null||typeof i=="function"||typeof i=="symbol"||isNaN(i)?e.removeAttribute(n):e.setAttribute(n,i);break;case"popover":me("beforetoggle",e),me("toggle",e),or(e,"popover",i);break;case"xlinkActuate":mn(e,"http://www.w3.org/1999/xlink","xlink:actuate",i);break;case"xlinkArcrole":mn(e,"http://www.w3.org/1999/xlink","xlink:arcrole",i);break;case"xlinkRole":mn(e,"http://www.w3.org/1999/xlink","xlink:role",i);break;case"xlinkShow":mn(e,"http://www.w3.org/1999/xlink","xlink:show",i);break;case"xlinkTitle":mn(e,"http://www.w3.org/1999/xlink","xlink:title",i);break;case"xlinkType":mn(e,"http://www.w3.org/1999/xlink","xlink:type",i);break;case"xmlBase":mn(e,"http://www.w3.org/XML/1998/namespace","xml:base",i);break;case"xmlLang":mn(e,"http://www.w3.org/XML/1998/namespace","xml:lang",i);break;case"xmlSpace":mn(e,"http://www.w3.org/XML/1998/namespace","xml:space",i);break;case"is":or(e,"is",i);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=Cy.get(n)||n,or(e,n,i))}}function Go(e,t,n,i,s,f){switch(n){case"style":ed(e,i,f);break;case"dangerouslySetInnerHTML":if(i!=null){if(typeof i!="object"||!("__html"in i))throw Error(u(61));if(n=i.__html,n!=null){if(s.children!=null)throw Error(u(60));e.innerHTML=n}}break;case"children":typeof i=="string"?Ga(e,i):(typeof i=="number"||typeof i=="bigint")&&Ga(e,""+i);break;case"onScroll":i!=null&&me("scroll",e);break;case"onScrollEnd":i!=null&&me("scrollend",e);break;case"onClick":i!=null&&(e.onclick=ls);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Qf.hasOwnProperty(n))e:{if(n[0]==="o"&&n[1]==="n"&&(s=n.endsWith("Capture"),t=n.slice(2,s?n.length-7:void 0),f=e[St]||null,f=f!=null?f[n]:null,typeof f=="function"&&e.removeEventListener(t,f,s),typeof i=="function")){typeof f!="function"&&f!==null&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,i,s);break e}n in e?e[n]=i:i===!0?e.setAttribute(n,""):or(e,n,i)}}}function st(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":me("error",e),me("load",e);var i=!1,s=!1,f;for(f in n)if(n.hasOwnProperty(f)){var g=n[f];if(g!=null)switch(f){case"src":i=!0;break;case"srcSet":s=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(u(137,t));default:Ae(e,t,f,g,n,null)}}s&&Ae(e,t,"srcSet",n.srcSet,n,null),i&&Ae(e,t,"src",n.src,n,null);return;case"input":me("invalid",e);var v=f=g=s=null,E=null,k=null;for(i in n)if(n.hasOwnProperty(i)){var z=n[i];if(z!=null)switch(i){case"name":s=z;break;case"type":g=z;break;case"checked":E=z;break;case"defaultChecked":k=z;break;case"value":f=z;break;case"defaultValue":v=z;break;case"children":case"dangerouslySetInnerHTML":if(z!=null)throw Error(u(137,t));break;default:Ae(e,t,i,z,n,null)}}Zf(e,f,v,E,k,g,s,!1),fr(e);return;case"select":me("invalid",e),i=g=f=null;for(s in n)if(n.hasOwnProperty(s)&&(v=n[s],v!=null))switch(s){case"value":f=v;break;case"defaultValue":g=v;break;case"multiple":i=v;default:Ae(e,t,s,v,n,null)}t=f,n=g,e.multiple=!!i,t!=null?Ya(e,!!i,t,!1):n!=null&&Ya(e,!!i,n,!0);return;case"textarea":me("invalid",e),f=s=i=null;for(g in n)if(n.hasOwnProperty(g)&&(v=n[g],v!=null))switch(g){case"value":i=v;break;case"defaultValue":s=v;break;case"children":f=v;break;case"dangerouslySetInnerHTML":if(v!=null)throw Error(u(91));break;default:Ae(e,t,g,v,n,null)}Jf(e,i,s,f),fr(e);return;case"option":for(E in n)if(n.hasOwnProperty(E)&&(i=n[E],i!=null))switch(E){case"selected":e.selected=i&&typeof i!="function"&&typeof i!="symbol";break;default:Ae(e,t,E,i,n,null)}return;case"dialog":me("beforetoggle",e),me("toggle",e),me("cancel",e),me("close",e);break;case"iframe":case"object":me("load",e);break;case"video":case"audio":for(i=0;i<_l.length;i++)me(_l[i],e);break;case"image":me("error",e),me("load",e);break;case"details":me("toggle",e);break;case"embed":case"source":case"link":me("error",e),me("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(k in n)if(n.hasOwnProperty(k)&&(i=n[k],i!=null))switch(k){case"children":case"dangerouslySetInnerHTML":throw Error(u(137,t));default:Ae(e,t,k,i,n,null)}return;default:if(ru(t)){for(z in n)n.hasOwnProperty(z)&&(i=n[z],i!==void 0&&Go(e,t,z,i,n,void 0));return}}for(v in n)n.hasOwnProperty(v)&&(i=n[v],i!=null&&Ae(e,t,v,i,n,null))}function J0(e,t,n,i){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var s=null,f=null,g=null,v=null,E=null,k=null,z=null;for(N in n){var H=n[N];if(n.hasOwnProperty(N)&&H!=null)switch(N){case"checked":break;case"value":break;case"defaultValue":E=H;default:i.hasOwnProperty(N)||Ae(e,t,N,null,i,H)}}for(var U in i){var N=i[U];if(H=n[U],i.hasOwnProperty(U)&&(N!=null||H!=null))switch(U){case"type":f=N;break;case"name":s=N;break;case"checked":k=N;break;case"defaultChecked":z=N;break;case"value":g=N;break;case"defaultValue":v=N;break;case"children":case"dangerouslySetInnerHTML":if(N!=null)throw Error(u(137,t));break;default:N!==H&&Ae(e,t,U,N,i,H)}}iu(e,g,v,E,k,z,f,s);return;case"select":N=g=v=U=null;for(f in n)if(E=n[f],n.hasOwnProperty(f)&&E!=null)switch(f){case"value":break;case"multiple":N=E;default:i.hasOwnProperty(f)||Ae(e,t,f,null,i,E)}for(s in i)if(f=i[s],E=n[s],i.hasOwnProperty(s)&&(f!=null||E!=null))switch(s){case"value":U=f;break;case"defaultValue":v=f;break;case"multiple":g=f;default:f!==E&&Ae(e,t,s,f,i,E)}t=v,n=g,i=N,U!=null?Ya(e,!!n,U,!1):!!i!=!!n&&(t!=null?Ya(e,!!n,t,!0):Ya(e,!!n,n?[]:"",!1));return;case"textarea":N=U=null;for(v in n)if(s=n[v],n.hasOwnProperty(v)&&s!=null&&!i.hasOwnProperty(v))switch(v){case"value":break;case"children":break;default:Ae(e,t,v,null,i,s)}for(g in i)if(s=i[g],f=n[g],i.hasOwnProperty(g)&&(s!=null||f!=null))switch(g){case"value":U=s;break;case"defaultValue":N=s;break;case"children":break;case"dangerouslySetInnerHTML":if(s!=null)throw Error(u(91));break;default:s!==f&&Ae(e,t,g,s,i,f)}Wf(e,U,N);return;case"option":for(var se in n)if(U=n[se],n.hasOwnProperty(se)&&U!=null&&!i.hasOwnProperty(se))switch(se){case"selected":e.selected=!1;break;default:Ae(e,t,se,null,i,U)}for(E in i)if(U=i[E],N=n[E],i.hasOwnProperty(E)&&U!==N&&(U!=null||N!=null))switch(E){case"selected":e.selected=U&&typeof U!="function"&&typeof U!="symbol";break;default:Ae(e,t,E,U,i,N)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var ae in n)U=n[ae],n.hasOwnProperty(ae)&&U!=null&&!i.hasOwnProperty(ae)&&Ae(e,t,ae,null,i,U);for(k in i)if(U=i[k],N=n[k],i.hasOwnProperty(k)&&U!==N&&(U!=null||N!=null))switch(k){case"children":case"dangerouslySetInnerHTML":if(U!=null)throw Error(u(137,t));break;default:Ae(e,t,k,U,i,N)}return;default:if(ru(t)){for(var Oe in n)U=n[Oe],n.hasOwnProperty(Oe)&&U!==void 0&&!i.hasOwnProperty(Oe)&&Go(e,t,Oe,void 0,i,U);for(z in i)U=i[z],N=n[z],!i.hasOwnProperty(z)||U===N||U===void 0&&N===void 0||Go(e,t,z,U,i,N);return}}for(var O in n)U=n[O],n.hasOwnProperty(O)&&U!=null&&!i.hasOwnProperty(O)&&Ae(e,t,O,null,i,U);for(H in i)U=i[H],N=n[H],!i.hasOwnProperty(H)||U===N||U==null&&N==null||Ae(e,t,H,U,i,N)}var Xo=null,$o=null;function rs(e){return e.nodeType===9?e:e.ownerDocument}function Fm(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function eg(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function Ko(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Io=null;function F0(){var e=window.event;return e&&e.type==="popstate"?e===Io?!1:(Io=e,!0):(Io=null,!1)}var tg=typeof setTimeout=="function"?setTimeout:void 0,e1=typeof clearTimeout=="function"?clearTimeout:void 0,ng=typeof Promise=="function"?Promise:void 0,t1=typeof queueMicrotask=="function"?queueMicrotask:typeof ng<"u"?function(e){return ng.resolve(null).then(e).catch(n1)}:tg;function n1(e){setTimeout(function(){throw e})}function ta(e){return e==="head"}function ag(e,t){var n=t,i=0,s=0;do{var f=n.nextSibling;if(e.removeChild(n),f&&f.nodeType===8)if(n=f.data,n==="/$"){if(0<i&&8>i){n=i;var g=e.ownerDocument;if(n&1&&Rl(g.documentElement),n&2&&Rl(g.body),n&4)for(n=g.head,Rl(n),g=n.firstChild;g;){var v=g.nextSibling,E=g.nodeName;g[Hi]||E==="SCRIPT"||E==="STYLE"||E==="LINK"&&g.rel.toLowerCase()==="stylesheet"||n.removeChild(g),g=v}}if(s===0){e.removeChild(f),Ul(t);return}s--}else n==="$"||n==="$?"||n==="$!"?s++:i=n.charCodeAt(0)-48;else i=0;n=f}while(n);Ul(t)}function Zo(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":Zo(n),eu(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}e.removeChild(n)}}function a1(e,t,n,i){for(;e.nodeType===1;){var s=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!i&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(i){if(!e[Hi])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(f=e.getAttribute("rel"),f==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(f!==s.rel||e.getAttribute("href")!==(s.href==null||s.href===""?null:s.href)||e.getAttribute("crossorigin")!==(s.crossOrigin==null?null:s.crossOrigin)||e.getAttribute("title")!==(s.title==null?null:s.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(f=e.getAttribute("src"),(f!==(s.src==null?null:s.src)||e.getAttribute("type")!==(s.type==null?null:s.type)||e.getAttribute("crossorigin")!==(s.crossOrigin==null?null:s.crossOrigin))&&f&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var f=s.name==null?null:""+s.name;if(s.type==="hidden"&&e.getAttribute("name")===f)return e}else return e;if(e=Zt(e.nextSibling),e===null)break}return null}function i1(e,t,n){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!n||(e=Zt(e.nextSibling),e===null))return null;return e}function Wo(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function l1(e,t){var n=e.ownerDocument;if(e.data!=="$?"||n.readyState==="complete")t();else{var i=function(){t(),n.removeEventListener("DOMContentLoaded",i)};n.addEventListener("DOMContentLoaded",i),e._reactRetry=i}}function Zt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Jo=null;function ig(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}function lg(e,t,n){switch(t=rs(n),e){case"html":if(e=t.documentElement,!e)throw Error(u(452));return e;case"head":if(e=t.head,!e)throw Error(u(453));return e;case"body":if(e=t.body,!e)throw Error(u(454));return e;default:throw Error(u(451))}}function Rl(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);eu(e)}var Qt=new Map,rg=new Set;function ss(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var An=Y.d;Y.d={f:r1,r:s1,D:u1,C:o1,L:c1,m:f1,X:h1,S:d1,M:m1};function r1(){var e=An.f(),t=Jr();return e||t}function s1(e){var t=Ha(e);t!==null&&t.tag===5&&t.type==="form"?Th(t):An.r(e)}var Si=typeof document>"u"?null:document;function sg(e,t,n){var i=Si;if(i&&typeof t=="string"&&t){var s=Lt(t);s='link[rel="'+e+'"][href="'+s+'"]',typeof n=="string"&&(s+='[crossorigin="'+n+'"]'),rg.has(s)||(rg.add(s),e={rel:e,crossOrigin:n,href:t},i.querySelector(s)===null&&(t=i.createElement("link"),st(t,"link",e),Fe(t),i.head.appendChild(t)))}}function u1(e){An.D(e),sg("dns-prefetch",e,null)}function o1(e,t){An.C(e,t),sg("preconnect",e,t)}function c1(e,t,n){An.L(e,t,n);var i=Si;if(i&&e&&t){var s='link[rel="preload"][as="'+Lt(t)+'"]';t==="image"&&n&&n.imageSrcSet?(s+='[imagesrcset="'+Lt(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(s+='[imagesizes="'+Lt(n.imageSizes)+'"]')):s+='[href="'+Lt(e)+'"]';var f=s;switch(t){case"style":f=Ei(e);break;case"script":f=xi(e)}Qt.has(f)||(e=y({rel:"preload",href:t==="image"&&n&&n.imageSrcSet?void 0:e,as:t},n),Qt.set(f,e),i.querySelector(s)!==null||t==="style"&&i.querySelector(Tl(f))||t==="script"&&i.querySelector(Cl(f))||(t=i.createElement("link"),st(t,"link",e),Fe(t),i.head.appendChild(t)))}}function f1(e,t){An.m(e,t);var n=Si;if(n&&e){var i=t&&typeof t.as=="string"?t.as:"script",s='link[rel="modulepreload"][as="'+Lt(i)+'"][href="'+Lt(e)+'"]',f=s;switch(i){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":f=xi(e)}if(!Qt.has(f)&&(e=y({rel:"modulepreload",href:e},t),Qt.set(f,e),n.querySelector(s)===null)){switch(i){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Cl(f)))return}i=n.createElement("link"),st(i,"link",e),Fe(i),n.head.appendChild(i)}}}function d1(e,t,n){An.S(e,t,n);var i=Si;if(i&&e){var s=Va(i).hoistableStyles,f=Ei(e);t=t||"default";var g=s.get(f);if(!g){var v={loading:0,preload:null};if(g=i.querySelector(Tl(f)))v.loading=5;else{e=y({rel:"stylesheet",href:e,"data-precedence":t},n),(n=Qt.get(f))&&Fo(e,n);var E=g=i.createElement("link");Fe(E),st(E,"link",e),E._p=new Promise(function(k,z){E.onload=k,E.onerror=z}),E.addEventListener("load",function(){v.loading|=1}),E.addEventListener("error",function(){v.loading|=2}),v.loading|=4,us(g,t,i)}g={type:"stylesheet",instance:g,count:1,state:v},s.set(f,g)}}}function h1(e,t){An.X(e,t);var n=Si;if(n&&e){var i=Va(n).hoistableScripts,s=xi(e),f=i.get(s);f||(f=n.querySelector(Cl(s)),f||(e=y({src:e,async:!0},t),(t=Qt.get(s))&&ec(e,t),f=n.createElement("script"),Fe(f),st(f,"link",e),n.head.appendChild(f)),f={type:"script",instance:f,count:1,state:null},i.set(s,f))}}function m1(e,t){An.M(e,t);var n=Si;if(n&&e){var i=Va(n).hoistableScripts,s=xi(e),f=i.get(s);f||(f=n.querySelector(Cl(s)),f||(e=y({src:e,async:!0,type:"module"},t),(t=Qt.get(s))&&ec(e,t),f=n.createElement("script"),Fe(f),st(f,"link",e),n.head.appendChild(f)),f={type:"script",instance:f,count:1,state:null},i.set(s,f))}}function ug(e,t,n,i){var s=(s=le.current)?ss(s):null;if(!s)throw Error(u(446));switch(e){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(t=Ei(n.href),n=Va(s).hoistableStyles,i=n.get(t),i||(i={type:"style",instance:null,count:0,state:null},n.set(t,i)),i):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){e=Ei(n.href);var f=Va(s).hoistableStyles,g=f.get(e);if(g||(s=s.ownerDocument||s,g={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},f.set(e,g),(f=s.querySelector(Tl(e)))&&!f._p&&(g.instance=f,g.state.loading=5),Qt.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Qt.set(e,n),f||g1(s,e,n,g.state))),t&&i===null)throw Error(u(528,""));return g}if(t&&i!==null)throw Error(u(529,""));return null;case"script":return t=n.async,n=n.src,typeof n=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=xi(n),n=Va(s).hoistableScripts,i=n.get(t),i||(i={type:"script",instance:null,count:0,state:null},n.set(t,i)),i):{type:"void",instance:null,count:0,state:null};default:throw Error(u(444,e))}}function Ei(e){return'href="'+Lt(e)+'"'}function Tl(e){return'link[rel="stylesheet"]['+e+"]"}function og(e){return y({},e,{"data-precedence":e.precedence,precedence:null})}function g1(e,t,n,i){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?i.loading=1:(t=e.createElement("link"),i.preload=t,t.addEventListener("load",function(){return i.loading|=1}),t.addEventListener("error",function(){return i.loading|=2}),st(t,"link",n),Fe(t),e.head.appendChild(t))}function xi(e){return'[src="'+Lt(e)+'"]'}function Cl(e){return"script[async]"+e}function cg(e,t,n){if(t.count++,t.instance===null)switch(t.type){case"style":var i=e.querySelector('style[data-href~="'+Lt(n.href)+'"]');if(i)return t.instance=i,Fe(i),i;var s=y({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return i=(e.ownerDocument||e).createElement("style"),Fe(i),st(i,"style",s),us(i,n.precedence,e),t.instance=i;case"stylesheet":s=Ei(n.href);var f=e.querySelector(Tl(s));if(f)return t.state.loading|=4,t.instance=f,Fe(f),f;i=og(n),(s=Qt.get(s))&&Fo(i,s),f=(e.ownerDocument||e).createElement("link"),Fe(f);var g=f;return g._p=new Promise(function(v,E){g.onload=v,g.onerror=E}),st(f,"link",i),t.state.loading|=4,us(f,n.precedence,e),t.instance=f;case"script":return f=xi(n.src),(s=e.querySelector(Cl(f)))?(t.instance=s,Fe(s),s):(i=n,(s=Qt.get(f))&&(i=y({},n),ec(i,s)),e=e.ownerDocument||e,s=e.createElement("script"),Fe(s),st(s,"link",i),e.head.appendChild(s),t.instance=s);case"void":return null;default:throw Error(u(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(i=t.instance,t.state.loading|=4,us(i,n.precedence,e));return t.instance}function us(e,t,n){for(var i=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),s=i.length?i[i.length-1]:null,f=s,g=0;g<i.length;g++){var v=i[g];if(v.dataset.precedence===t)f=v;else if(f!==s)break}f?f.parentNode.insertBefore(e,f.nextSibling):(t=n.nodeType===9?n.head:n,t.insertBefore(e,t.firstChild))}function Fo(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function ec(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var os=null;function fg(e,t,n){if(os===null){var i=new Map,s=os=new Map;s.set(n,i)}else s=os,i=s.get(n),i||(i=new Map,s.set(n,i));if(i.has(e))return i;for(i.set(e,null),n=n.getElementsByTagName(e),s=0;s<n.length;s++){var f=n[s];if(!(f[Hi]||f[ot]||e==="link"&&f.getAttribute("rel")==="stylesheet")&&f.namespaceURI!=="http://www.w3.org/2000/svg"){var g=f.getAttribute(t)||"";g=e+g;var v=i.get(g);v?v.push(f):i.set(g,[f])}}return i}function dg(e,t,n){e=e.ownerDocument||e,e.head.insertBefore(n,t==="title"?e.querySelector("head > title"):null)}function p1(e,t,n){if(n===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function hg(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Al=null;function v1(){}function y1(e,t,n){if(Al===null)throw Error(u(475));var i=Al;if(t.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var s=Ei(n.href),f=e.querySelector(Tl(s));if(f){e=f._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(i.count++,i=cs.bind(i),e.then(i,i)),t.state.loading|=4,t.instance=f,Fe(f);return}f=e.ownerDocument||e,n=og(n),(s=Qt.get(s))&&Fo(n,s),f=f.createElement("link"),Fe(f);var g=f;g._p=new Promise(function(v,E){g.onload=v,g.onerror=E}),st(f,"link",n),t.instance=f}i.stylesheets===null&&(i.stylesheets=new Map),i.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(i.count++,t=cs.bind(i),e.addEventListener("load",t),e.addEventListener("error",t))}}function b1(){if(Al===null)throw Error(u(475));var e=Al;return e.stylesheets&&e.count===0&&tc(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&tc(e,e.stylesheets),e.unsuspend){var i=e.unsuspend;e.unsuspend=null,i()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}function cs(){if(this.count--,this.count===0){if(this.stylesheets)tc(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var fs=null;function tc(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,fs=new Map,t.forEach(S1,e),fs=null,cs.call(e))}function S1(e,t){if(!(t.state.loading&4)){var n=fs.get(e);if(n)var i=n.get(null);else{n=new Map,fs.set(e,n);for(var s=e.querySelectorAll("link[data-precedence],style[data-precedence]"),f=0;f<s.length;f++){var g=s[f];(g.nodeName==="LINK"||g.getAttribute("media")!=="not all")&&(n.set(g.dataset.precedence,g),i=g)}i&&n.set(null,i)}s=t.instance,g=s.getAttribute("data-precedence"),f=n.get(g)||i,f===i&&n.set(null,s),n.set(g,s),this.count++,i=cs.bind(this),s.addEventListener("load",i),s.addEventListener("error",i),f?f.parentNode.insertBefore(s,f.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(s,e.firstChild)),t.state.loading|=4}}var Ol={$$typeof:G,Provider:null,Consumer:null,_currentValue:W,_currentValue2:W,_threadCount:0};function E1(e,t,n,i,s,f,g,v){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Zs(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Zs(0),this.hiddenUpdates=Zs(null),this.identifierPrefix=i,this.onUncaughtError=s,this.onCaughtError=f,this.onRecoverableError=g,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=v,this.incompleteTransitions=new Map}function mg(e,t,n,i,s,f,g,v,E,k,z,H){return e=new E1(e,t,n,g,v,E,k,H),t=1,f===!0&&(t|=24),f=At(3,null,null,t),e.current=f,f.stateNode=e,t=Du(),t.refCount++,e.pooledCache=t,t.refCount++,f.memoizedState={element:i,isDehydrated:n,cache:t},Bu(f),e}function gg(e){return e?(e=Fa,e):Fa}function pg(e,t,n,i,s,f){s=gg(s),i.context===null?i.context=s:i.pendingContext=s,i=Pn(t),i.payload={element:n},f=f===void 0?null:f,f!==null&&(i.callback=f),n=Qn(e,i,t),n!==null&&(Ut(n,e,t),ll(n,e,t))}function vg(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function nc(e,t){vg(e,t),(e=e.alternate)&&vg(e,t)}function yg(e){if(e.tag===13){var t=Ja(e,67108864);t!==null&&Ut(t,e,67108864),nc(e,67108864)}}var ds=!0;function x1(e,t,n,i){var s=D.T;D.T=null;var f=Y.p;try{Y.p=2,ac(e,t,n,i)}finally{Y.p=f,D.T=s}}function _1(e,t,n,i){var s=D.T;D.T=null;var f=Y.p;try{Y.p=8,ac(e,t,n,i)}finally{Y.p=f,D.T=s}}function ac(e,t,n,i){if(ds){var s=ic(i);if(s===null)Yo(e,t,i,hs,n),Sg(e,i);else if(R1(s,e,t,n,i))i.stopPropagation();else if(Sg(e,i),t&4&&-1<w1.indexOf(e)){for(;s!==null;){var f=Ha(s);if(f!==null)switch(f.tag){case 3:if(f=f.stateNode,f.current.memoizedState.isDehydrated){var g=pa(f.pendingLanes);if(g!==0){var v=f;for(v.pendingLanes|=2,v.entangledLanes|=2;g;){var E=1<<31-it(g);v.entanglements[1]|=E,g&=~E}on(f),(we&6)===0&&(Zr=Be()+500,xl(0))}}break;case 13:v=Ja(f,2),v!==null&&Ut(v,f,2),Jr(),nc(f,2)}if(f=ic(i),f===null&&Yo(e,t,i,hs,n),f===s)break;s=f}s!==null&&i.stopPropagation()}else Yo(e,t,i,null,n)}}function ic(e){return e=uu(e),lc(e)}var hs=null;function lc(e){if(hs=null,e=Ba(e),e!==null){var t=c(e);if(t===null)e=null;else{var n=t.tag;if(n===13){if(e=d(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return hs=e,null}function bg(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(zn()){case Ge:return 2;case Je:return 8;case yt:case ir:return 32;case zi:return 268435456;default:return 32}default:return 32}}var rc=!1,na=null,aa=null,ia=null,Ml=new Map,jl=new Map,la=[],w1="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Sg(e,t){switch(e){case"focusin":case"focusout":na=null;break;case"dragenter":case"dragleave":aa=null;break;case"mouseover":case"mouseout":ia=null;break;case"pointerover":case"pointerout":Ml.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":jl.delete(t.pointerId)}}function kl(e,t,n,i,s,f){return e===null||e.nativeEvent!==f?(e={blockedOn:t,domEventName:n,eventSystemFlags:i,nativeEvent:f,targetContainers:[s]},t!==null&&(t=Ha(t),t!==null&&yg(t)),e):(e.eventSystemFlags|=i,t=e.targetContainers,s!==null&&t.indexOf(s)===-1&&t.push(s),e)}function R1(e,t,n,i,s){switch(t){case"focusin":return na=kl(na,e,t,n,i,s),!0;case"dragenter":return aa=kl(aa,e,t,n,i,s),!0;case"mouseover":return ia=kl(ia,e,t,n,i,s),!0;case"pointerover":var f=s.pointerId;return Ml.set(f,kl(Ml.get(f)||null,e,t,n,i,s)),!0;case"gotpointercapture":return f=s.pointerId,jl.set(f,kl(jl.get(f)||null,e,t,n,i,s)),!0}return!1}function Eg(e){var t=Ba(e.target);if(t!==null){var n=c(t);if(n!==null){if(t=n.tag,t===13){if(t=d(n),t!==null){e.blockedOn=t,yy(e.priority,function(){if(n.tag===13){var i=kt();i=Ws(i);var s=Ja(n,i);s!==null&&Ut(s,n,i),nc(n,i)}});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ms(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=ic(e.nativeEvent);if(n===null){n=e.nativeEvent;var i=new n.constructor(n.type,n);su=i,n.target.dispatchEvent(i),su=null}else return t=Ha(n),t!==null&&yg(t),e.blockedOn=n,!1;t.shift()}return!0}function xg(e,t,n){ms(e)&&n.delete(t)}function T1(){rc=!1,na!==null&&ms(na)&&(na=null),aa!==null&&ms(aa)&&(aa=null),ia!==null&&ms(ia)&&(ia=null),Ml.forEach(xg),jl.forEach(xg)}function gs(e,t){e.blockedOn===t&&(e.blockedOn=null,rc||(rc=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,T1)))}var ps=null;function _g(e){ps!==e&&(ps=e,a.unstable_scheduleCallback(a.unstable_NormalPriority,function(){ps===e&&(ps=null);for(var t=0;t<e.length;t+=3){var n=e[t],i=e[t+1],s=e[t+2];if(typeof i!="function"){if(lc(i||n)===null)continue;break}var f=Ha(n);f!==null&&(e.splice(t,3),t-=3,io(f,{pending:!0,data:s,method:n.method,action:i},i,s))}}))}function Ul(e){function t(E){return gs(E,e)}na!==null&&gs(na,e),aa!==null&&gs(aa,e),ia!==null&&gs(ia,e),Ml.forEach(t),jl.forEach(t);for(var n=0;n<la.length;n++){var i=la[n];i.blockedOn===e&&(i.blockedOn=null)}for(;0<la.length&&(n=la[0],n.blockedOn===null);)Eg(n),n.blockedOn===null&&la.shift();if(n=(e.ownerDocument||e).$$reactFormReplay,n!=null)for(i=0;i<n.length;i+=3){var s=n[i],f=n[i+1],g=s[St]||null;if(typeof f=="function")g||_g(n);else if(g){var v=null;if(f&&f.hasAttribute("formAction")){if(s=f,g=f[St]||null)v=g.formAction;else if(lc(s)!==null)continue}else v=g.action;typeof v=="function"?n[i+1]=v:(n.splice(i,3),i-=3),_g(n)}}}function sc(e){this._internalRoot=e}vs.prototype.render=sc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(u(409));var n=t.current,i=kt();pg(n,i,e,t,null,null)},vs.prototype.unmount=sc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;pg(e.current,2,null,e,null,null),Jr(),t[qa]=null}};function vs(e){this._internalRoot=e}vs.prototype.unstable_scheduleHydration=function(e){if(e){var t=Hf();e={blockedOn:null,target:e,priority:t};for(var n=0;n<la.length&&t!==0&&t<la[n].priority;n++);la.splice(n,0,e),n===0&&Eg(e)}};var wg=l.version;if(wg!=="19.1.0")throw Error(u(527,wg,"19.1.0"));Y.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(u(188)):(e=Object.keys(e).join(","),Error(u(268,e)));return e=h(t),e=e!==null?m(e):null,e=e===null?null:e.stateNode,e};var C1={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:D,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ys=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ys.isDisabled&&ys.supportsFiber)try{mt=ys.inject(C1),at=ys}catch{}}return Dl.createRoot=function(e,t){if(!o(e))throw Error(u(299));var n=!1,i="",s=Hh,f=Vh,g=Ph,v=null;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(i=t.identifierPrefix),t.onUncaughtError!==void 0&&(s=t.onUncaughtError),t.onCaughtError!==void 0&&(f=t.onCaughtError),t.onRecoverableError!==void 0&&(g=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(v=t.unstable_transitionCallbacks)),t=mg(e,1,!1,null,null,n,i,s,f,g,v,null),e[qa]=t.current,Qo(e),new sc(t)},Dl.hydrateRoot=function(e,t,n){if(!o(e))throw Error(u(299));var i=!1,s="",f=Hh,g=Vh,v=Ph,E=null,k=null;return n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onUncaughtError!==void 0&&(f=n.onUncaughtError),n.onCaughtError!==void 0&&(g=n.onCaughtError),n.onRecoverableError!==void 0&&(v=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(E=n.unstable_transitionCallbacks),n.formState!==void 0&&(k=n.formState)),t=mg(e,1,!0,t,n??null,i,s,f,g,v,E,k),t.context=gg(null),n=t.current,i=kt(),i=Ws(i),s=Pn(i),s.callback=null,Qn(n,s,i),n=i,t.current.lanes=n,Bi(t,n),on(t),e[qa]=t.current,Qo(e),new vs(t)},Dl.version="19.1.0",Dl}var Ng;function L1(){if(Ng)return cc.exports;Ng=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(l){console.error(l)}}return a(),cc.exports=D1(),cc.exports}var z1=L1(),q1=Object.freeze({InvalidProxyUrlErrorMessage:"The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})",InvalidPublishableKeyErrorMessage:"The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})",MissingPublishableKeyErrorMessage:"Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingSecretKeyErrorMessage:"Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingClerkProvider:"{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider"});function Vp({packageName:a,customMessages:l}){let r=a;const u={...q1,...l};function o(c,d){if(!d)return`${r}: ${c}`;let p=c;const h=c.matchAll(/{{([a-zA-Z0-9-_]+)}}/g);for(const m of h){const y=(d[m[1]]||"").toString();p=p.replace(`{{${m[1]}}}`,y)}return`${r}: ${p}`}return{setPackageName({packageName:c}){return typeof c=="string"&&(r=c),this},setMessages({customMessages:c}){return Object.assign(u,c||{}),this},throwInvalidPublishableKeyError(c){throw new Error(o(u.InvalidPublishableKeyErrorMessage,c))},throwInvalidProxyUrl(c){throw new Error(o(u.InvalidProxyUrlErrorMessage,c))},throwMissingPublishableKeyError(){throw new Error(o(u.MissingPublishableKeyErrorMessage))},throwMissingSecretKeyError(){throw new Error(o(u.MissingSecretKeyErrorMessage))},throwMissingClerkProviderError(c){throw new Error(o(u.MissingClerkProvider,c))},throw(c){throw new Error(o(c))}}}var Pp=Object.defineProperty,B1=Object.getOwnPropertyDescriptor,H1=Object.getOwnPropertyNames,V1=Object.prototype.hasOwnProperty,P1=(a,l)=>{for(var r in l)Pp(a,r,{get:l[r],enumerable:!0})},Q1=(a,l,r,u)=>{if(l&&typeof l=="object"||typeof l=="function")for(let o of H1(l))!V1.call(a,o)&&o!==r&&Pp(a,o,{get:()=>l[o],enumerable:!(u=B1(l,o))||u.enumerable});return a},Y1=(a,l,r)=>(Q1(a,l,"default"),r),G1={strict_mfa:{afterMinutes:10,level:"multi_factor"},strict:{afterMinutes:10,level:"second_factor"},moderate:{afterMinutes:60,level:"second_factor"},lax:{afterMinutes:1440,level:"second_factor"}},X1=new Set(["first_factor","second_factor","multi_factor"]),$1=new Set(["strict_mfa","strict","moderate","lax"]),K1=a=>typeof a=="number"&&a>0,I1=a=>X1.has(a),Z1=a=>$1.has(a),mc=a=>a.replace(/^(org:)*/,"org:"),W1=(a,l)=>{const{orgId:r,orgRole:u,orgPermissions:o}=l;return!a.role&&!a.permission||!r||!u||!o?null:a.permission?o.includes(mc(a.permission)):a.role?mc(u)===mc(a.role):null},Dg=(a,l)=>{const{org:r,user:u}=F1(a),[o,c]=l.split(":"),d=c||o;return o==="org"?r.includes(d):o==="user"?u.includes(d):[...r,...u].includes(d)},J1=(a,l)=>{const{features:r,plans:u}=l;return a.feature&&r?Dg(r,a.feature):a.plan&&u?Dg(u,a.plan):null},F1=a=>{const l=a?a.split(",").map(r=>r.trim()):[];return{org:l.filter(r=>r.split(":")[0].includes("o")).map(r=>r.split(":")[1]),user:l.filter(r=>r.split(":")[0].includes("u")).map(r=>r.split(":")[1])}},eb=a=>{if(!a)return!1;const l=o=>typeof o=="string"?G1[o]:o,r=typeof a=="string"&&Z1(a),u=typeof a=="object"&&I1(a.level)&&K1(a.afterMinutes);return r||u?l.bind(null,a):!1},tb=(a,{factorVerificationAge:l})=>{if(!a.reverification||!l)return null;const r=eb(a.reverification);if(!r)return null;const{level:u,afterMinutes:o}=r(),[c,d]=l,p=c!==-1?o>c:null,h=d!==-1?o>d:null;switch(u){case"first_factor":return p;case"second_factor":return d!==-1?h:p;case"multi_factor":return d===-1?p:p&&h}},nb=a=>l=>{if(!a.userId)return!1;const r=J1(l,a),u=W1(l,a),o=tb(l,a);return[r||u,o].some(c=>c===null)?[r||u,o].some(c=>c===!0):[r||u,o].every(c=>c===!0)},ab=({authObject:{sessionId:a,sessionStatus:l,userId:r,actor:u,orgId:o,orgRole:c,orgSlug:d,signOut:p,getToken:h,has:m,sessionClaims:y},options:{treatPendingAsSignedOut:S=!0}})=>{if(a===void 0&&r===void 0)return{isLoaded:!1,isSignedIn:void 0,sessionId:a,sessionClaims:void 0,userId:r,actor:void 0,orgId:void 0,orgRole:void 0,orgSlug:void 0,has:void 0,signOut:p,getToken:h};if(a===null&&r===null)return{isLoaded:!0,isSignedIn:!1,sessionId:a,userId:r,sessionClaims:null,actor:null,orgId:null,orgRole:null,orgSlug:null,has:()=>!1,signOut:p,getToken:h};if(S&&l==="pending")return{isLoaded:!0,isSignedIn:!1,sessionId:null,userId:null,sessionClaims:null,actor:null,orgId:null,orgRole:null,orgSlug:null,has:()=>!1,signOut:p,getToken:h};if(a&&y&&r&&o&&c)return{isLoaded:!0,isSignedIn:!0,sessionId:a,sessionClaims:y,userId:r,actor:u||null,orgId:o,orgRole:c,orgSlug:d||null,has:m,signOut:p,getToken:h};if(a&&y&&r&&!o)return{isLoaded:!0,isSignedIn:!0,sessionId:a,sessionClaims:y,userId:r,actor:u||null,orgId:null,orgRole:null,orgSlug:null,has:m,signOut:p,getToken:h}},Qp=a=>typeof atob<"u"&&typeof atob=="function"?atob(a):typeof global<"u"&&global.Buffer?new global.Buffer(a,"base64").toString():a,ib=[".lcl.dev",".stg.dev",".lclstage.dev",".stgstage.dev",".dev.lclclerk.com",".stg.lclclerk.com",".accounts.lclclerk.com","accountsstage.dev","accounts.dev"],Yp="pk_live_",lb="pk_test_";function Lg(a,l={}){if(a=a||"",!a||!Fc(a)){if(l.fatal&&!a)throw new Error("Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys");if(l.fatal&&!Fc(a))throw new Error("Publishable key not valid.");return null}const r=a.startsWith(Yp)?"production":"development";let u=Qp(a.split("_")[2]);return u=u.slice(0,-1),l.proxyUrl?u=l.proxyUrl:r!=="development"&&l.domain&&l.isSatellite&&(u=`clerk.${l.domain}`),{instanceType:r,frontendApi:u}}function Fc(a=""){try{const l=a.startsWith(Yp)||a.startsWith(lb),r=Qp(a.split("_")[2]||"").endsWith("$");return l&&r}catch{return!1}}function rb(){const a=new Map;return{isDevOrStagingUrl:l=>{if(!l)return!1;const r=typeof l=="string"?l:l.hostname;let u=a.get(r);return u===void 0&&(u=ib.some(o=>r.endsWith(o)),a.set(r,u)),u}}}var sb="METHOD_CALLED";function Gp(a,l){return{event:sb,payload:{method:a,...l}}}var gc={exports:{}},pc={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var zg;function ub(){if(zg)return pc;zg=1;var a=ki();function l(S,b){return S===b&&(S!==0||1/S===1/b)||S!==S&&b!==b}var r=typeof Object.is=="function"?Object.is:l,u=a.useState,o=a.useEffect,c=a.useLayoutEffect,d=a.useDebugValue;function p(S,b){var x=b(),_=u({inst:{value:x,getSnapshot:b}}),M=_[0].inst,L=_[1];return c(function(){M.value=x,M.getSnapshot=b,h(M)&&L({inst:M})},[S,x,b]),o(function(){return h(M)&&L({inst:M}),S(function(){h(M)&&L({inst:M})})},[S]),d(x),x}function h(S){var b=S.getSnapshot;S=S.value;try{var x=b();return!r(S,x)}catch{return!0}}function m(S,b){return b()}var y=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?m:p;return pc.useSyncExternalStore=a.useSyncExternalStore!==void 0?a.useSyncExternalStore:y,pc}var qg;function ob(){return qg||(qg=1,gc.exports=ub()),gc.exports}var Xp=ob();const $p=0,Kp=1,Ip=2,Bg=3;var Hg=Object.prototype.hasOwnProperty;function ef(a,l){var r,u;if(a===l)return!0;if(a&&l&&(r=a.constructor)===l.constructor){if(r===Date)return a.getTime()===l.getTime();if(r===RegExp)return a.toString()===l.toString();if(r===Array){if((u=a.length)===l.length)for(;u--&&ef(a[u],l[u]););return u===-1}if(!r||typeof a=="object"){u=0;for(r in a)if(Hg.call(a,r)&&++u&&!Hg.call(l,r)||!(r in l)||!ef(a[r],l[r]))return!1;return Object.keys(l).length===u}}return a!==a&&l!==l}const Wt=new WeakMap,fa=()=>{},Ze=fa(),Ds=Object,oe=a=>a===Ze,Xt=a=>typeof a=="function",Dn=(a,l)=>({...a,...l}),Zp=a=>Xt(a.then),vc={},bs={},Sf="undefined",Wl=typeof window!=Sf,tf=typeof document!=Sf,cb=Wl&&"Deno"in window,fb=()=>Wl&&typeof window.requestAnimationFrame!=Sf,oa=(a,l)=>{const r=Wt.get(a);return[()=>!oe(l)&&a.get(l)||vc,u=>{if(!oe(l)){const o=a.get(l);l in bs||(bs[l]=o),r[5](l,Dn(o,u),o||vc)}},r[6],()=>!oe(l)&&l in bs?bs[l]:!oe(l)&&a.get(l)||vc]};let nf=!0;const db=()=>nf,[af,lf]=Wl&&window.addEventListener?[window.addEventListener.bind(window),window.removeEventListener.bind(window)]:[fa,fa],hb=()=>{const a=tf&&document.visibilityState;return oe(a)||a!=="hidden"},mb=a=>(tf&&document.addEventListener("visibilitychange",a),af("focus",a),()=>{tf&&document.removeEventListener("visibilitychange",a),lf("focus",a)}),gb=a=>{const l=()=>{nf=!0,a()},r=()=>{nf=!1};return af("online",l),af("offline",r),()=>{lf("online",l),lf("offline",r)}},pb={isOnline:db,isVisible:hb},vb={initFocus:mb,initReconnect:gb},Vg=!q.useId,Yl=!Wl||cb,yb=a=>fb()?window.requestAnimationFrame(a):setTimeout(a,1),Bl=Yl?A.useEffect:A.useLayoutEffect,yc=typeof navigator<"u"&&navigator.connection,Pg=!Yl&&yc&&(["slow-2g","2g"].includes(yc.effectiveType)||yc.saveData),Ss=new WeakMap,bc=(a,l)=>Ds.prototype.toString.call(a)===`[object ${l}]`;let bb=0;const rf=a=>{const l=typeof a,r=bc(a,"Date"),u=bc(a,"RegExp"),o=bc(a,"Object");let c,d;if(Ds(a)===a&&!r&&!u){if(c=Ss.get(a),c)return c;if(c=++bb+"~",Ss.set(a,c),Array.isArray(a)){for(c="@",d=0;d<a.length;d++)c+=rf(a[d])+",";Ss.set(a,c)}if(o){c="#";const p=Ds.keys(a).sort();for(;!oe(d=p.pop());)oe(a[d])||(c+=d+":"+rf(a[d])+",");Ss.set(a,c)}}else c=r?a.toJSON():l=="symbol"?a.toString():l=="string"?JSON.stringify(a):""+a;return c},Oi=a=>{if(Xt(a))try{a=a()}catch{a=""}const l=a;return a=typeof a=="string"?a:(Array.isArray(a)?a.length:a)?rf(a):"",[a,l]};let Sb=0;const sf=()=>++Sb;async function Wp(...a){const[l,r,u,o]=a,c=Dn({populateCache:!0,throwOnError:!0},typeof o=="boolean"?{revalidate:o}:o||{});let d=c.populateCache;const p=c.rollbackOnError;let h=c.optimisticData;const m=b=>typeof p=="function"?p(b):p!==!1,y=c.throwOnError;if(Xt(r)){const b=r,x=[],_=l.keys();for(const M of _)!/^\$(inf|sub)\$/.test(M)&&b(l.get(M)._k)&&x.push(M);return Promise.all(x.map(S))}return S(r);async function S(b){const[x]=Oi(b);if(!x)return;const[_,M]=oa(l,x),[L,T,P,G]=Wt.get(l),J=()=>{const Te=L[x];return(Xt(c.revalidate)?c.revalidate(_().data,b):c.revalidate!==!1)&&(delete P[x],delete G[x],Te&&Te[0])?Te[0](Ip).then(()=>_().data):_().data};if(a.length<3)return J();let Q=u,F;const I=sf();T[x]=[I,0];const $=!oe(h),re=_(),ne=re.data,pe=re._c,ge=oe(pe)?ne:pe;if($&&(h=Xt(h)?h(ge,ne):h,M({data:h,_c:ge})),Xt(Q))try{Q=Q(ge)}catch(Te){F=Te}if(Q&&Zp(Q))if(Q=await Q.catch(Te=>{F=Te}),I!==T[x][0]){if(F)throw F;return Q}else F&&$&&m(F)&&(d=!0,M({data:ge,_c:Ze}));if(d&&!F)if(Xt(d)){const Te=d(Q,ge);M({data:Te,error:Ze,_c:Ze})}else M({data:Q,error:Ze,_c:Ze});if(T[x][1]=sf(),Promise.resolve(J()).then(()=>{M({_c:Ze})}),F){if(y)throw F;return}return Q}}const Qg=(a,l)=>{for(const r in a)a[r][0]&&a[r][0](l)},Jp=(a,l)=>{if(!Wt.has(a)){const r=Dn(vb,l),u=Object.create(null),o=Wp.bind(Ze,a);let c=fa;const d=Object.create(null),p=(y,S)=>{const b=d[y]||[];return d[y]=b,b.push(S),()=>b.splice(b.indexOf(S),1)},h=(y,S,b)=>{a.set(y,S);const x=d[y];if(x)for(const _ of x)_(S,b)},m=()=>{if(!Wt.has(a)&&(Wt.set(a,[u,Object.create(null),Object.create(null),Object.create(null),o,h,p]),!Yl)){const y=r.initFocus(setTimeout.bind(Ze,Qg.bind(Ze,u,$p))),S=r.initReconnect(setTimeout.bind(Ze,Qg.bind(Ze,u,Kp)));c=()=>{y&&y(),S&&S(),Wt.delete(a)}}};return m(),[a,o,m,c]}return[a,Wt.get(a)[4]]},Eb=(a,l,r,u,o)=>{const c=r.errorRetryCount,d=o.retryCount,p=~~((Math.random()+.5)*(1<<(d<8?d:8)))*r.errorRetryInterval;!oe(c)&&d>c||setTimeout(u,p,o)},xb=ef,[Jl,Fp]=Jp(new Map),ev=Dn({onLoadingSlow:fa,onSuccess:fa,onError:fa,onErrorRetry:Eb,onDiscarded:fa,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:Pg?1e4:5e3,focusThrottleInterval:5*1e3,dedupingInterval:2*1e3,loadingTimeout:Pg?5e3:3e3,compare:xb,isPaused:()=>!1,cache:Jl,mutate:Fp,fallback:{}},pb),tv=(a,l)=>{const r=Dn(a,l);if(l){const{use:u,fallback:o}=a,{use:c,fallback:d}=l;u&&c&&(r.use=u.concat(c)),o&&d&&(r.fallback=Dn(o,d))}return r},uf=A.createContext({}),_b=a=>{const{value:l}=a,r=A.useContext(uf),u=Xt(l),o=A.useMemo(()=>u?l(r):l,[u,r,l]),c=A.useMemo(()=>u?o:tv(r,o),[u,r,o]),d=o&&o.provider,p=A.useRef(Ze);d&&!p.current&&(p.current=Jp(d(c.cache||Jl),o));const h=p.current;return h&&(c.cache=h[0],c.mutate=h[1]),Bl(()=>{if(h)return h[2]&&h[2](),h[3]},[]),A.createElement(uf.Provider,Dn(a,{value:c}))},nv="$inf$",av=Wl&&window.__SWR_DEVTOOLS_USE__,wb=av?window.__SWR_DEVTOOLS_USE__:[],Rb=()=>{av&&(window.__SWR_DEVTOOLS_REACT__=q)},iv=a=>Xt(a[1])?[a[0],a[1],a[2]||{}]:[a[0],null,(a[1]===null?a[2]:a[1])||{}],lv=()=>Dn(ev,A.useContext(uf)),Tb=(a,l)=>{const[r,u]=Oi(a),[,,,o]=Wt.get(Jl);if(o[r])return o[r];const c=l(u);return o[r]=c,c},Cb=a=>(l,r,u)=>a(l,r&&((...c)=>{const[d]=Oi(l),[,,,p]=Wt.get(Jl);if(d.startsWith(nv))return r(...c);const h=p[d];return oe(h)?r(...c):(delete p[d],h)}),u),Ab=wb.concat(Cb),Ob=a=>function(...r){const u=lv(),[o,c,d]=iv(r),p=tv(u,d);let h=a;const{use:m}=p,y=(m||[]).concat(Ab);for(let S=y.length;S--;)h=y[S](h);return h(o,c||p.fetcher||null,p)},Mb=(a,l,r)=>{const u=l[a]||(l[a]=[]);return u.push(r),()=>{const o=u.indexOf(r);o>=0&&(u[o]=u[u.length-1],u.pop())}},jb=(a,l)=>(...r)=>{const[u,o,c]=iv(r),d=(c.use||[]).concat(l);return a(u,o,{...c,use:d})};Rb();const kb=()=>{},Ub=kb(),of=Object,Yg=a=>a===Ub,Nb=a=>typeof a=="function",Es=new WeakMap,Sc=(a,l)=>of.prototype.toString.call(a)===`[object ${l}]`;let Db=0;const cf=a=>{const l=typeof a,r=Sc(a,"Date"),u=Sc(a,"RegExp"),o=Sc(a,"Object");let c,d;if(of(a)===a&&!r&&!u){if(c=Es.get(a),c)return c;if(c=++Db+"~",Es.set(a,c),Array.isArray(a)){for(c="@",d=0;d<a.length;d++)c+=cf(a[d])+",";Es.set(a,c)}if(o){c="#";const p=of.keys(a).sort();for(;!Yg(d=p.pop());)Yg(a[d])||(c+=d+":"+cf(a[d])+",");Es.set(a,c)}}else c=r?a.toJSON():l=="symbol"?a.toString():l=="string"?JSON.stringify(a):""+a;return c},Lb=a=>{if(Nb(a))try{a=a()}catch{a=""}const l=a;return a=typeof a=="string"?a:(Array.isArray(a)?a.length:a)?cf(a):"",[a,l]},zb=a=>Lb(a)[0],Ec=q.use||(a=>{switch(a.status){case"pending":throw a;case"fulfilled":return a.value;case"rejected":throw a.reason;default:throw a.status="pending",a.then(l=>{a.status="fulfilled",a.value=l},l=>{a.status="rejected",a.reason=l}),a}}),xc={dedupe:!0},qb=(a,l,r)=>{const{cache:u,compare:o,suspense:c,fallbackData:d,revalidateOnMount:p,revalidateIfStale:h,refreshInterval:m,refreshWhenHidden:y,refreshWhenOffline:S,keepPreviousData:b}=r,[x,_,M,L]=Wt.get(u),[T,P]=Oi(a),G=A.useRef(!1),J=A.useRef(!1),Q=A.useRef(T),F=A.useRef(l),I=A.useRef(r),$=()=>I.current,re=()=>$().isVisible()&&$().isOnline(),[ne,pe,ge,Te]=oa(u,T),Me=A.useRef({}).current,Se=oe(d)?oe(r.fallback)?Ze:r.fallback[T]:d,D=(_e,xe)=>{for(const We in Me){const Le=We;if(Le==="data"){if(!o(_e[Le],xe[Le])&&(!oe(_e[Le])||!o(fe,xe[Le])))return!1}else if(xe[Le]!==_e[Le])return!1}return!0},Y=A.useMemo(()=>{const _e=!T||!l?!1:oe(p)?$().isPaused()||c?!1:h!==!1:p,xe=Je=>{const yt=Dn(Je);return delete yt._k,_e?{isValidating:!0,isLoading:!0,...yt}:yt},We=ne(),Le=Te(),Be=xe(We),zn=We===Le?Be:xe(Le);let Ge=Be;return[()=>{const Je=xe(ne());return D(Je,Ge)?(Ge.data=Je.data,Ge.isLoading=Je.isLoading,Ge.isValidating=Je.isValidating,Ge.error=Je.error,Ge):(Ge=Je,Je)},()=>zn]},[u,T]),W=Xp.useSyncExternalStore(A.useCallback(_e=>ge(T,(xe,We)=>{D(We,xe)||_e()}),[u,T]),Y[0],Y[1]),ve=!G.current,R=x[T]&&x[T].length>0,V=W.data,X=oe(V)?Se&&Zp(Se)?Ec(Se):Se:V,K=W.error,ee=A.useRef(X),fe=b?oe(V)?oe(ee.current)?X:ee.current:V:X,le=R&&!oe(K)?!1:ve&&!oe(p)?p:$().isPaused()?!1:c?oe(X)?!1:h:oe(X)||h,ut=!!(T&&l&&ve&&le),ke=oe(W.isValidating)?ut:W.isValidating,nn=oe(W.isLoading)?ut:W.isLoading,hn=A.useCallback(async _e=>{const xe=F.current;if(!T||!xe||J.current||$().isPaused())return!1;let We,Le,Be=!0;const zn=_e||{},Ge=!M[T]||!zn.dedupe,Je=()=>Vg?!J.current&&T===Q.current&&G.current:T===Q.current,yt={isValidating:!1,isLoading:!1},ir=()=>{pe(yt)},zi=()=>{const bt=M[T];bt&&bt[1]===Le&&delete M[T]},lr={isValidating:!0};oe(ne().data)&&(lr.isLoading=!0);try{if(Ge&&(pe(lr),r.loadingTimeout&&oe(ne().data)&&setTimeout(()=>{Be&&Je()&&$().onLoadingSlow(T,r)},r.loadingTimeout),M[T]=[xe(P),sf()]),[We,Le]=M[T],We=await We,Ge&&setTimeout(zi,r.dedupingInterval),!M[T]||M[T][1]!==Le)return Ge&&Je()&&$().onDiscarded(T),!1;yt.error=Ze;const bt=_[T];if(!oe(bt)&&(Le<=bt[0]||Le<=bt[1]||bt[1]===0))return ir(),Ge&&Je()&&$().onDiscarded(T),!1;const mt=ne().data;yt.data=o(mt,We)?mt:We,Ge&&Je()&&$().onSuccess(We,T,r)}catch(bt){zi();const mt=$(),{shouldRetryOnError:at}=mt;mt.isPaused()||(yt.error=bt,Ge&&Je()&&(mt.onError(bt,T,mt),(at===!0||Xt(at)&&at(bt))&&(!$().revalidateOnFocus||!$().revalidateOnReconnect||re())&&mt.onErrorRetry(bt,T,mt,an=>{const it=x[T];it&&it[0]&&it[0](Bg,an)},{retryCount:(zn.retryCount||0)+1,dedupe:!0})))}return Be=!1,ir(),!0},[T,u]),ga=A.useCallback((..._e)=>Wp(u,Q.current,..._e),[]);if(Bl(()=>{F.current=l,I.current=r,oe(V)||(ee.current=V)}),Bl(()=>{if(!T)return;const _e=hn.bind(Ze,xc);let xe=0;$().revalidateOnFocus&&(xe=Date.now()+$().focusThrottleInterval);const Le=Mb(T,x,(Be,zn={})=>{if(Be==$p){const Ge=Date.now();$().revalidateOnFocus&&Ge>xe&&re()&&(xe=Ge+$().focusThrottleInterval,_e())}else if(Be==Kp)$().revalidateOnReconnect&&re()&&_e();else{if(Be==Ip)return hn();if(Be==Bg)return hn(zn)}});return J.current=!1,Q.current=T,G.current=!0,pe({_k:P}),le&&(oe(X)||Yl?_e():yb(_e)),()=>{J.current=!0,Le()}},[T]),Bl(()=>{let _e;function xe(){const Le=Xt(m)?m(ne().data):m;Le&&_e!==-1&&(_e=setTimeout(We,Le))}function We(){!ne().error&&(y||$().isVisible())&&(S||$().isOnline())?hn(xc).then(xe):xe()}return xe(),()=>{_e&&(clearTimeout(_e),_e=-1)}},[m,y,S,T]),A.useDebugValue(fe),c&&oe(X)&&T){if(!Vg&&Yl)throw new Error("Fallback data is required when using Suspense in SSR.");F.current=l,I.current=r,J.current=!1;const _e=L[T];if(!oe(_e)){const xe=ga(_e);Ec(xe)}if(oe(K)){const xe=hn(xc);oe(fe)||(xe.status="fulfilled",xe.value=!0),Ec(xe)}else throw K}return{mutate:ga,get data(){return Me.data=!0,fe},get error(){return Me.error=!0,K},get isValidating(){return Me.isValidating=!0,ke},get isLoading(){return Me.isLoading=!0,nn}}},Bb=Ds.defineProperty(_b,"defaultValue",{value:ev}),Ef=Ob(qb),Hb=Object.freeze(Object.defineProperty({__proto__:null,SWRConfig:Bb,default:Ef,mutate:Fp,preload:Tb,unstable_serialize:zb,useSWRConfig:lv},Symbol.toStringTag,{value:"Module"})),Vb=()=>{},Pb=Vb(),ff=Object,Gg=a=>a===Pb,Qb=a=>typeof a=="function",xs=new WeakMap,_c=(a,l)=>ff.prototype.toString.call(a)===`[object ${l}]`;let Yb=0;const df=a=>{const l=typeof a,r=_c(a,"Date"),u=_c(a,"RegExp"),o=_c(a,"Object");let c,d;if(ff(a)===a&&!r&&!u){if(c=xs.get(a),c)return c;if(c=++Yb+"~",xs.set(a,c),Array.isArray(a)){for(c="@",d=0;d<a.length;d++)c+=df(a[d])+",";xs.set(a,c)}if(o){c="#";const p=ff.keys(a).sort();for(;!Gg(d=p.pop());)Gg(a[d])||(c+=d+":"+df(a[d])+",");xs.set(a,c)}}else c=r?a.toJSON():l=="symbol"?a.toString():l=="string"?JSON.stringify(a):""+a;return c},Gb=a=>{if(Qb(a))try{a=a()}catch{a=""}const l=a;return a=typeof a=="string"?a:(Array.isArray(a)?a.length:a)?df(a):"",[a,l]},Xb=a=>Gb(a?a(0,null):null)[0],wc=Promise.resolve(),$b=a=>(l,r,u)=>{const o=A.useRef(!1),{cache:c,initialSize:d=1,revalidateAll:p=!1,persistSize:h=!1,revalidateFirstPage:m=!0,revalidateOnMount:y=!1,parallel:S=!1}=u,[,,,b]=Wt.get(Jl);let x;try{x=Xb(l),x&&(x=nv+x)}catch{}const[_,M,L]=oa(c,x),T=A.useCallback(()=>oe(_()._l)?d:_()._l,[c,x,d]);Xp.useSyncExternalStore(A.useCallback($=>x?L(x,()=>{$()}):()=>{},[c,x]),T,T);const P=A.useCallback(()=>{const $=_()._l;return oe($)?d:$},[x,d]),G=A.useRef(P());Bl(()=>{if(!o.current){o.current=!0;return}x&&M({_l:h?G.current:P()})},[x,c]);const J=y&&!o.current,Q=a(x,async $=>{const re=_()._i,ne=_()._r;M({_r:Ze});const pe=[],ge=P(),[Te]=oa(c,$),Me=Te().data,Se=[];let D=null;for(let Y=0;Y<ge;++Y){const[W,ve]=Oi(l(Y,S?null:D));if(!W)break;const[R,V]=oa(c,W);let X=R().data;const K=p||re||oe(X)||m&&!Y&&!oe(Me)||J||Me&&!oe(Me[Y])&&!u.compare(Me[Y],X);if(r&&(typeof ne=="function"?ne(X,ve):K)){const ee=async()=>{if(!(W in b))X=await r(ve);else{const le=b[W];delete b[W],X=await le}V({data:X,_k:ve}),pe[Y]=X};S?Se.push(ee):await ee()}else pe[Y]=X;S||(D=X)}return S&&await Promise.all(Se.map(Y=>Y())),M({_i:Ze}),pe},u),F=A.useCallback(function($,re){const ne=typeof re=="boolean"?{revalidate:re}:re||{},pe=ne.revalidate!==!1;return x?(pe&&(oe($)?M({_i:!0,_r:ne.revalidate}):M({_i:!1,_r:ne.revalidate})),arguments.length?Q.mutate($,{...ne,revalidate:pe}):Q.mutate()):wc},[x,c]),I=A.useCallback($=>{if(!x)return wc;const[,re]=oa(c,x);let ne;if(Xt($)?ne=$(P()):typeof $=="number"&&(ne=$),typeof ne!="number")return wc;re({_l:ne}),G.current=ne;const pe=[],[ge]=oa(c,x);let Te=null;for(let Me=0;Me<ne;++Me){const[Se]=Oi(l(Me,Te)),[D]=oa(c,Se),Y=Se?D().data:Ze;if(oe(Y))return F(ge().data);pe.push(Y),Te=Y}return F(pe)},[x,c,F,P]);return{size:P(),setSize:I,mutate:F,get data(){return Q.data},get error(){return Q.error},get isValidating(){return Q.isValidating},get isLoading(){return Q.isLoading}}},Kb=jb(Ef,$b);var Xg=Object.prototype.hasOwnProperty;function $g(a,l,r){for(r of a.keys())if(Hl(r,l))return r}function Hl(a,l){var r,u,o;if(a===l)return!0;if(a&&l&&(r=a.constructor)===l.constructor){if(r===Date)return a.getTime()===l.getTime();if(r===RegExp)return a.toString()===l.toString();if(r===Array){if((u=a.length)===l.length)for(;u--&&Hl(a[u],l[u]););return u===-1}if(r===Set){if(a.size!==l.size)return!1;for(u of a)if(o=u,o&&typeof o=="object"&&(o=$g(l,o),!o)||!l.has(o))return!1;return!0}if(r===Map){if(a.size!==l.size)return!1;for(u of a)if(o=u[0],o&&typeof o=="object"&&(o=$g(l,o),!o)||!Hl(u[1],l.get(o)))return!1;return!0}if(r===ArrayBuffer)a=new Uint8Array(a),l=new Uint8Array(l);else if(r===DataView){if((u=a.byteLength)===l.byteLength)for(;u--&&a.getInt8(u)===l.getInt8(u););return u===-1}if(ArrayBuffer.isView(a)){if((u=a.byteLength)===l.byteLength)for(;u--&&a[u]===l[u];);return u===-1}if(!r||typeof a=="object"){u=0;for(r in a)if(Xg.call(a,r)&&++u&&!Xg.call(l,r)||!(r in l)||!Hl(a[r],l[r]))return!1;return Object.keys(l).length===u}}return a!==a&&l!==l}function Ib(a,l){if(!a)throw typeof l=="string"?new Error(l):new Error(`${l.displayName} not found`)}var Ui=(a,l)=>{const{assertCtxFn:r=Ib}={},u=q.createContext(void 0);return u.displayName=a,[u,()=>{const d=q.useContext(u);return r(d,`${a} not found`),d.value},()=>{const d=q.useContext(u);return d?d.value:{}}]},xf={};P1(xf,{useSWR:()=>Ef,useSWRInfinite:()=>Kb});Y1(xf,Hb);var[rv,sv]=Ui("ClerkInstanceContext"),[Zb,Wb]=Ui("UserContext"),[Jb,X_]=Ui("ClientContext"),[Fb,$_]=Ui("SessionContext");q.createContext({});var[eS,K_]=Ui("OrganizationContext"),tS=({children:a,organization:l,swrConfig:r})=>q.createElement(xf.SWRConfig,{value:r},q.createElement(eS.Provider,{value:{value:{organization:l}}},a));function uv(a){if(!q.useContext(rv)){if(typeof a=="function"){a();return}throw new Error(`${a} can only be used within the <ClerkProvider /> component.

Possible fixes:
1. Ensure that the <ClerkProvider /> is correctly wrapping your application where this component is used.
2. Check for multiple versions of the \`@clerk/shared\` package in your project. Use a tool like \`npm ls @clerk/shared\` to identify multiple versions, and update your dependencies to only rely on one.

Learn more: https://clerk.com/docs/components/clerk-provider`.trim())}}typeof window<"u"?q.useLayoutEffect:q.useEffect;var Kg="useUser";function ov(){uv(Kg);const a=Wb();return sv().telemetry?.record(Gp(Kg)),a===void 0?{isLoaded:!1,isSignedIn:void 0,user:void 0}:a===null?{isLoaded:!0,isSignedIn:!1,user:null}:{isLoaded:!0,isSignedIn:!0,user:a}}var Ig=Hl,nS=()=>{try{return!1}catch{}return!1},aS=()=>{try{return!1}catch{}return!1},iS=()=>{try{return!0}catch{}return!1},Zg=new Set,_f=(a,l,r)=>{const u=aS()||iS(),o=a;Zg.has(o)||u||(Zg.add(o),console.warn(`Clerk - DEPRECATION WARNING: "${a}" is deprecated and will be removed in the next major release.
${l}`))},dn=Vp({packageName:"@clerk/clerk-react"});function lS(a){dn.setMessages(a).setPackageName(a)}var[rS,sS]=Ui("AuthContext"),uS=rv,cv=sv,oS="You've added multiple <ClerkProvider> components in your React component tree. Wrap your components in a single <ClerkProvider>.",cS=a=>`You've passed multiple children components to <${a}/>. You can only pass a single child component or text.`,fS="Invalid state. Feel free to submit a bug or reach out to support here: https://clerk.com/support",Rc="Unsupported usage of isSatellite, domain or proxyUrl. The usage of isSatellite, domain or proxyUrl as function is not supported in non-browser environments.",dS="<UserProfile.Page /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.",hS="<UserProfile.Link /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.",mS="<OrganizationProfile.Page /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.",gS="<OrganizationProfile.Link /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.",pS=a=>`<${a} /> can only accept <${a}.Page /> and <${a}.Link /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.`,vS=a=>`Missing props. <${a}.Page /> component requires the following props: url, label, labelIcon, alongside with children to be rendered inside the page.`,yS=a=>`Missing props. <${a}.Link /> component requires the following props: url, label and labelIcon.`,bS="<UserButton /> can only accept <UserButton.UserProfilePage />, <UserButton.UserProfileLink /> and <UserButton.MenuItems /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.",SS="<UserButton.MenuItems /> component can only accept <UserButton.Action /> and <UserButton.Link /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.",ES="<UserButton.MenuItems /> component needs to be a direct child of `<UserButton />`.",xS="<UserButton.Action /> component needs to be a direct child of `<UserButton.MenuItems />`.",_S="<UserButton.Link /> component needs to be a direct child of `<UserButton.MenuItems />`.",wS="Missing props. <UserButton.Link /> component requires the following props: href, label and labelIcon.",RS="Missing props. <UserButton.Action /> component requires the following props: label.",fv=a=>{uv(()=>{dn.throwMissingClerkProviderError({source:a})})},dv=a=>new Promise(l=>{const r=u=>{["ready","degraded"].includes(u)&&(l(),a.off("status",r))};a.on("status",r,{notify:!0})}),TS=a=>async l=>(await dv(a),a.session?a.session.getToken(l):null),CS=a=>async(...l)=>(await dv(a),a.signOut(...l)),hv=(a={})=>{var l,r;fv("useAuth");const{treatPendingAsSignedOut:u,...o}=a??{},c=o;let p=sS();p.sessionId===void 0&&p.userId===void 0&&(p=c??{});const h=cv(),m=A.useCallback(TS(h),[h]),y=A.useCallback(CS(h),[h]);return(l=h.telemetry)==null||l.record(Gp("useAuth",{treatPendingAsSignedOut:u})),AS({...p,getToken:m,signOut:y},{treatPendingAsSignedOut:u??((r=h.__internal_getOption)==null?void 0:r.call(h,"treatPendingAsSignedOut"))})};function AS(a,{treatPendingAsSignedOut:l=!0}={}){const{userId:r,orgId:u,orgRole:o,has:c,signOut:d,getToken:p,orgPermissions:h,factorVerificationAge:m,sessionClaims:y}=a??{},S=A.useCallback(x=>c?c(x):nb({userId:r,orgId:u,orgRole:o,orgPermissions:h,factorVerificationAge:m,features:y?.fea||"",plans:y?.pla||""})(x),[c,r,u,o,h,m]),b=ab({authObject:{...a,getToken:p,signOut:d,has:S},options:{treatPendingAsSignedOut:l}});return b||dn.throw(fS)}var Pe=(a,l)=>{const u=(typeof l=="string"?l:l?.component)||a.displayName||a.name||"Component";a.displayName=u;const o=typeof l=="string"?void 0:l,c=d=>{fv(u||"withClerk");const p=cv();return!p.loaded&&!o?.renderWhileLoading?null:q.createElement(a,{...d,component:u,clerk:p})};return c.displayName=`withClerk(${u})`,c};Pe(({clerk:a,...l})=>{const{client:r,session:u}=a,o=r.signedInSessions?r.signedInSessions.length>0:r.activeSessions&&r.activeSessions.length>0;return q.useEffect(()=>{u===null&&o?a.redirectToAfterSignOut():a.redirectToSignIn(l)},[]),null},"RedirectToSignIn");Pe(({clerk:a,...l})=>(q.useEffect(()=>{a.redirectToSignUp(l)},[]),null),"RedirectToSignUp");Pe(({clerk:a})=>(q.useEffect(()=>{_f("RedirectToUserProfile","Use the `redirectToUserProfile()` method instead."),a.redirectToUserProfile()},[]),null),"RedirectToUserProfile");Pe(({clerk:a})=>(q.useEffect(()=>{_f("RedirectToOrganizationProfile","Use the `redirectToOrganizationProfile()` method instead."),a.redirectToOrganizationProfile()},[]),null),"RedirectToOrganizationProfile");Pe(({clerk:a})=>(q.useEffect(()=>{_f("RedirectToCreateOrganization","Use the `redirectToCreateOrganization()` method instead."),a.redirectToCreateOrganization()},[]),null),"RedirectToCreateOrganization");Pe(({clerk:a,...l})=>(q.useEffect(()=>{a.handleRedirectCallback(l)},[]),null),"AuthenticateWithRedirectCallback");var mv=a=>{throw TypeError(a)},wf=(a,l,r)=>l.has(a)||mv("Cannot "+r),qe=(a,l,r)=>(wf(a,l,"read from private field"),r?r.call(a):l.get(a)),Na=(a,l,r)=>l.has(a)?mv("Cannot add the same private member more than once"):l instanceof WeakSet?l.add(a):l.set(a,r),_i=(a,l,r,u)=>(wf(a,l,"write to private field"),l.set(a,r),r),Wg=(a,l,r)=>(wf(a,l,"access private method"),r),OS=(a,l="5.69.1")=>{if(a)return a;const r=MS(l);return r?r==="snapshot"?"5.69.1":r:jS(l)},MS=a=>a.trim().replace(/^v/,"").match(/-(.+?)(\.|$)/)?.[1],jS=a=>a.trim().replace(/^v/,"").split(".")[0];function kS(a){return a?US(a)||gv(a):!0}function US(a){return/^http(s)?:\/\//.test(a||"")}function gv(a){return a.startsWith("/")}function NS(a){return a?gv(a)?new URL(a,window.location.origin).toString():a:""}function DS(a){if(!a)return"";let l;if(a.match(/^(clerk\.)+\w*$/))l=/(clerk\.)*(?=clerk\.)/;else{if(a.match(/\.clerk.accounts/))return a;l=/^(clerk\.)*/gi}return`clerk.${a.replace(l,"")}`}var LS={initialDelay:125,maxDelayBetweenRetries:0,factor:2,shouldRetry:(a,l)=>l<5,retryImmediately:!1,jitter:!0},zS=100,pv=async a=>new Promise(l=>setTimeout(l,a)),vv=(a,l)=>l?a*(1+Math.random()):a,qS=a=>{let l=0;const r=()=>{const u=a.initialDelay,o=a.factor;let c=u*Math.pow(o,l);return c=vv(c,a.jitter),Math.min(a.maxDelayBetweenRetries||c,c)};return async()=>{await pv(r()),l++}},BS=async(a,l={})=>{let r=0;const{shouldRetry:u,initialDelay:o,maxDelayBetweenRetries:c,factor:d,retryImmediately:p,jitter:h}={...LS,...l},m=qS({initialDelay:o,maxDelayBetweenRetries:c,factor:d,jitter:h});for(;;)try{return await a()}catch(y){if(r++,!u(y,r))throw y;p&&r===1?await pv(vv(zS,h)):await m()}},HS="loadScript cannot be called when document does not exist",VS="loadScript cannot be called without a src";async function PS(a="",l){const{async:r,defer:u,beforeLoad:o,crossOrigin:c,nonce:d}=l||{};return BS(()=>new Promise((h,m)=>{a||m(new Error(VS)),(!document||!document.body)&&m(HS);const y=document.createElement("script");c&&y.setAttribute("crossorigin",c),y.async=r||!1,y.defer=u||!1,y.addEventListener("load",()=>{y.remove(),h(y)}),y.addEventListener("error",()=>{y.remove(),m()}),y.src=a,y.nonce=d,o?.(y),document.body.appendChild(y)}),{shouldRetry:(h,m)=>m<=5})}var Jg="Clerk: Failed to load Clerk",{isDevOrStagingUrl:QS}=rb(),yv=Vp({packageName:"@clerk/shared"});function YS(a){yv.setPackageName({packageName:a})}var GS=async a=>{const l=document.querySelector("script[data-clerk-js-script]");if(l)return new Promise((r,u)=>{l.addEventListener("load",()=>{r(l)}),l.addEventListener("error",()=>{u(Jg)})});if(!a?.publishableKey){yv.throwMissingPublishableKeyError();return}return PS(XS(a),{async:!0,crossOrigin:"anonymous",nonce:a.nonce,beforeLoad:KS(a)}).catch(()=>{throw new Error(Jg)})},XS=a=>{const{clerkJSUrl:l,clerkJSVariant:r,clerkJSVersion:u,proxyUrl:o,domain:c,publishableKey:d}=a;if(l)return l;let p="";o&&kS(o)?p=NS(o).replace(/http(s)?:\/\//,""):c&&!QS(Lg(d)?.frontendApi||"")?p=DS(c):p=Lg(d)?.frontendApi||"";const h=r?`${r.replace(/\.+$/,"")}.`:"",m=OS(u);return`https://${p}/npm/@clerk/clerk-js@${m}/dist/clerk.${h}browser.js`},$S=a=>{const l={};return a.publishableKey&&(l["data-clerk-publishable-key"]=a.publishableKey),a.proxyUrl&&(l["data-clerk-proxy-url"]=a.proxyUrl),a.domain&&(l["data-clerk-domain"]=a.domain),a.nonce&&(l.nonce=a.nonce),l},KS=a=>l=>{const r=$S(a);for(const u in r)l.setAttribute(u,r[u])},vt=a=>{nS()&&console.error(`Clerk: ${a}`)};function Tc(a,l,r){if(typeof a=="function")return a(l);if(typeof a<"u")return a;if(typeof r<"u")return r}var IS=Hp(),Fg=(a,...l)=>{const r={...a};for(const u of l)delete r[u];return r},ZS=(a,l,r)=>!a&&r?WS(r):JS(l),WS=a=>{const l=a.userId,r=a.user,u=a.sessionId,o=a.sessionStatus,c=a.sessionClaims,d=a.session,p=a.organization,h=a.orgId,m=a.orgRole,y=a.orgPermissions,S=a.orgSlug,b=a.actor,x=a.factorVerificationAge;return{userId:l,user:r,sessionId:u,session:d,sessionStatus:o,sessionClaims:c,organization:p,orgId:h,orgRole:m,orgPermissions:y,orgSlug:S,actor:b,factorVerificationAge:x}},JS=a=>{const l=a.user?a.user.id:a.user,r=a.user,u=a.session?a.session.id:a.session,o=a.session,c=a.session?.status,d=a.session?a.session.lastActiveToken?.jwt?.claims:null,p=a.session?a.session.factorVerificationAge:null,h=o?.actor,m=a.organization,y=a.organization?a.organization.id:a.organization,S=m?.slug,b=m&&r?.organizationMemberships?.find(M=>M.organization.id===y),x=b&&b.permissions,_=b&&b.role;return{userId:l,user:r,sessionId:u,session:o,sessionStatus:c,sessionClaims:d,organization:m,orgId:y,orgRole:_,orgSlug:S,orgPermissions:x,actor:h,factorVerificationAge:p}};function ep(){return typeof window<"u"}var tp=(a,l,r,u,o)=>{const{notify:c}=o||{};let d=a.get(r);d||(d=[],a.set(r,d)),d.push(u),c&&l.has(r)&&u(l.get(r))},np=(a,l,r)=>(a.get(l)||[]).map(u=>u(r)),ap=(a,l,r)=>{const u=a.get(l);u&&(r?u.splice(u.indexOf(r)>>>0,1):a.set(l,[]))},FS=()=>{const a=new Map,l=new Map,r=new Map;return{on:(...o)=>tp(a,l,...o),prioritizedOn:(...o)=>tp(r,l,...o),emit:(o,c)=>{l.set(o,c),np(r,o,c),np(a,o,c)},off:(...o)=>ap(a,...o),prioritizedOff:(...o)=>ap(r,...o),internal:{retrieveListeners:o=>a.get(o)||[]}}},_s={Status:"status"},eE=()=>FS();typeof window<"u"&&!window.global&&(window.global=typeof global>"u"?window:global);var Hs=a=>l=>{try{return q.Children.only(a)}catch{return dn.throw(cS(l))}},Vs=(a,l)=>(a||(a=l),typeof a=="string"&&(a=q.createElement("button",null,a)),a),Ps=a=>(...l)=>{if(a&&typeof a=="function")return a(...l)};function tE(a){return typeof a=="function"}var ws=new Map;function nE(a,l,r=1){q.useEffect(()=>{const u=ws.get(a)||0;return u==r?dn.throw(l):(ws.set(a,u+1),()=>{ws.set(a,(ws.get(a)||1)-1)})},[])}function aE(a,l,r){const u=a.displayName||a.name||l||"Component",o=c=>(nE(l,r),q.createElement(a,{...c}));return o.displayName=`withMaxAllowedInstancesGuard(${u})`,o}var Vl=a=>{const l=Array(a.length).fill(null),[r,u]=A.useState(l);return a.map((o,c)=>({id:o.id,mount:d=>u(p=>p.map((h,m)=>m===c?d:h)),unmount:()=>u(d=>d.map((p,h)=>h===c?null:p)),portal:()=>q.createElement(q.Fragment,null,r[c]?IS.createPortal(o.component,r[c]):null)}))},pt=(a,l)=>!!a&&q.isValidElement(a)&&a?.type===l,bv=(a,l)=>xv({children:a,reorderItemsLabels:["account","security"],LinkComponent:er,PageComponent:Fl,MenuItemsComponent:Ys,componentName:"UserProfile"},l),Sv=(a,l)=>xv({children:a,reorderItemsLabels:["general","members"],LinkComponent:Xs,PageComponent:Gs,componentName:"OrganizationProfile"},l),Ev=a=>{const l=[],r=[Xs,Gs,Ys,Fl,er];return q.Children.forEach(a,u=>{r.some(o=>pt(u,o))||l.push(u)}),l},xv=(a,l)=>{const{children:r,LinkComponent:u,PageComponent:o,MenuItemsComponent:c,reorderItemsLabels:d,componentName:p}=a,{allowForAnyChildren:h=!1}=l||{},m=[];q.Children.forEach(r,P=>{if(!pt(P,o)&&!pt(P,u)&&!pt(P,c)){P&&!h&&vt(pS(p));return}const{props:G}=P,{children:J,label:Q,url:F,labelIcon:I}=G;if(pt(P,o))if(ip(G,d))m.push({label:Q});else if(Cc(G))m.push({label:Q,labelIcon:I,children:J,url:F});else{vt(vS(p));return}if(pt(P,u))if(Ac(G))m.push({label:Q,labelIcon:I,url:F});else{vt(yS(p));return}});const y=[],S=[],b=[];m.forEach((P,G)=>{if(Cc(P)){y.push({component:P.children,id:G}),S.push({component:P.labelIcon,id:G});return}Ac(P)&&b.push({component:P.labelIcon,id:G})});const x=Vl(y),_=Vl(S),M=Vl(b),L=[],T=[];return m.forEach((P,G)=>{if(ip(P,d)){L.push({label:P.label});return}if(Cc(P)){const{portal:J,mount:Q,unmount:F}=x.find(ne=>ne.id===G),{portal:I,mount:$,unmount:re}=_.find(ne=>ne.id===G);L.push({label:P.label,url:P.url,mount:Q,unmount:F,mountIcon:$,unmountIcon:re}),T.push(J),T.push(I);return}if(Ac(P)){const{portal:J,mount:Q,unmount:F}=M.find(I=>I.id===G);L.push({label:P.label,url:P.url,mountIcon:Q,unmountIcon:F}),T.push(J);return}}),{customPages:L,customPagesPortals:T}},ip=(a,l)=>{const{children:r,label:u,url:o,labelIcon:c}=a;return!r&&!o&&!c&&l.some(d=>d===u)},Cc=a=>{const{children:l,label:r,url:u,labelIcon:o}=a;return!!l&&!!u&&!!o&&!!r},Ac=a=>{const{children:l,label:r,url:u,labelIcon:o}=a;return!l&&!!u&&!!o&&!!r},iE=a=>lE({children:a,reorderItemsLabels:["manageAccount","signOut"],MenuItemsComponent:Ys,MenuActionComponent:wv,MenuLinkComponent:Rv,UserProfileLinkComponent:er,UserProfilePageComponent:Fl}),lE=({children:a,MenuItemsComponent:l,MenuActionComponent:r,MenuLinkComponent:u,UserProfileLinkComponent:o,UserProfilePageComponent:c,reorderItemsLabels:d})=>{const p=[],h=[],m=[];q.Children.forEach(a,_=>{if(!pt(_,l)&&!pt(_,o)&&!pt(_,c)){_&&vt(bS);return}if(pt(_,o)||pt(_,c))return;const{props:M}=_;q.Children.forEach(M.children,L=>{if(!pt(L,r)&&!pt(L,u)){L&&vt(SS);return}const{props:T}=L,{label:P,labelIcon:G,href:J,onClick:Q,open:F}=T;if(pt(L,r))if(lp(T,d))p.push({label:P});else if(Oc(T)){const I={label:P,labelIcon:G};if(Q!==void 0)p.push({...I,onClick:Q});else if(F!==void 0)p.push({...I,open:F.startsWith("/")?F:`/${F}`});else{vt("Custom menu item must have either onClick or open property");return}}else{vt(RS);return}if(pt(L,u))if(Mc(T))p.push({label:P,labelIcon:G,href:J});else{vt(wS);return}})});const y=[],S=[];p.forEach((_,M)=>{Oc(_)&&y.push({component:_.labelIcon,id:M}),Mc(_)&&S.push({component:_.labelIcon,id:M})});const b=Vl(y),x=Vl(S);return p.forEach((_,M)=>{if(lp(_,d)&&h.push({label:_.label}),Oc(_)){const{portal:L,mount:T,unmount:P}=b.find(J=>J.id===M),G={label:_.label,mountIcon:T,unmountIcon:P};"onClick"in _?G.onClick=_.onClick:"open"in _&&(G.open=_.open),h.push(G),m.push(L)}if(Mc(_)){const{portal:L,mount:T,unmount:P}=x.find(G=>G.id===M);h.push({label:_.label,href:_.href,mountIcon:T,unmountIcon:P}),m.push(L)}}),{customMenuItems:h,customMenuItemsPortals:m}},lp=(a,l)=>{const{children:r,label:u,onClick:o,labelIcon:c}=a;return!r&&!o&&!c&&l.some(d=>d===u)},Oc=a=>{const{label:l,labelIcon:r,onClick:u,open:o}=a;return!!r&&!!l&&(typeof u=="function"||typeof o=="string")},Mc=a=>{const{label:l,href:r,labelIcon:u}=a;return!!r&&!!u&&!!l};function rE(a){const{root:l=document?.body,selector:r,timeout:u=0}=a;return new Promise((o,c)=>{if(!l){c(new Error("No root element provided"));return}let d=l;if(r&&(d=l?.querySelector(r)),d?.childElementCount&&d.childElementCount>0){o();return}const h=new MutationObserver(m=>{for(const y of m)if(y.type==="childList"&&(!d&&r&&(d=l?.querySelector(r)),d?.childElementCount&&d.childElementCount>0)){h.disconnect(),o();return}});h.observe(l,{childList:!0,subtree:!0}),u>0&&setTimeout(()=>{h.disconnect(),c(new Error("Timeout waiting for element children"))},u)})}function $t(a){const l=A.useRef(),[r,u]=A.useState("rendering");return A.useEffect(()=>{if(!a)throw new Error("Clerk: no component name provided, unable to detect mount.");typeof window<"u"&&!l.current&&(l.current=rE({selector:`[data-clerk-component="${a}"]`}).then(()=>{u("rendered")}).catch(()=>{u("error")}))},[a]),r}var Rs=a=>"mount"in a,rp=a=>"open"in a,sp=a=>a?.map(({mountIcon:l,unmountIcon:r,...u})=>u),Tt=class extends q.PureComponent{constructor(){super(...arguments),this.rootRef=q.createRef()}componentDidUpdate(a){var l,r,u,o;if(!Rs(a)||!Rs(this.props))return;const c=Fg(a.props,"customPages","customMenuItems","children"),d=Fg(this.props.props,"customPages","customMenuItems","children"),p=((l=c.customPages)==null?void 0:l.length)!==((r=d.customPages)==null?void 0:r.length),h=((u=c.customMenuItems)==null?void 0:u.length)!==((o=d.customMenuItems)==null?void 0:o.length),m=sp(a.props.customMenuItems),y=sp(this.props.props.customMenuItems);(!Ig(c,d)||!Ig(m,y)||p||h)&&this.rootRef.current&&this.props.updateProps({node:this.rootRef.current,props:this.props.props})}componentDidMount(){this.rootRef.current&&(Rs(this.props)&&this.props.mount(this.rootRef.current,this.props.props),rp(this.props)&&this.props.open(this.props.props))}componentWillUnmount(){this.rootRef.current&&(Rs(this.props)&&this.props.unmount(this.rootRef.current),rp(this.props)&&this.props.close())}render(){const{hideRootHtmlElement:a=!1}=this.props,l={ref:this.rootRef,...this.props.rootProps,...this.props.component&&{"data-clerk-component":this.props.component}};return q.createElement(q.Fragment,null,!a&&q.createElement("div",{...l}),this.props.children)}},Qs=a=>{var l,r;return q.createElement(q.Fragment,null,(l=a?.customPagesPortals)==null?void 0:l.map((u,o)=>A.createElement(u,{key:o})),(r=a?.customMenuItemsPortals)==null?void 0:r.map((u,o)=>A.createElement(u,{key:o})))},sE=Pe(({clerk:a,component:l,fallback:r,...u})=>{const c=$t(l)==="rendering"||!a.loaded,d={...c&&r&&{style:{display:"none"}}};return q.createElement(q.Fragment,null,c&&r,a.loaded&&q.createElement(Tt,{component:l,mount:a.mountSignIn,unmount:a.unmountSignIn,updateProps:a.__unstable__updateProps,props:u,rootProps:d}))},{component:"SignIn",renderWhileLoading:!0}),uE=Pe(({clerk:a,component:l,fallback:r,...u})=>{const c=$t(l)==="rendering"||!a.loaded,d={...c&&r&&{style:{display:"none"}}};return q.createElement(q.Fragment,null,c&&r,a.loaded&&q.createElement(Tt,{component:l,mount:a.mountSignUp,unmount:a.unmountSignUp,updateProps:a.__unstable__updateProps,props:u,rootProps:d}))},{component:"SignUp",renderWhileLoading:!0});function Fl({children:a}){return vt(dS),q.createElement(q.Fragment,null,a)}function er({children:a}){return vt(hS),q.createElement(q.Fragment,null,a)}var oE=Pe(({clerk:a,component:l,fallback:r,...u})=>{const c=$t(l)==="rendering"||!a.loaded,d={...c&&r&&{style:{display:"none"}}},{customPages:p,customPagesPortals:h}=bv(u.children);return q.createElement(q.Fragment,null,c&&r,q.createElement(Tt,{component:l,mount:a.mountUserProfile,unmount:a.unmountUserProfile,updateProps:a.__unstable__updateProps,props:{...u,customPages:p},rootProps:d},q.createElement(Qs,{customPagesPortals:h})))},{component:"UserProfile",renderWhileLoading:!0});Object.assign(oE,{Page:Fl,Link:er});var _v=A.createContext({mount:()=>{},unmount:()=>{},updateProps:()=>{}}),cE=Pe(({clerk:a,component:l,fallback:r,...u})=>{const c=$t(l)==="rendering"||!a.loaded,d={...c&&r&&{style:{display:"none"}}},{customPages:p,customPagesPortals:h}=bv(u.children,{allowForAnyChildren:!!u.__experimental_asProvider}),m=Object.assign(u.userProfileProps||{},{customPages:p}),{customMenuItems:y,customMenuItemsPortals:S}=iE(u.children),b=Ev(u.children),x={mount:a.mountUserButton,unmount:a.unmountUserButton,updateProps:a.__unstable__updateProps,props:{...u,userProfileProps:m,customMenuItems:y}},_={customPagesPortals:h,customMenuItemsPortals:S};return q.createElement(_v.Provider,{value:x},c&&r,a.loaded&&q.createElement(Tt,{component:l,...x,hideRootHtmlElement:!!u.__experimental_asProvider,rootProps:d},u.__experimental_asProvider?b:null,q.createElement(Qs,{..._})))},{component:"UserButton",renderWhileLoading:!0});function Ys({children:a}){return vt(ES),q.createElement(q.Fragment,null,a)}function wv({children:a}){return vt(xS),q.createElement(q.Fragment,null,a)}function Rv({children:a}){return vt(_S),q.createElement(q.Fragment,null,a)}function fE(a){const l=A.useContext(_v),r={...l,props:{...l.props,...a}};return q.createElement(Tt,{...r})}var dE=Object.assign(cE,{UserProfilePage:Fl,UserProfileLink:er,MenuItems:Ys,Action:wv,Link:Rv,__experimental_Outlet:fE});function Gs({children:a}){return vt(mS),q.createElement(q.Fragment,null,a)}function Xs({children:a}){return vt(gS),q.createElement(q.Fragment,null,a)}var hE=Pe(({clerk:a,component:l,fallback:r,...u})=>{const c=$t(l)==="rendering"||!a.loaded,d={...c&&r&&{style:{display:"none"}}},{customPages:p,customPagesPortals:h}=Sv(u.children);return q.createElement(q.Fragment,null,c&&r,a.loaded&&q.createElement(Tt,{component:l,mount:a.mountOrganizationProfile,unmount:a.unmountOrganizationProfile,updateProps:a.__unstable__updateProps,props:{...u,customPages:p},rootProps:d},q.createElement(Qs,{customPagesPortals:h})))},{component:"OrganizationProfile",renderWhileLoading:!0});Object.assign(hE,{Page:Gs,Link:Xs});Pe(({clerk:a,component:l,fallback:r,...u})=>{const c=$t(l)==="rendering"||!a.loaded,d={...c&&r&&{style:{display:"none"}}};return q.createElement(q.Fragment,null,c&&r,a.loaded&&q.createElement(Tt,{component:l,mount:a.mountCreateOrganization,unmount:a.unmountCreateOrganization,updateProps:a.__unstable__updateProps,props:u,rootProps:d}))},{component:"CreateOrganization",renderWhileLoading:!0});var Tv=A.createContext({mount:()=>{},unmount:()=>{},updateProps:()=>{}}),mE=Pe(({clerk:a,component:l,fallback:r,...u})=>{const c=$t(l)==="rendering"||!a.loaded,d={...c&&r&&{style:{display:"none"}}},{customPages:p,customPagesPortals:h}=Sv(u.children,{allowForAnyChildren:!!u.__experimental_asProvider}),m=Object.assign(u.organizationProfileProps||{},{customPages:p}),y=Ev(u.children),S={mount:a.mountOrganizationSwitcher,unmount:a.unmountOrganizationSwitcher,updateProps:a.__unstable__updateProps,props:{...u,organizationProfileProps:m},rootProps:d,component:l};return a.__experimental_prefetchOrganizationSwitcher(),q.createElement(Tv.Provider,{value:S},q.createElement(q.Fragment,null,c&&r,a.loaded&&q.createElement(Tt,{...S,hideRootHtmlElement:!!u.__experimental_asProvider},u.__experimental_asProvider?y:null,q.createElement(Qs,{customPagesPortals:h}))))},{component:"OrganizationSwitcher",renderWhileLoading:!0});function gE(a){const l=A.useContext(Tv),r={...l,props:{...l.props,...a}};return q.createElement(Tt,{...r})}Object.assign(mE,{OrganizationProfilePage:Gs,OrganizationProfileLink:Xs,__experimental_Outlet:gE});Pe(({clerk:a,component:l,fallback:r,...u})=>{const c=$t(l)==="rendering"||!a.loaded,d={...c&&r&&{style:{display:"none"}}};return q.createElement(q.Fragment,null,c&&r,a.loaded&&q.createElement(Tt,{component:l,mount:a.mountOrganizationList,unmount:a.unmountOrganizationList,updateProps:a.__unstable__updateProps,props:u,rootProps:d}))},{component:"OrganizationList",renderWhileLoading:!0});Pe(({clerk:a,component:l,fallback:r,...u})=>{const c=$t(l)==="rendering"||!a.loaded,d={...c&&r&&{style:{display:"none"}}};return q.createElement(q.Fragment,null,c&&r,a.loaded&&q.createElement(Tt,{component:l,open:a.openGoogleOneTap,close:a.closeGoogleOneTap,updateProps:a.__unstable__updateProps,props:u,rootProps:d}))},{component:"GoogleOneTap",renderWhileLoading:!0});Pe(({clerk:a,component:l,fallback:r,...u})=>{const c=$t(l)==="rendering"||!a.loaded,d={...c&&r&&{style:{display:"none"}}};return q.createElement(q.Fragment,null,c&&r,a.loaded&&q.createElement(Tt,{component:l,mount:a.mountWaitlist,unmount:a.unmountWaitlist,updateProps:a.__unstable__updateProps,props:u,rootProps:d}))},{component:"Waitlist",renderWhileLoading:!0});Pe(({clerk:a,component:l,fallback:r,...u})=>{const c=$t(l)==="rendering"||!a.loaded,d={...c&&r&&{style:{display:"none"}}};return q.createElement(q.Fragment,null,c&&r,a.loaded&&q.createElement(Tt,{component:l,mount:a.mountPricingTable,unmount:a.unmountPricingTable,updateProps:a.__unstable__updateProps,props:u,rootProps:d}))},{component:"PricingTable",renderWhileLoading:!0});Pe(({clerk:a,component:l,fallback:r,...u})=>{const c=$t(l)==="rendering"||!a.loaded,d={...c&&r&&{style:{display:"none"}}};return q.createElement(q.Fragment,null,c&&r,a.loaded&&q.createElement(Tt,{component:l,mount:a.mountApiKeys,unmount:a.unmountApiKeys,updateProps:a.__unstable__updateProps,props:u,rootProps:d}))},{component:"ApiKeys",renderWhileLoading:!0});Pe(({clerk:a,children:l,...r})=>{const{signUpFallbackRedirectUrl:u,forceRedirectUrl:o,fallbackRedirectUrl:c,signUpForceRedirectUrl:d,mode:p,initialValues:h,withSignUp:m,oauthFlow:y,...S}=r;l=Vs(l,"Sign in");const b=Hs(l)("SignInButton"),x=()=>{const L={forceRedirectUrl:o,fallbackRedirectUrl:c,signUpFallbackRedirectUrl:u,signUpForceRedirectUrl:d,initialValues:h,withSignUp:m,oauthFlow:y};return p==="modal"?a.openSignIn({...L,appearance:r.appearance}):a.redirectToSignIn({...L,signInFallbackRedirectUrl:c,signInForceRedirectUrl:o})},M={...S,onClick:async L=>(b&&typeof b=="object"&&"props"in b&&await Ps(b.props.onClick)(L),x())};return q.cloneElement(b,M)},{component:"SignInButton",renderWhileLoading:!0});Pe(({clerk:a,children:l,...r})=>{const{fallbackRedirectUrl:u,forceRedirectUrl:o,signInFallbackRedirectUrl:c,signInForceRedirectUrl:d,mode:p,unsafeMetadata:h,initialValues:m,oauthFlow:y,...S}=r;l=Vs(l,"Sign up");const b=Hs(l)("SignUpButton"),x=()=>{const L={fallbackRedirectUrl:u,forceRedirectUrl:o,signInFallbackRedirectUrl:c,signInForceRedirectUrl:d,unsafeMetadata:h,initialValues:m,oauthFlow:y};return p==="modal"?a.openSignUp({...L,appearance:r.appearance}):a.redirectToSignUp({...L,signUpFallbackRedirectUrl:u,signUpForceRedirectUrl:o})},M={...S,onClick:async L=>(b&&typeof b=="object"&&"props"in b&&await Ps(b.props.onClick)(L),x())};return q.cloneElement(b,M)},{component:"SignUpButton",renderWhileLoading:!0});Pe(({clerk:a,children:l,...r})=>{const{redirectUrl:u="/",signOutOptions:o,...c}=r;l=Vs(l,"Sign out");const d=Hs(l)("SignOutButton"),p=()=>a.signOut({redirectUrl:u,...o}),m={...c,onClick:async y=>(await Ps(d.props.onClick)(y),p())};return q.cloneElement(d,m)},{component:"SignOutButton",renderWhileLoading:!0});Pe(({clerk:a,children:l,...r})=>{const{redirectUrl:u,...o}=r;l=Vs(l,"Sign in with Metamask");const c=Hs(l)("SignInWithMetamaskButton"),d=async()=>{async function m(){await a.authenticateWithMetamask({redirectUrl:u||void 0})}m()},h={...o,onClick:async m=>(await Ps(c.props.onClick)(m),d())};return q.cloneElement(c,h)},{component:"SignInWithMetamask",renderWhileLoading:!0});typeof globalThis.__BUILD_DISABLE_RHC__>"u"&&(globalThis.__BUILD_DISABLE_RHC__=!1);var pE={name:"@clerk/clerk-react",version:"5.32.1",environment:"production"},js,Ti,Ci,ua,On,ca,ks,hf,Cv=class Av{constructor(l){Na(this,ks),this.clerkjs=null,this.preopenOneTap=null,this.preopenUserVerification=null,this.preopenSignIn=null,this.preopenCheckout=null,this.preopenPlanDetails=null,this.preopenSignUp=null,this.preopenUserProfile=null,this.preopenOrganizationProfile=null,this.preopenCreateOrganization=null,this.preOpenWaitlist=null,this.premountSignInNodes=new Map,this.premountSignUpNodes=new Map,this.premountUserProfileNodes=new Map,this.premountUserButtonNodes=new Map,this.premountOrganizationProfileNodes=new Map,this.premountCreateOrganizationNodes=new Map,this.premountOrganizationSwitcherNodes=new Map,this.premountOrganizationListNodes=new Map,this.premountMethodCalls=new Map,this.premountWaitlistNodes=new Map,this.premountPricingTableNodes=new Map,this.premountApiKeysNodes=new Map,this.premountOAuthConsentNodes=new Map,this.premountAddListenerCalls=new Map,this.loadedListeners=[],Na(this,js,"loading"),Na(this,Ti),Na(this,Ci),Na(this,ua),Na(this,On,eE()),this.buildSignInUrl=o=>{const c=()=>{var d;return((d=this.clerkjs)==null?void 0:d.buildSignInUrl(o))||""};if(this.clerkjs&&this.loaded)return c();this.premountMethodCalls.set("buildSignInUrl",c)},this.buildSignUpUrl=o=>{const c=()=>{var d;return((d=this.clerkjs)==null?void 0:d.buildSignUpUrl(o))||""};if(this.clerkjs&&this.loaded)return c();this.premountMethodCalls.set("buildSignUpUrl",c)},this.buildAfterSignInUrl=(...o)=>{const c=()=>{var d;return((d=this.clerkjs)==null?void 0:d.buildAfterSignInUrl(...o))||""};if(this.clerkjs&&this.loaded)return c();this.premountMethodCalls.set("buildAfterSignInUrl",c)},this.buildAfterSignUpUrl=(...o)=>{const c=()=>{var d;return((d=this.clerkjs)==null?void 0:d.buildAfterSignUpUrl(...o))||""};if(this.clerkjs&&this.loaded)return c();this.premountMethodCalls.set("buildAfterSignUpUrl",c)},this.buildAfterSignOutUrl=()=>{const o=()=>{var c;return((c=this.clerkjs)==null?void 0:c.buildAfterSignOutUrl())||""};if(this.clerkjs&&this.loaded)return o();this.premountMethodCalls.set("buildAfterSignOutUrl",o)},this.buildNewSubscriptionRedirectUrl=()=>{const o=()=>{var c;return((c=this.clerkjs)==null?void 0:c.buildNewSubscriptionRedirectUrl())||""};if(this.clerkjs&&this.loaded)return o();this.premountMethodCalls.set("buildNewSubscriptionRedirectUrl",o)},this.buildAfterMultiSessionSingleSignOutUrl=()=>{const o=()=>{var c;return((c=this.clerkjs)==null?void 0:c.buildAfterMultiSessionSingleSignOutUrl())||""};if(this.clerkjs&&this.loaded)return o();this.premountMethodCalls.set("buildAfterMultiSessionSingleSignOutUrl",o)},this.buildUserProfileUrl=()=>{const o=()=>{var c;return((c=this.clerkjs)==null?void 0:c.buildUserProfileUrl())||""};if(this.clerkjs&&this.loaded)return o();this.premountMethodCalls.set("buildUserProfileUrl",o)},this.buildCreateOrganizationUrl=()=>{const o=()=>{var c;return((c=this.clerkjs)==null?void 0:c.buildCreateOrganizationUrl())||""};if(this.clerkjs&&this.loaded)return o();this.premountMethodCalls.set("buildCreateOrganizationUrl",o)},this.buildOrganizationProfileUrl=()=>{const o=()=>{var c;return((c=this.clerkjs)==null?void 0:c.buildOrganizationProfileUrl())||""};if(this.clerkjs&&this.loaded)return o();this.premountMethodCalls.set("buildOrganizationProfileUrl",o)},this.buildWaitlistUrl=()=>{const o=()=>{var c;return((c=this.clerkjs)==null?void 0:c.buildWaitlistUrl())||""};if(this.clerkjs&&this.loaded)return o();this.premountMethodCalls.set("buildWaitlistUrl",o)},this.buildUrlWithAuth=o=>{const c=()=>{var d;return((d=this.clerkjs)==null?void 0:d.buildUrlWithAuth(o))||""};if(this.clerkjs&&this.loaded)return c();this.premountMethodCalls.set("buildUrlWithAuth",c)},this.handleUnauthenticated=async()=>{const o=()=>{var c;return(c=this.clerkjs)==null?void 0:c.handleUnauthenticated()};this.clerkjs&&this.loaded?o():this.premountMethodCalls.set("handleUnauthenticated",o)},this.on=(...o)=>{var c;if((c=this.clerkjs)!=null&&c.on)return this.clerkjs.on(...o);qe(this,On).on(...o)},this.off=(...o)=>{var c;if((c=this.clerkjs)!=null&&c.off)return this.clerkjs.off(...o);qe(this,On).off(...o)},this.addOnLoaded=o=>{this.loadedListeners.push(o),this.loaded&&this.emitLoaded()},this.emitLoaded=()=>{this.loadedListeners.forEach(o=>o()),this.loadedListeners=[]},this.beforeLoad=o=>{if(!o)throw new Error("Failed to hydrate latest Clerk JS")},this.hydrateClerkJS=o=>{var c;if(!o)throw new Error("Failed to hydrate latest Clerk JS");return this.clerkjs=o,this.premountMethodCalls.forEach(d=>d()),this.premountAddListenerCalls.forEach((d,p)=>{d.nativeUnsubscribe=o.addListener(p)}),(c=qe(this,On).internal.retrieveListeners("status"))==null||c.forEach(d=>{this.on("status",d,{notify:!0})}),this.preopenSignIn!==null&&o.openSignIn(this.preopenSignIn),this.preopenCheckout!==null&&o.__internal_openCheckout(this.preopenCheckout),this.preopenPlanDetails!==null&&o.__internal_openPlanDetails(this.preopenPlanDetails),this.preopenSignUp!==null&&o.openSignUp(this.preopenSignUp),this.preopenUserProfile!==null&&o.openUserProfile(this.preopenUserProfile),this.preopenUserVerification!==null&&o.__internal_openReverification(this.preopenUserVerification),this.preopenOneTap!==null&&o.openGoogleOneTap(this.preopenOneTap),this.preopenOrganizationProfile!==null&&o.openOrganizationProfile(this.preopenOrganizationProfile),this.preopenCreateOrganization!==null&&o.openCreateOrganization(this.preopenCreateOrganization),this.preOpenWaitlist!==null&&o.openWaitlist(this.preOpenWaitlist),this.premountSignInNodes.forEach((d,p)=>{o.mountSignIn(p,d)}),this.premountSignUpNodes.forEach((d,p)=>{o.mountSignUp(p,d)}),this.premountUserProfileNodes.forEach((d,p)=>{o.mountUserProfile(p,d)}),this.premountUserButtonNodes.forEach((d,p)=>{o.mountUserButton(p,d)}),this.premountOrganizationListNodes.forEach((d,p)=>{o.mountOrganizationList(p,d)}),this.premountWaitlistNodes.forEach((d,p)=>{o.mountWaitlist(p,d)}),this.premountPricingTableNodes.forEach((d,p)=>{o.mountPricingTable(p,d)}),this.premountApiKeysNodes.forEach((d,p)=>{o.mountApiKeys(p,d)}),this.premountOAuthConsentNodes.forEach((d,p)=>{o.__internal_mountOAuthConsent(p,d)}),typeof this.clerkjs.status>"u"&&qe(this,On).emit(_s.Status,"ready"),this.emitLoaded(),this.clerkjs},this.__unstable__updateProps=async o=>{const c=await Wg(this,ks,hf).call(this);if(c&&"__unstable__updateProps"in c)return c.__unstable__updateProps(o)},this.__experimental_navigateToTask=async o=>this.clerkjs?this.clerkjs.__experimental_navigateToTask(o):Promise.reject(),this.setActive=o=>this.clerkjs?this.clerkjs.setActive(o):Promise.reject(),this.openSignIn=o=>{this.clerkjs&&this.loaded?this.clerkjs.openSignIn(o):this.preopenSignIn=o},this.closeSignIn=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeSignIn():this.preopenSignIn=null},this.__internal_openCheckout=o=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openCheckout(o):this.preopenCheckout=o},this.__internal_closeCheckout=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closeCheckout():this.preopenCheckout=null},this.__internal_openPlanDetails=o=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openPlanDetails(o):this.preopenPlanDetails=o},this.__internal_closePlanDetails=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closePlanDetails():this.preopenPlanDetails=null},this.__internal_openReverification=o=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openReverification(o):this.preopenUserVerification=o},this.__internal_closeReverification=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closeReverification():this.preopenUserVerification=null},this.openGoogleOneTap=o=>{this.clerkjs&&this.loaded?this.clerkjs.openGoogleOneTap(o):this.preopenOneTap=o},this.closeGoogleOneTap=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeGoogleOneTap():this.preopenOneTap=null},this.openUserProfile=o=>{this.clerkjs&&this.loaded?this.clerkjs.openUserProfile(o):this.preopenUserProfile=o},this.closeUserProfile=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeUserProfile():this.preopenUserProfile=null},this.openOrganizationProfile=o=>{this.clerkjs&&this.loaded?this.clerkjs.openOrganizationProfile(o):this.preopenOrganizationProfile=o},this.closeOrganizationProfile=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeOrganizationProfile():this.preopenOrganizationProfile=null},this.openCreateOrganization=o=>{this.clerkjs&&this.loaded?this.clerkjs.openCreateOrganization(o):this.preopenCreateOrganization=o},this.closeCreateOrganization=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeCreateOrganization():this.preopenCreateOrganization=null},this.openWaitlist=o=>{this.clerkjs&&this.loaded?this.clerkjs.openWaitlist(o):this.preOpenWaitlist=o},this.closeWaitlist=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeWaitlist():this.preOpenWaitlist=null},this.openSignUp=o=>{this.clerkjs&&this.loaded?this.clerkjs.openSignUp(o):this.preopenSignUp=o},this.closeSignUp=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeSignUp():this.preopenSignUp=null},this.mountSignIn=(o,c)=>{this.clerkjs&&this.loaded?this.clerkjs.mountSignIn(o,c):this.premountSignInNodes.set(o,c)},this.unmountSignIn=o=>{this.clerkjs&&this.loaded?this.clerkjs.unmountSignIn(o):this.premountSignInNodes.delete(o)},this.mountSignUp=(o,c)=>{this.clerkjs&&this.loaded?this.clerkjs.mountSignUp(o,c):this.premountSignUpNodes.set(o,c)},this.unmountSignUp=o=>{this.clerkjs&&this.loaded?this.clerkjs.unmountSignUp(o):this.premountSignUpNodes.delete(o)},this.mountUserProfile=(o,c)=>{this.clerkjs&&this.loaded?this.clerkjs.mountUserProfile(o,c):this.premountUserProfileNodes.set(o,c)},this.unmountUserProfile=o=>{this.clerkjs&&this.loaded?this.clerkjs.unmountUserProfile(o):this.premountUserProfileNodes.delete(o)},this.mountOrganizationProfile=(o,c)=>{this.clerkjs&&this.loaded?this.clerkjs.mountOrganizationProfile(o,c):this.premountOrganizationProfileNodes.set(o,c)},this.unmountOrganizationProfile=o=>{this.clerkjs&&this.loaded?this.clerkjs.unmountOrganizationProfile(o):this.premountOrganizationProfileNodes.delete(o)},this.mountCreateOrganization=(o,c)=>{this.clerkjs&&this.loaded?this.clerkjs.mountCreateOrganization(o,c):this.premountCreateOrganizationNodes.set(o,c)},this.unmountCreateOrganization=o=>{this.clerkjs&&this.loaded?this.clerkjs.unmountCreateOrganization(o):this.premountCreateOrganizationNodes.delete(o)},this.mountOrganizationSwitcher=(o,c)=>{this.clerkjs&&this.loaded?this.clerkjs.mountOrganizationSwitcher(o,c):this.premountOrganizationSwitcherNodes.set(o,c)},this.unmountOrganizationSwitcher=o=>{this.clerkjs&&this.loaded?this.clerkjs.unmountOrganizationSwitcher(o):this.premountOrganizationSwitcherNodes.delete(o)},this.__experimental_prefetchOrganizationSwitcher=()=>{const o=()=>{var c;return(c=this.clerkjs)==null?void 0:c.__experimental_prefetchOrganizationSwitcher()};this.clerkjs&&this.loaded?o():this.premountMethodCalls.set("__experimental_prefetchOrganizationSwitcher",o)},this.mountOrganizationList=(o,c)=>{this.clerkjs&&this.loaded?this.clerkjs.mountOrganizationList(o,c):this.premountOrganizationListNodes.set(o,c)},this.unmountOrganizationList=o=>{this.clerkjs&&this.loaded?this.clerkjs.unmountOrganizationList(o):this.premountOrganizationListNodes.delete(o)},this.mountUserButton=(o,c)=>{this.clerkjs&&this.loaded?this.clerkjs.mountUserButton(o,c):this.premountUserButtonNodes.set(o,c)},this.unmountUserButton=o=>{this.clerkjs&&this.loaded?this.clerkjs.unmountUserButton(o):this.premountUserButtonNodes.delete(o)},this.mountWaitlist=(o,c)=>{this.clerkjs&&this.loaded?this.clerkjs.mountWaitlist(o,c):this.premountWaitlistNodes.set(o,c)},this.unmountWaitlist=o=>{this.clerkjs&&this.loaded?this.clerkjs.unmountWaitlist(o):this.premountWaitlistNodes.delete(o)},this.mountPricingTable=(o,c)=>{this.clerkjs&&this.loaded?this.clerkjs.mountPricingTable(o,c):this.premountPricingTableNodes.set(o,c)},this.unmountPricingTable=o=>{this.clerkjs&&this.loaded?this.clerkjs.unmountPricingTable(o):this.premountPricingTableNodes.delete(o)},this.mountApiKeys=(o,c)=>{this.clerkjs&&this.loaded?this.clerkjs.mountApiKeys(o,c):this.premountApiKeysNodes.set(o,c)},this.unmountApiKeys=o=>{this.clerkjs&&this.loaded?this.clerkjs.unmountApiKeys(o):this.premountApiKeysNodes.delete(o)},this.__internal_mountOAuthConsent=(o,c)=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_mountOAuthConsent(o,c):this.premountOAuthConsentNodes.set(o,c)},this.__internal_unmountOAuthConsent=o=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_unmountOAuthConsent(o):this.premountOAuthConsentNodes.delete(o)},this.addListener=o=>{if(this.clerkjs)return this.clerkjs.addListener(o);{const c=()=>{var d;const p=this.premountAddListenerCalls.get(o);p&&((d=p.nativeUnsubscribe)==null||d.call(p),this.premountAddListenerCalls.delete(o))};return this.premountAddListenerCalls.set(o,{unsubscribe:c,nativeUnsubscribe:void 0}),c}},this.navigate=o=>{const c=()=>{var d;return(d=this.clerkjs)==null?void 0:d.navigate(o)};this.clerkjs&&this.loaded?c():this.premountMethodCalls.set("navigate",c)},this.redirectWithAuth=async(...o)=>{const c=()=>{var d;return(d=this.clerkjs)==null?void 0:d.redirectWithAuth(...o)};if(this.clerkjs&&this.loaded)return c();this.premountMethodCalls.set("redirectWithAuth",c)},this.redirectToSignIn=async o=>{const c=()=>{var d;return(d=this.clerkjs)==null?void 0:d.redirectToSignIn(o)};if(this.clerkjs&&this.loaded)return c();this.premountMethodCalls.set("redirectToSignIn",c)},this.redirectToSignUp=async o=>{const c=()=>{var d;return(d=this.clerkjs)==null?void 0:d.redirectToSignUp(o)};if(this.clerkjs&&this.loaded)return c();this.premountMethodCalls.set("redirectToSignUp",c)},this.redirectToUserProfile=async()=>{const o=()=>{var c;return(c=this.clerkjs)==null?void 0:c.redirectToUserProfile()};if(this.clerkjs&&this.loaded)return o();this.premountMethodCalls.set("redirectToUserProfile",o)},this.redirectToAfterSignUp=()=>{const o=()=>{var c;return(c=this.clerkjs)==null?void 0:c.redirectToAfterSignUp()};if(this.clerkjs&&this.loaded)return o();this.premountMethodCalls.set("redirectToAfterSignUp",o)},this.redirectToAfterSignIn=()=>{const o=()=>{var c;return(c=this.clerkjs)==null?void 0:c.redirectToAfterSignIn()};this.clerkjs&&this.loaded?o():this.premountMethodCalls.set("redirectToAfterSignIn",o)},this.redirectToAfterSignOut=()=>{const o=()=>{var c;return(c=this.clerkjs)==null?void 0:c.redirectToAfterSignOut()};this.clerkjs&&this.loaded?o():this.premountMethodCalls.set("redirectToAfterSignOut",o)},this.redirectToOrganizationProfile=async()=>{const o=()=>{var c;return(c=this.clerkjs)==null?void 0:c.redirectToOrganizationProfile()};if(this.clerkjs&&this.loaded)return o();this.premountMethodCalls.set("redirectToOrganizationProfile",o)},this.redirectToCreateOrganization=async()=>{const o=()=>{var c;return(c=this.clerkjs)==null?void 0:c.redirectToCreateOrganization()};if(this.clerkjs&&this.loaded)return o();this.premountMethodCalls.set("redirectToCreateOrganization",o)},this.redirectToWaitlist=async()=>{const o=()=>{var c;return(c=this.clerkjs)==null?void 0:c.redirectToWaitlist()};if(this.clerkjs&&this.loaded)return o();this.premountMethodCalls.set("redirectToWaitlist",o)},this.handleRedirectCallback=async o=>{var c;const d=()=>{var p;return(p=this.clerkjs)==null?void 0:p.handleRedirectCallback(o)};this.clerkjs&&this.loaded?(c=d())==null||c.catch(()=>{}):this.premountMethodCalls.set("handleRedirectCallback",d)},this.handleGoogleOneTapCallback=async(o,c)=>{var d;const p=()=>{var h;return(h=this.clerkjs)==null?void 0:h.handleGoogleOneTapCallback(o,c)};this.clerkjs&&this.loaded?(d=p())==null||d.catch(()=>{}):this.premountMethodCalls.set("handleGoogleOneTapCallback",p)},this.handleEmailLinkVerification=async o=>{const c=()=>{var d;return(d=this.clerkjs)==null?void 0:d.handleEmailLinkVerification(o)};if(this.clerkjs&&this.loaded)return c();this.premountMethodCalls.set("handleEmailLinkVerification",c)},this.authenticateWithMetamask=async o=>{const c=()=>{var d;return(d=this.clerkjs)==null?void 0:d.authenticateWithMetamask(o)};if(this.clerkjs&&this.loaded)return c();this.premountMethodCalls.set("authenticateWithMetamask",c)},this.authenticateWithCoinbaseWallet=async o=>{const c=()=>{var d;return(d=this.clerkjs)==null?void 0:d.authenticateWithCoinbaseWallet(o)};if(this.clerkjs&&this.loaded)return c();this.premountMethodCalls.set("authenticateWithCoinbaseWallet",c)},this.authenticateWithOKXWallet=async o=>{const c=()=>{var d;return(d=this.clerkjs)==null?void 0:d.authenticateWithOKXWallet(o)};if(this.clerkjs&&this.loaded)return c();this.premountMethodCalls.set("authenticateWithOKXWallet",c)},this.authenticateWithWeb3=async o=>{const c=()=>{var d;return(d=this.clerkjs)==null?void 0:d.authenticateWithWeb3(o)};if(this.clerkjs&&this.loaded)return c();this.premountMethodCalls.set("authenticateWithWeb3",c)},this.authenticateWithGoogleOneTap=async o=>(await Wg(this,ks,hf).call(this)).authenticateWithGoogleOneTap(o),this.createOrganization=async o=>{const c=()=>{var d;return(d=this.clerkjs)==null?void 0:d.createOrganization(o)};if(this.clerkjs&&this.loaded)return c();this.premountMethodCalls.set("createOrganization",c)},this.getOrganization=async o=>{const c=()=>{var d;return(d=this.clerkjs)==null?void 0:d.getOrganization(o)};if(this.clerkjs&&this.loaded)return c();this.premountMethodCalls.set("getOrganization",c)},this.joinWaitlist=async o=>{const c=()=>{var d;return(d=this.clerkjs)==null?void 0:d.joinWaitlist(o)};if(this.clerkjs&&this.loaded)return c();this.premountMethodCalls.set("joinWaitlist",c)},this.signOut=async(...o)=>{const c=()=>{var d;return(d=this.clerkjs)==null?void 0:d.signOut(...o)};if(this.clerkjs&&this.loaded)return c();this.premountMethodCalls.set("signOut",c)};const{Clerk:r=null,publishableKey:u}=l||{};_i(this,ua,u),_i(this,Ci,l?.proxyUrl),_i(this,Ti,l?.domain),this.options=l,this.Clerk=r,this.mode=ep()?"browser":"server",this.options.sdkMetadata||(this.options.sdkMetadata=pE),qe(this,On).emit(_s.Status,"loading"),qe(this,On).prioritizedOn(_s.Status,o=>_i(this,js,o)),qe(this,ua)&&this.loadClerkJS()}get publishableKey(){return qe(this,ua)}get loaded(){var l;return((l=this.clerkjs)==null?void 0:l.loaded)||!1}get status(){var l;return this.clerkjs?((l=this.clerkjs)==null?void 0:l.status)||(this.clerkjs.loaded?"ready":"loading"):qe(this,js)}static getOrCreateInstance(l){return(!ep()||!qe(this,ca)||l.Clerk&&qe(this,ca).Clerk!==l.Clerk||qe(this,ca).publishableKey!==l.publishableKey)&&_i(this,ca,new Av(l)),qe(this,ca)}static clearInstance(){_i(this,ca,null)}get domain(){return typeof window<"u"&&window.location?Tc(qe(this,Ti),new URL(window.location.href),""):typeof qe(this,Ti)=="function"?dn.throw(Rc):qe(this,Ti)||""}get proxyUrl(){return typeof window<"u"&&window.location?Tc(qe(this,Ci),new URL(window.location.href),""):typeof qe(this,Ci)=="function"?dn.throw(Rc):qe(this,Ci)||""}__internal_getOption(l){var r,u;return(r=this.clerkjs)!=null&&r.__internal_getOption?(u=this.clerkjs)==null?void 0:u.__internal_getOption(l):this.options[l]}get sdkMetadata(){var l;return((l=this.clerkjs)==null?void 0:l.sdkMetadata)||this.options.sdkMetadata||void 0}get instanceType(){var l;return(l=this.clerkjs)==null?void 0:l.instanceType}get frontendApi(){var l;return((l=this.clerkjs)==null?void 0:l.frontendApi)||""}get isStandardBrowser(){var l;return((l=this.clerkjs)==null?void 0:l.isStandardBrowser)||this.options.standardBrowser||!1}get isSatellite(){return typeof window<"u"&&window.location?Tc(this.options.isSatellite,new URL(window.location.href),!1):typeof this.options.isSatellite=="function"?dn.throw(Rc):!1}async loadClerkJS(){var l;if(!(this.mode!=="browser"||this.loaded)){typeof window<"u"&&(window.__clerk_publishable_key=qe(this,ua),window.__clerk_proxy_url=this.proxyUrl,window.__clerk_domain=this.domain);try{if(this.Clerk){let r;tE(this.Clerk)?(r=new this.Clerk(qe(this,ua),{proxyUrl:this.proxyUrl,domain:this.domain}),this.beforeLoad(r),await r.load(this.options)):(r=this.Clerk,r.loaded||(this.beforeLoad(r),await r.load(this.options))),global.Clerk=r}else if(!__BUILD_DISABLE_RHC__){if(global.Clerk||await GS({...this.options,publishableKey:qe(this,ua),proxyUrl:this.proxyUrl,domain:this.domain,nonce:this.options.nonce}),!global.Clerk)throw new Error("Failed to download latest ClerkJS. Contact <EMAIL>.");this.beforeLoad(global.Clerk),await global.Clerk.load(this.options)}return(l=global.Clerk)!=null&&l.loaded?this.hydrateClerkJS(global.Clerk):void 0}catch(r){const u=r;qe(this,On).emit(_s.Status,"error"),console.error(u.stack||u.message||u);return}}}get version(){var l;return(l=this.clerkjs)==null?void 0:l.version}get client(){if(this.clerkjs)return this.clerkjs.client}get session(){if(this.clerkjs)return this.clerkjs.session}get user(){if(this.clerkjs)return this.clerkjs.user}get organization(){if(this.clerkjs)return this.clerkjs.organization}get telemetry(){if(this.clerkjs)return this.clerkjs.telemetry}get __unstable__environment(){if(this.clerkjs)return this.clerkjs.__unstable__environment}get isSignedIn(){return this.clerkjs?this.clerkjs.isSignedIn:!1}get billing(){var l;return(l=this.clerkjs)==null?void 0:l.billing}get apiKeys(){var l;return(l=this.clerkjs)==null?void 0:l.apiKeys}__unstable__setEnvironment(...l){if(this.clerkjs&&"__unstable__setEnvironment"in this.clerkjs)this.clerkjs.__unstable__setEnvironment(l);else return}};js=new WeakMap;Ti=new WeakMap;Ci=new WeakMap;ua=new WeakMap;On=new WeakMap;ca=new WeakMap;ks=new WeakSet;hf=function(){return new Promise(a=>{this.addOnLoaded(()=>a(this.clerkjs))})};Na(Cv,ca);var up=Cv;function vE(a){const{isomorphicClerkOptions:l,initialState:r,children:u}=a,{isomorphicClerk:o,clerkStatus:c}=yE(l),[d,p]=q.useState({client:o.client,session:o.session,user:o.user,organization:o.organization});q.useEffect(()=>o.addListener(ge=>p({...ge})),[]);const h=ZS(o.loaded,d,r),m=q.useMemo(()=>({value:o}),[c]),y=q.useMemo(()=>({value:d.client}),[d.client]),{sessionId:S,sessionStatus:b,sessionClaims:x,session:_,userId:M,user:L,orgId:T,actor:P,organization:G,orgRole:J,orgSlug:Q,orgPermissions:F,factorVerificationAge:I}=h,$=q.useMemo(()=>({value:{sessionId:S,sessionStatus:b,sessionClaims:x,userId:M,actor:P,orgId:T,orgRole:J,orgSlug:Q,orgPermissions:F,factorVerificationAge:I}}),[S,b,M,P,T,J,Q,I,x?.__raw]),re=q.useMemo(()=>({value:_}),[S,_]),ne=q.useMemo(()=>({value:L}),[M,L]),pe=q.useMemo(()=>({value:{organization:G}}),[T,G]);return q.createElement(uS.Provider,{value:m},q.createElement(Jb.Provider,{value:y},q.createElement(Fb.Provider,{value:re},q.createElement(tS,{...pe.value},q.createElement(rS.Provider,{value:$},q.createElement(Zb.Provider,{value:ne},u))))))}var yE=a=>{const l=q.useRef(up.getOrCreateInstance(a)),[r,u]=q.useState(l.current.status);return q.useEffect(()=>{l.current.__unstable__updateProps({appearance:a.appearance})},[a.appearance]),q.useEffect(()=>{l.current.__unstable__updateProps({options:a})},[a.localization]),q.useEffect(()=>(l.current.on("status",u),()=>{l.current&&l.current.off("status",u),up.clearInstance()}),[]),{isomorphicClerk:l.current,clerkStatus:r}};function bE(a){const{initialState:l,children:r,__internal_bypassMissingPublishableKey:u,...o}=a,{publishableKey:c="",Clerk:d}=o;return!d&&!u&&(c?c&&!Fc(c)&&dn.throwInvalidPublishableKeyError({key:c}):dn.throwMissingPublishableKeyError()),q.createElement(vE,{initialState:l,isomorphicClerkOptions:o},r)}var Ov=aE(bE,"ClerkProvider",oS);Ov.displayName="ClerkProvider";lS({packageName:"@clerk/clerk-react"});YS("@clerk/clerk-react");var Ll={},op;function SE(){if(op)return Ll;op=1,Object.defineProperty(Ll,"__esModule",{value:!0}),Ll.parse=d,Ll.serialize=m;const a=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,l=/^[\u0021-\u003A\u003C-\u007E]*$/,r=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,u=/^[\u0020-\u003A\u003D-\u007E]*$/,o=Object.prototype.toString,c=(()=>{const b=function(){};return b.prototype=Object.create(null),b})();function d(b,x){const _=new c,M=b.length;if(M<2)return _;const L=x?.decode||y;let T=0;do{const P=b.indexOf("=",T);if(P===-1)break;const G=b.indexOf(";",T),J=G===-1?M:G;if(P>J){T=b.lastIndexOf(";",P-1)+1;continue}const Q=p(b,T,P),F=h(b,P,Q),I=b.slice(Q,F);if(_[I]===void 0){let $=p(b,P+1,J),re=h(b,J,$);const ne=L(b.slice($,re));_[I]=ne}T=J+1}while(T<M);return _}function p(b,x,_){do{const M=b.charCodeAt(x);if(M!==32&&M!==9)return x}while(++x<_);return _}function h(b,x,_){for(;x>_;){const M=b.charCodeAt(--x);if(M!==32&&M!==9)return x+1}return _}function m(b,x,_){const M=_?.encode||encodeURIComponent;if(!a.test(b))throw new TypeError(`argument name is invalid: ${b}`);const L=M(x);if(!l.test(L))throw new TypeError(`argument val is invalid: ${x}`);let T=b+"="+L;if(!_)return T;if(_.maxAge!==void 0){if(!Number.isInteger(_.maxAge))throw new TypeError(`option maxAge is invalid: ${_.maxAge}`);T+="; Max-Age="+_.maxAge}if(_.domain){if(!r.test(_.domain))throw new TypeError(`option domain is invalid: ${_.domain}`);T+="; Domain="+_.domain}if(_.path){if(!u.test(_.path))throw new TypeError(`option path is invalid: ${_.path}`);T+="; Path="+_.path}if(_.expires){if(!S(_.expires)||!Number.isFinite(_.expires.valueOf()))throw new TypeError(`option expires is invalid: ${_.expires}`);T+="; Expires="+_.expires.toUTCString()}if(_.httpOnly&&(T+="; HttpOnly"),_.secure&&(T+="; Secure"),_.partitioned&&(T+="; Partitioned"),_.priority)switch(typeof _.priority=="string"?_.priority.toLowerCase():void 0){case"low":T+="; Priority=Low";break;case"medium":T+="; Priority=Medium";break;case"high":T+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${_.priority}`)}if(_.sameSite)switch(typeof _.sameSite=="string"?_.sameSite.toLowerCase():_.sameSite){case!0:case"strict":T+="; SameSite=Strict";break;case"lax":T+="; SameSite=Lax";break;case"none":T+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${_.sameSite}`)}return T}function y(b){if(b.indexOf("%")===-1)return b;try{return decodeURIComponent(b)}catch{return b}}function S(b){return o.call(b)==="[object Date]"}return Ll}SE();var cp="popstate";function EE(a={}){function l(u,o){let{pathname:c,search:d,hash:p}=u.location;return mf("",{pathname:c,search:d,hash:p},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function r(u,o){return typeof o=="string"?o:Gl(o)}return _E(l,r,null,a)}function De(a,l){if(a===!1||a===null||typeof a>"u")throw new Error(l)}function Ft(a,l){if(!a){typeof console<"u"&&console.warn(l);try{throw new Error(l)}catch{}}}function xE(){return Math.random().toString(36).substring(2,10)}function fp(a,l){return{usr:a.state,key:a.key,idx:l}}function mf(a,l,r=null,u){return{pathname:typeof a=="string"?a:a.pathname,search:"",hash:"",...typeof l=="string"?Ni(l):l,state:r,key:l&&l.key||u||xE()}}function Gl({pathname:a="/",search:l="",hash:r=""}){return l&&l!=="?"&&(a+=l.charAt(0)==="?"?l:"?"+l),r&&r!=="#"&&(a+=r.charAt(0)==="#"?r:"#"+r),a}function Ni(a){let l={};if(a){let r=a.indexOf("#");r>=0&&(l.hash=a.substring(r),a=a.substring(0,r));let u=a.indexOf("?");u>=0&&(l.search=a.substring(u),a=a.substring(0,u)),a&&(l.pathname=a)}return l}function _E(a,l,r,u={}){let{window:o=document.defaultView,v5Compat:c=!1}=u,d=o.history,p="POP",h=null,m=y();m==null&&(m=0,d.replaceState({...d.state,idx:m},""));function y(){return(d.state||{idx:null}).idx}function S(){p="POP";let L=y(),T=L==null?null:L-m;m=L,h&&h({action:p,location:M.location,delta:T})}function b(L,T){p="PUSH";let P=mf(M.location,L,T);m=y()+1;let G=fp(P,m),J=M.createHref(P);try{d.pushState(G,"",J)}catch(Q){if(Q instanceof DOMException&&Q.name==="DataCloneError")throw Q;o.location.assign(J)}c&&h&&h({action:p,location:M.location,delta:1})}function x(L,T){p="REPLACE";let P=mf(M.location,L,T);m=y();let G=fp(P,m),J=M.createHref(P);d.replaceState(G,"",J),c&&h&&h({action:p,location:M.location,delta:0})}function _(L){return wE(L)}let M={get action(){return p},get location(){return a(o,d)},listen(L){if(h)throw new Error("A history only accepts one active listener");return o.addEventListener(cp,S),h=L,()=>{o.removeEventListener(cp,S),h=null}},createHref(L){return l(o,L)},createURL:_,encodeLocation(L){let T=_(L);return{pathname:T.pathname,search:T.search,hash:T.hash}},push:b,replace:x,go(L){return d.go(L)}};return M}function wE(a,l=!1){let r="http://localhost";typeof window<"u"&&(r=window.location.origin!=="null"?window.location.origin:window.location.href),De(r,"No window.location.(origin|href) available to create URL");let u=typeof a=="string"?a:Gl(a);return u=u.replace(/ $/,"%20"),!l&&u.startsWith("//")&&(u=r+u),new URL(u,r)}function Mv(a,l,r="/"){return RE(a,l,r,!1)}function RE(a,l,r,u){let o=typeof l=="string"?Ni(l):l,c=Ln(o.pathname||"/",r);if(c==null)return null;let d=jv(a);TE(d);let p=null;for(let h=0;p==null&&h<d.length;++h){let m=zE(c);p=DE(d[h],m,u)}return p}function jv(a,l=[],r=[],u=""){let o=(c,d,p)=>{let h={relativePath:p===void 0?c.path||"":p,caseSensitive:c.caseSensitive===!0,childrenIndex:d,route:c};h.relativePath.startsWith("/")&&(De(h.relativePath.startsWith(u),`Absolute route path "${h.relativePath}" nested under path "${u}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),h.relativePath=h.relativePath.slice(u.length));let m=Nn([u,h.relativePath]),y=r.concat(h);c.children&&c.children.length>0&&(De(c.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${m}".`),jv(c.children,l,y,m)),!(c.path==null&&!c.index)&&l.push({path:m,score:UE(m,c.index),routesMeta:y})};return a.forEach((c,d)=>{if(c.path===""||!c.path?.includes("?"))o(c,d);else for(let p of kv(c.path))o(c,d,p)}),l}function kv(a){let l=a.split("/");if(l.length===0)return[];let[r,...u]=l,o=r.endsWith("?"),c=r.replace(/\?$/,"");if(u.length===0)return o?[c,""]:[c];let d=kv(u.join("/")),p=[];return p.push(...d.map(h=>h===""?c:[c,h].join("/"))),o&&p.push(...d),p.map(h=>a.startsWith("/")&&h===""?"/":h)}function TE(a){a.sort((l,r)=>l.score!==r.score?r.score-l.score:NE(l.routesMeta.map(u=>u.childrenIndex),r.routesMeta.map(u=>u.childrenIndex)))}var CE=/^:[\w-]+$/,AE=3,OE=2,ME=1,jE=10,kE=-2,dp=a=>a==="*";function UE(a,l){let r=a.split("/"),u=r.length;return r.some(dp)&&(u+=kE),l&&(u+=OE),r.filter(o=>!dp(o)).reduce((o,c)=>o+(CE.test(c)?AE:c===""?ME:jE),u)}function NE(a,l){return a.length===l.length&&a.slice(0,-1).every((u,o)=>u===l[o])?a[a.length-1]-l[l.length-1]:0}function DE(a,l,r=!1){let{routesMeta:u}=a,o={},c="/",d=[];for(let p=0;p<u.length;++p){let h=u[p],m=p===u.length-1,y=c==="/"?l:l.slice(c.length)||"/",S=Ls({path:h.relativePath,caseSensitive:h.caseSensitive,end:m},y),b=h.route;if(!S&&m&&r&&!u[u.length-1].route.index&&(S=Ls({path:h.relativePath,caseSensitive:h.caseSensitive,end:!1},y)),!S)return null;Object.assign(o,S.params),d.push({params:o,pathname:Nn([c,S.pathname]),pathnameBase:VE(Nn([c,S.pathnameBase])),route:b}),S.pathnameBase!=="/"&&(c=Nn([c,S.pathnameBase]))}return d}function Ls(a,l){typeof a=="string"&&(a={path:a,caseSensitive:!1,end:!0});let[r,u]=LE(a.path,a.caseSensitive,a.end),o=l.match(r);if(!o)return null;let c=o[0],d=c.replace(/(.)\/+$/,"$1"),p=o.slice(1);return{params:u.reduce((m,{paramName:y,isOptional:S},b)=>{if(y==="*"){let _=p[b]||"";d=c.slice(0,c.length-_.length).replace(/(.)\/+$/,"$1")}const x=p[b];return S&&!x?m[y]=void 0:m[y]=(x||"").replace(/%2F/g,"/"),m},{}),pathname:c,pathnameBase:d,pattern:a}}function LE(a,l=!1,r=!0){Ft(a==="*"||!a.endsWith("*")||a.endsWith("/*"),`Route path "${a}" will be treated as if it were "${a.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${a.replace(/\*$/,"/*")}".`);let u=[],o="^"+a.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(d,p,h)=>(u.push({paramName:p,isOptional:h!=null}),h?"/?([^\\/]+)?":"/([^\\/]+)"));return a.endsWith("*")?(u.push({paramName:"*"}),o+=a==="*"||a==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?o+="\\/*$":a!==""&&a!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,l?void 0:"i"),u]}function zE(a){try{return a.split("/").map(l=>decodeURIComponent(l).replace(/\//g,"%2F")).join("/")}catch(l){return Ft(!1,`The URL path "${a}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${l}).`),a}}function Ln(a,l){if(l==="/")return a;if(!a.toLowerCase().startsWith(l.toLowerCase()))return null;let r=l.endsWith("/")?l.length-1:l.length,u=a.charAt(r);return u&&u!=="/"?null:a.slice(r)||"/"}function qE(a,l="/"){let{pathname:r,search:u="",hash:o=""}=typeof a=="string"?Ni(a):a;return{pathname:r?r.startsWith("/")?r:BE(r,l):l,search:PE(u),hash:QE(o)}}function BE(a,l){let r=l.replace(/\/+$/,"").split("/");return a.split("/").forEach(o=>{o===".."?r.length>1&&r.pop():o!=="."&&r.push(o)}),r.length>1?r.join("/"):"/"}function jc(a,l,r,u){return`Cannot include a '${a}' character in a manually specified \`to.${l}\` field [${JSON.stringify(u)}].  Please separate it out to the \`to.${r}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function HE(a){return a.filter((l,r)=>r===0||l.route.path&&l.route.path.length>0)}function Rf(a){let l=HE(a);return l.map((r,u)=>u===l.length-1?r.pathname:r.pathnameBase)}function Tf(a,l,r,u=!1){let o;typeof a=="string"?o=Ni(a):(o={...a},De(!o.pathname||!o.pathname.includes("?"),jc("?","pathname","search",o)),De(!o.pathname||!o.pathname.includes("#"),jc("#","pathname","hash",o)),De(!o.search||!o.search.includes("#"),jc("#","search","hash",o)));let c=a===""||o.pathname==="",d=c?"/":o.pathname,p;if(d==null)p=r;else{let S=l.length-1;if(!u&&d.startsWith("..")){let b=d.split("/");for(;b[0]==="..";)b.shift(),S-=1;o.pathname=b.join("/")}p=S>=0?l[S]:"/"}let h=qE(o,p),m=d&&d!=="/"&&d.endsWith("/"),y=(c||d===".")&&r.endsWith("/");return!h.pathname.endsWith("/")&&(m||y)&&(h.pathname+="/"),h}var Nn=a=>a.join("/").replace(/\/\/+/g,"/"),VE=a=>a.replace(/\/+$/,"").replace(/^\/*/,"/"),PE=a=>!a||a==="?"?"":a.startsWith("?")?a:"?"+a,QE=a=>!a||a==="#"?"":a.startsWith("#")?a:"#"+a;function YE(a){return a!=null&&typeof a.status=="number"&&typeof a.statusText=="string"&&typeof a.internal=="boolean"&&"data"in a}var Uv=["POST","PUT","PATCH","DELETE"];new Set(Uv);var GE=["GET",...Uv];new Set(GE);var Di=A.createContext(null);Di.displayName="DataRouter";var $s=A.createContext(null);$s.displayName="DataRouterState";var Nv=A.createContext({isTransitioning:!1});Nv.displayName="ViewTransition";var XE=A.createContext(new Map);XE.displayName="Fetchers";var $E=A.createContext(null);$E.displayName="Await";var en=A.createContext(null);en.displayName="Navigation";var tr=A.createContext(null);tr.displayName="Location";var tn=A.createContext({outlet:null,matches:[],isDataRoute:!1});tn.displayName="Route";var Cf=A.createContext(null);Cf.displayName="RouteError";function KE(a,{relative:l}={}){De(Li(),"useHref() may be used only in the context of a <Router> component.");let{basename:r,navigator:u}=A.useContext(en),{hash:o,pathname:c,search:d}=nr(a,{relative:l}),p=c;return r!=="/"&&(p=c==="/"?r:Nn([r,c])),u.createHref({pathname:p,search:d,hash:o})}function Li(){return A.useContext(tr)!=null}function ma(){return De(Li(),"useLocation() may be used only in the context of a <Router> component."),A.useContext(tr).location}var Dv="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function Lv(a){A.useContext(en).static||A.useLayoutEffect(a)}function Af(){let{isDataRoute:a}=A.useContext(tn);return a?s2():IE()}function IE(){De(Li(),"useNavigate() may be used only in the context of a <Router> component.");let a=A.useContext(Di),{basename:l,navigator:r}=A.useContext(en),{matches:u}=A.useContext(tn),{pathname:o}=ma(),c=JSON.stringify(Rf(u)),d=A.useRef(!1);return Lv(()=>{d.current=!0}),A.useCallback((h,m={})=>{if(Ft(d.current,Dv),!d.current)return;if(typeof h=="number"){r.go(h);return}let y=Tf(h,JSON.parse(c),o,m.relative==="path");a==null&&l!=="/"&&(y.pathname=y.pathname==="/"?l:Nn([l,y.pathname])),(m.replace?r.replace:r.push)(y,m.state,m)},[l,r,c,o,a])}A.createContext(null);function zv(){let{matches:a}=A.useContext(tn),l=a[a.length-1];return l?l.params:{}}function nr(a,{relative:l}={}){let{matches:r}=A.useContext(tn),{pathname:u}=ma(),o=JSON.stringify(Rf(r));return A.useMemo(()=>Tf(a,JSON.parse(o),u,l==="path"),[a,o,u,l])}function ZE(a,l){return qv(a,l)}function qv(a,l,r,u){De(Li(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:o}=A.useContext(en),{matches:c}=A.useContext(tn),d=c[c.length-1],p=d?d.params:{},h=d?d.pathname:"/",m=d?d.pathnameBase:"/",y=d&&d.route;{let T=y&&y.path||"";Bv(h,!y||T.endsWith("*")||T.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${h}" (under <Route path="${T}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${T}"> to <Route path="${T==="/"?"*":`${T}/*`}">.`)}let S=ma(),b;if(l){let T=typeof l=="string"?Ni(l):l;De(m==="/"||T.pathname?.startsWith(m),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${m}" but pathname "${T.pathname}" was given in the \`location\` prop.`),b=T}else b=S;let x=b.pathname||"/",_=x;if(m!=="/"){let T=m.replace(/^\//,"").split("/");_="/"+x.replace(/^\//,"").split("/").slice(T.length).join("/")}let M=Mv(a,{pathname:_});Ft(y||M!=null,`No routes matched location "${b.pathname}${b.search}${b.hash}" `),Ft(M==null||M[M.length-1].route.element!==void 0||M[M.length-1].route.Component!==void 0||M[M.length-1].route.lazy!==void 0,`Matched leaf route at location "${b.pathname}${b.search}${b.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let L=t2(M&&M.map(T=>Object.assign({},T,{params:Object.assign({},p,T.params),pathname:Nn([m,o.encodeLocation?o.encodeLocation(T.pathname).pathname:T.pathname]),pathnameBase:T.pathnameBase==="/"?m:Nn([m,o.encodeLocation?o.encodeLocation(T.pathnameBase).pathname:T.pathnameBase])})),c,r,u);return l&&L?A.createElement(tr.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...b},navigationType:"POP"}},L):L}function WE(){let a=r2(),l=YE(a)?`${a.status} ${a.statusText}`:a instanceof Error?a.message:JSON.stringify(a),r=a instanceof Error?a.stack:null,u="rgba(200,200,200, 0.5)",o={padding:"0.5rem",backgroundColor:u},c={padding:"2px 4px",backgroundColor:u},d=null;return console.error("Error handled by React Router default ErrorBoundary:",a),d=A.createElement(A.Fragment,null,A.createElement("p",null,"💿 Hey developer 👋"),A.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",A.createElement("code",{style:c},"ErrorBoundary")," or"," ",A.createElement("code",{style:c},"errorElement")," prop on your route.")),A.createElement(A.Fragment,null,A.createElement("h2",null,"Unexpected Application Error!"),A.createElement("h3",{style:{fontStyle:"italic"}},l),r?A.createElement("pre",{style:o},r):null,d)}var JE=A.createElement(WE,null),FE=class extends A.Component{constructor(a){super(a),this.state={location:a.location,revalidation:a.revalidation,error:a.error}}static getDerivedStateFromError(a){return{error:a}}static getDerivedStateFromProps(a,l){return l.location!==a.location||l.revalidation!=="idle"&&a.revalidation==="idle"?{error:a.error,location:a.location,revalidation:a.revalidation}:{error:a.error!==void 0?a.error:l.error,location:l.location,revalidation:a.revalidation||l.revalidation}}componentDidCatch(a,l){console.error("React Router caught the following error during render",a,l)}render(){return this.state.error!==void 0?A.createElement(tn.Provider,{value:this.props.routeContext},A.createElement(Cf.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function e2({routeContext:a,match:l,children:r}){let u=A.useContext(Di);return u&&u.static&&u.staticContext&&(l.route.errorElement||l.route.ErrorBoundary)&&(u.staticContext._deepestRenderedBoundaryId=l.route.id),A.createElement(tn.Provider,{value:a},r)}function t2(a,l=[],r=null,u=null){if(a==null){if(!r)return null;if(r.errors)a=r.matches;else if(l.length===0&&!r.initialized&&r.matches.length>0)a=r.matches;else return null}let o=a,c=r?.errors;if(c!=null){let h=o.findIndex(m=>m.route.id&&c?.[m.route.id]!==void 0);De(h>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(c).join(",")}`),o=o.slice(0,Math.min(o.length,h+1))}let d=!1,p=-1;if(r)for(let h=0;h<o.length;h++){let m=o[h];if((m.route.HydrateFallback||m.route.hydrateFallbackElement)&&(p=h),m.route.id){let{loaderData:y,errors:S}=r,b=m.route.loader&&!y.hasOwnProperty(m.route.id)&&(!S||S[m.route.id]===void 0);if(m.route.lazy||b){d=!0,p>=0?o=o.slice(0,p+1):o=[o[0]];break}}}return o.reduceRight((h,m,y)=>{let S,b=!1,x=null,_=null;r&&(S=c&&m.route.id?c[m.route.id]:void 0,x=m.route.errorElement||JE,d&&(p<0&&y===0?(Bv("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),b=!0,_=null):p===y&&(b=!0,_=m.route.hydrateFallbackElement||null)));let M=l.concat(o.slice(0,y+1)),L=()=>{let T;return S?T=x:b?T=_:m.route.Component?T=A.createElement(m.route.Component,null):m.route.element?T=m.route.element:T=h,A.createElement(e2,{match:m,routeContext:{outlet:h,matches:M,isDataRoute:r!=null},children:T})};return r&&(m.route.ErrorBoundary||m.route.errorElement||y===0)?A.createElement(FE,{location:r.location,revalidation:r.revalidation,component:x,error:S,children:L(),routeContext:{outlet:null,matches:M,isDataRoute:!0}}):L()},null)}function Of(a){return`${a} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function n2(a){let l=A.useContext(Di);return De(l,Of(a)),l}function a2(a){let l=A.useContext($s);return De(l,Of(a)),l}function i2(a){let l=A.useContext(tn);return De(l,Of(a)),l}function Mf(a){let l=i2(a),r=l.matches[l.matches.length-1];return De(r.route.id,`${a} can only be used on routes that contain a unique "id"`),r.route.id}function l2(){return Mf("useRouteId")}function r2(){let a=A.useContext(Cf),l=a2("useRouteError"),r=Mf("useRouteError");return a!==void 0?a:l.errors?.[r]}function s2(){let{router:a}=n2("useNavigate"),l=Mf("useNavigate"),r=A.useRef(!1);return Lv(()=>{r.current=!0}),A.useCallback(async(o,c={})=>{Ft(r.current,Dv),r.current&&(typeof o=="number"?a.navigate(o):await a.navigate(o,{fromRouteId:l,...c}))},[a,l])}var hp={};function Bv(a,l,r){!l&&!hp[a]&&(hp[a]=!0,Ft(!1,r))}A.memo(u2);function u2({routes:a,future:l,state:r}){return qv(a,void 0,r,l)}function o2({to:a,replace:l,state:r,relative:u}){De(Li(),"<Navigate> may be used only in the context of a <Router> component.");let{static:o}=A.useContext(en);Ft(!o,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:c}=A.useContext(tn),{pathname:d}=ma(),p=Af(),h=Tf(a,Rf(c),d,u==="path"),m=JSON.stringify(h);return A.useEffect(()=>{p(JSON.parse(m),{replace:l,state:r,relative:u})},[p,m,u,l,r]),null}function Da(a){De(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function c2({basename:a="/",children:l=null,location:r,navigationType:u="POP",navigator:o,static:c=!1}){De(!Li(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let d=a.replace(/^\/*/,"/"),p=A.useMemo(()=>({basename:d,navigator:o,static:c,future:{}}),[d,o,c]);typeof r=="string"&&(r=Ni(r));let{pathname:h="/",search:m="",hash:y="",state:S=null,key:b="default"}=r,x=A.useMemo(()=>{let _=Ln(h,d);return _==null?null:{location:{pathname:_,search:m,hash:y,state:S,key:b},navigationType:u}},[d,h,m,y,S,b,u]);return Ft(x!=null,`<Router basename="${d}"> is not able to match the URL "${h}${m}${y}" because it does not start with the basename, so the <Router> won't render anything.`),x==null?null:A.createElement(en.Provider,{value:p},A.createElement(tr.Provider,{children:l,value:x}))}function f2({children:a,location:l}){return ZE(gf(a),l)}function gf(a,l=[]){let r=[];return A.Children.forEach(a,(u,o)=>{if(!A.isValidElement(u))return;let c=[...l,o];if(u.type===A.Fragment){r.push.apply(r,gf(u.props.children,c));return}De(u.type===Da,`[${typeof u.type=="string"?u.type:u.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),De(!u.props.index||!u.props.children,"An index route cannot have child routes.");let d={id:u.props.id||c.join("-"),caseSensitive:u.props.caseSensitive,element:u.props.element,Component:u.props.Component,index:u.props.index,path:u.props.path,loader:u.props.loader,action:u.props.action,hydrateFallbackElement:u.props.hydrateFallbackElement,HydrateFallback:u.props.HydrateFallback,errorElement:u.props.errorElement,ErrorBoundary:u.props.ErrorBoundary,hasErrorBoundary:u.props.hasErrorBoundary===!0||u.props.ErrorBoundary!=null||u.props.errorElement!=null,shouldRevalidate:u.props.shouldRevalidate,handle:u.props.handle,lazy:u.props.lazy};u.props.children&&(d.children=gf(u.props.children,c)),r.push(d)}),r}var Us="get",Ns="application/x-www-form-urlencoded";function Ks(a){return a!=null&&typeof a.tagName=="string"}function d2(a){return Ks(a)&&a.tagName.toLowerCase()==="button"}function h2(a){return Ks(a)&&a.tagName.toLowerCase()==="form"}function m2(a){return Ks(a)&&a.tagName.toLowerCase()==="input"}function g2(a){return!!(a.metaKey||a.altKey||a.ctrlKey||a.shiftKey)}function p2(a,l){return a.button===0&&(!l||l==="_self")&&!g2(a)}var Ts=null;function v2(){if(Ts===null)try{new FormData(document.createElement("form"),0),Ts=!1}catch{Ts=!0}return Ts}var y2=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function kc(a){return a!=null&&!y2.has(a)?(Ft(!1,`"${a}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Ns}"`),null):a}function b2(a,l){let r,u,o,c,d;if(h2(a)){let p=a.getAttribute("action");u=p?Ln(p,l):null,r=a.getAttribute("method")||Us,o=kc(a.getAttribute("enctype"))||Ns,c=new FormData(a)}else if(d2(a)||m2(a)&&(a.type==="submit"||a.type==="image")){let p=a.form;if(p==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let h=a.getAttribute("formaction")||p.getAttribute("action");if(u=h?Ln(h,l):null,r=a.getAttribute("formmethod")||p.getAttribute("method")||Us,o=kc(a.getAttribute("formenctype"))||kc(p.getAttribute("enctype"))||Ns,c=new FormData(p,a),!v2()){let{name:m,type:y,value:S}=a;if(y==="image"){let b=m?`${m}.`:"";c.append(`${b}x`,"0"),c.append(`${b}y`,"0")}else m&&c.append(m,S)}}else{if(Ks(a))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');r=Us,u=null,o=Ns,d=a}return c&&o==="text/plain"&&(d=c,c=void 0),{action:u,method:r.toLowerCase(),encType:o,formData:c,body:d}}function jf(a,l){if(a===!1||a===null||typeof a>"u")throw new Error(l)}async function S2(a,l){if(a.id in l)return l[a.id];try{let r=await import(a.module);return l[a.id]=r,r}catch(r){return console.error(`Error loading route module \`${a.module}\`, reloading page...`),console.error(r),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function E2(a){return a==null?!1:a.href==null?a.rel==="preload"&&typeof a.imageSrcSet=="string"&&typeof a.imageSizes=="string":typeof a.rel=="string"&&typeof a.href=="string"}async function x2(a,l,r){let u=await Promise.all(a.map(async o=>{let c=l.routes[o.route.id];if(c){let d=await S2(c,r);return d.links?d.links():[]}return[]}));return T2(u.flat(1).filter(E2).filter(o=>o.rel==="stylesheet"||o.rel==="preload").map(o=>o.rel==="stylesheet"?{...o,rel:"prefetch",as:"style"}:{...o,rel:"prefetch"}))}function mp(a,l,r,u,o,c){let d=(h,m)=>r[m]?h.route.id!==r[m].route.id:!0,p=(h,m)=>r[m].pathname!==h.pathname||r[m].route.path?.endsWith("*")&&r[m].params["*"]!==h.params["*"];return c==="assets"?l.filter((h,m)=>d(h,m)||p(h,m)):c==="data"?l.filter((h,m)=>{let y=u.routes[h.route.id];if(!y||!y.hasLoader)return!1;if(d(h,m)||p(h,m))return!0;if(h.route.shouldRevalidate){let S=h.route.shouldRevalidate({currentUrl:new URL(o.pathname+o.search+o.hash,window.origin),currentParams:r[0]?.params||{},nextUrl:new URL(a,window.origin),nextParams:h.params,defaultShouldRevalidate:!0});if(typeof S=="boolean")return S}return!0}):[]}function _2(a,l,{includeHydrateFallback:r}={}){return w2(a.map(u=>{let o=l.routes[u.route.id];if(!o)return[];let c=[o.module];return o.clientActionModule&&(c=c.concat(o.clientActionModule)),o.clientLoaderModule&&(c=c.concat(o.clientLoaderModule)),r&&o.hydrateFallbackModule&&(c=c.concat(o.hydrateFallbackModule)),o.imports&&(c=c.concat(o.imports)),c}).flat(1))}function w2(a){return[...new Set(a)]}function R2(a){let l={},r=Object.keys(a).sort();for(let u of r)l[u]=a[u];return l}function T2(a,l){let r=new Set;return new Set(l),a.reduce((u,o)=>{let c=JSON.stringify(R2(o));return r.has(c)||(r.add(c),u.push({key:c,link:o})),u},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var C2=new Set([100,101,204,205]);function A2(a,l){let r=typeof a=="string"?new URL(a,typeof window>"u"?"server://singlefetch/":window.location.origin):a;return r.pathname==="/"?r.pathname="_root.data":l&&Ln(r.pathname,l)==="/"?r.pathname=`${l.replace(/\/$/,"")}/_root.data`:r.pathname=`${r.pathname.replace(/\/$/,"")}.data`,r}function Hv(){let a=A.useContext(Di);return jf(a,"You must render this element inside a <DataRouterContext.Provider> element"),a}function O2(){let a=A.useContext($s);return jf(a,"You must render this element inside a <DataRouterStateContext.Provider> element"),a}var kf=A.createContext(void 0);kf.displayName="FrameworkContext";function Vv(){let a=A.useContext(kf);return jf(a,"You must render this element inside a <HydratedRouter> element"),a}function M2(a,l){let r=A.useContext(kf),[u,o]=A.useState(!1),[c,d]=A.useState(!1),{onFocus:p,onBlur:h,onMouseEnter:m,onMouseLeave:y,onTouchStart:S}=l,b=A.useRef(null);A.useEffect(()=>{if(a==="render"&&d(!0),a==="viewport"){let M=T=>{T.forEach(P=>{d(P.isIntersecting)})},L=new IntersectionObserver(M,{threshold:.5});return b.current&&L.observe(b.current),()=>{L.disconnect()}}},[a]),A.useEffect(()=>{if(u){let M=setTimeout(()=>{d(!0)},100);return()=>{clearTimeout(M)}}},[u]);let x=()=>{o(!0)},_=()=>{o(!1),d(!1)};return r?a!=="intent"?[c,b,{}]:[c,b,{onFocus:zl(p,x),onBlur:zl(h,_),onMouseEnter:zl(m,x),onMouseLeave:zl(y,_),onTouchStart:zl(S,x)}]:[!1,b,{}]}function zl(a,l){return r=>{a&&a(r),r.defaultPrevented||l(r)}}function j2({page:a,...l}){let{router:r}=Hv(),u=A.useMemo(()=>Mv(r.routes,a,r.basename),[r.routes,a,r.basename]);return u?A.createElement(U2,{page:a,matches:u,...l}):null}function k2(a){let{manifest:l,routeModules:r}=Vv(),[u,o]=A.useState([]);return A.useEffect(()=>{let c=!1;return x2(a,l,r).then(d=>{c||o(d)}),()=>{c=!0}},[a,l,r]),u}function U2({page:a,matches:l,...r}){let u=ma(),{manifest:o,routeModules:c}=Vv(),{basename:d}=Hv(),{loaderData:p,matches:h}=O2(),m=A.useMemo(()=>mp(a,l,h,o,u,"data"),[a,l,h,o,u]),y=A.useMemo(()=>mp(a,l,h,o,u,"assets"),[a,l,h,o,u]),S=A.useMemo(()=>{if(a===u.pathname+u.search+u.hash)return[];let _=new Set,M=!1;if(l.forEach(T=>{let P=o.routes[T.route.id];!P||!P.hasLoader||(!m.some(G=>G.route.id===T.route.id)&&T.route.id in p&&c[T.route.id]?.shouldRevalidate||P.hasClientLoader?M=!0:_.add(T.route.id))}),_.size===0)return[];let L=A2(a,d);return M&&_.size>0&&L.searchParams.set("_routes",l.filter(T=>_.has(T.route.id)).map(T=>T.route.id).join(",")),[L.pathname+L.search]},[d,p,u,o,m,l,a,c]),b=A.useMemo(()=>_2(y,o),[y,o]),x=k2(y);return A.createElement(A.Fragment,null,S.map(_=>A.createElement("link",{key:_,rel:"prefetch",as:"fetch",href:_,...r})),b.map(_=>A.createElement("link",{key:_,rel:"modulepreload",href:_,...r})),x.map(({key:_,link:M})=>A.createElement("link",{key:_,...M})))}function N2(...a){return l=>{a.forEach(r=>{typeof r=="function"?r(l):r!=null&&(r.current=l)})}}var Pv=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Pv&&(window.__reactRouterVersion="7.6.2")}catch{}function D2({basename:a,children:l,window:r}){let u=A.useRef();u.current==null&&(u.current=EE({window:r,v5Compat:!0}));let o=u.current,[c,d]=A.useState({action:o.action,location:o.location}),p=A.useCallback(h=>{A.startTransition(()=>d(h))},[d]);return A.useLayoutEffect(()=>o.listen(p),[o,p]),A.createElement(c2,{basename:a,children:l,location:c.location,navigationType:c.action,navigator:o})}var Qv=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Jt=A.forwardRef(function({onClick:l,discover:r="render",prefetch:u="none",relative:o,reloadDocument:c,replace:d,state:p,target:h,to:m,preventScrollReset:y,viewTransition:S,...b},x){let{basename:_}=A.useContext(en),M=typeof m=="string"&&Qv.test(m),L,T=!1;if(typeof m=="string"&&M&&(L=m,Pv))try{let re=new URL(window.location.href),ne=m.startsWith("//")?new URL(re.protocol+m):new URL(m),pe=Ln(ne.pathname,_);ne.origin===re.origin&&pe!=null?m=pe+ne.search+ne.hash:T=!0}catch{Ft(!1,`<Link to="${m}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let P=KE(m,{relative:o}),[G,J,Q]=M2(u,b),F=B2(m,{replace:d,state:p,target:h,preventScrollReset:y,relative:o,viewTransition:S});function I(re){l&&l(re),re.defaultPrevented||F(re)}let $=A.createElement("a",{...b,...Q,href:L||P,onClick:T||c?l:I,ref:N2(x,J),target:h,"data-discover":!M&&r==="render"?"true":void 0});return G&&!M?A.createElement(A.Fragment,null,$,A.createElement(j2,{page:P})):$});Jt.displayName="Link";var L2=A.forwardRef(function({"aria-current":l="page",caseSensitive:r=!1,className:u="",end:o=!1,style:c,to:d,viewTransition:p,children:h,...m},y){let S=nr(d,{relative:m.relative}),b=ma(),x=A.useContext($s),{navigator:_,basename:M}=A.useContext(en),L=x!=null&&Y2(S)&&p===!0,T=_.encodeLocation?_.encodeLocation(S).pathname:S.pathname,P=b.pathname,G=x&&x.navigation&&x.navigation.location?x.navigation.location.pathname:null;r||(P=P.toLowerCase(),G=G?G.toLowerCase():null,T=T.toLowerCase()),G&&M&&(G=Ln(G,M)||G);const J=T!=="/"&&T.endsWith("/")?T.length-1:T.length;let Q=P===T||!o&&P.startsWith(T)&&P.charAt(J)==="/",F=G!=null&&(G===T||!o&&G.startsWith(T)&&G.charAt(T.length)==="/"),I={isActive:Q,isPending:F,isTransitioning:L},$=Q?l:void 0,re;typeof u=="function"?re=u(I):re=[u,Q?"active":null,F?"pending":null,L?"transitioning":null].filter(Boolean).join(" ");let ne=typeof c=="function"?c(I):c;return A.createElement(Jt,{...m,"aria-current":$,className:re,ref:y,style:ne,to:d,viewTransition:p},typeof h=="function"?h(I):h)});L2.displayName="NavLink";var z2=A.forwardRef(({discover:a="render",fetcherKey:l,navigate:r,reloadDocument:u,replace:o,state:c,method:d=Us,action:p,onSubmit:h,relative:m,preventScrollReset:y,viewTransition:S,...b},x)=>{let _=P2(),M=Q2(p,{relative:m}),L=d.toLowerCase()==="get"?"get":"post",T=typeof p=="string"&&Qv.test(p),P=G=>{if(h&&h(G),G.defaultPrevented)return;G.preventDefault();let J=G.nativeEvent.submitter,Q=J?.getAttribute("formmethod")||d;_(J||G.currentTarget,{fetcherKey:l,method:Q,navigate:r,replace:o,state:c,relative:m,preventScrollReset:y,viewTransition:S})};return A.createElement("form",{ref:x,method:L,action:M,onSubmit:u?h:P,...b,"data-discover":!T&&a==="render"?"true":void 0})});z2.displayName="Form";function q2(a){return`${a} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Yv(a){let l=A.useContext(Di);return De(l,q2(a)),l}function B2(a,{target:l,replace:r,state:u,preventScrollReset:o,relative:c,viewTransition:d}={}){let p=Af(),h=ma(),m=nr(a,{relative:c});return A.useCallback(y=>{if(p2(y,l)){y.preventDefault();let S=r!==void 0?r:Gl(h)===Gl(m);p(a,{replace:S,state:u,preventScrollReset:o,relative:c,viewTransition:d})}},[h,p,m,r,u,l,a,o,c,d])}var H2=0,V2=()=>`__${String(++H2)}__`;function P2(){let{router:a}=Yv("useSubmit"),{basename:l}=A.useContext(en),r=l2();return A.useCallback(async(u,o={})=>{let{action:c,method:d,encType:p,formData:h,body:m}=b2(u,l);if(o.navigate===!1){let y=o.fetcherKey||V2();await a.fetch(y,r,o.action||c,{preventScrollReset:o.preventScrollReset,formData:h,body:m,formMethod:o.method||d,formEncType:o.encType||p,flushSync:o.flushSync})}else await a.navigate(o.action||c,{preventScrollReset:o.preventScrollReset,formData:h,body:m,formMethod:o.method||d,formEncType:o.encType||p,replace:o.replace,state:o.state,fromRouteId:r,flushSync:o.flushSync,viewTransition:o.viewTransition})},[a,l,r])}function Q2(a,{relative:l}={}){let{basename:r}=A.useContext(en),u=A.useContext(tn);De(u,"useFormAction must be used inside a RouteContext");let[o]=u.matches.slice(-1),c={...nr(a||".",{relative:l})},d=ma();if(a==null){c.search=d.search;let p=new URLSearchParams(c.search),h=p.getAll("index");if(h.some(y=>y==="")){p.delete("index"),h.filter(S=>S).forEach(S=>p.append("index",S));let y=p.toString();c.search=y?`?${y}`:""}}return(!a||a===".")&&o.route.index&&(c.search=c.search?c.search.replace(/^\?/,"?index&"):"?index"),r!=="/"&&(c.pathname=c.pathname==="/"?r:Nn([r,c.pathname])),Gl(c)}function Y2(a,l={}){let r=A.useContext(Nv);De(r!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:u}=Yv("useViewTransitionState"),o=nr(a,{relative:l.relative});if(!r.isTransitioning)return!1;let c=Ln(r.currentLocation.pathname,u)||r.currentLocation.pathname,d=Ln(r.nextLocation.pathname,u)||r.nextLocation.pathname;return Ls(o.pathname,d)!=null||Ls(o.pathname,c)!=null}[...C2];function jn({children:a,variant:l="primary",size:r="md",loading:u=!1,disabled:o,className:c="",...d}){const p="inline-flex items-center justify-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors",h={primary:"bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500",secondary:"bg-gray-200 hover:bg-gray-300 text-gray-900 focus:ring-gray-500",danger:"bg-red-600 hover:bg-red-700 text-white focus:ring-red-500"},m={sm:"px-3 py-2 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"},y=`${p} ${h[l]} ${m[r]} ${c}`;return w.jsxs("button",{className:y,disabled:o||u,...d,children:[u&&w.jsxs("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[w.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),w.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),a]})}const G2=A.forwardRef(({label:a,error:l,helperText:r,className:u="",...o},c)=>{const d=`
      block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm 
      placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 
      sm:text-sm ${l?"border-red-300 focus:ring-red-500 focus:border-red-500":""} 
      ${u}
    `;return w.jsxs("div",{className:"space-y-1",children:[a&&w.jsx("label",{className:"block text-sm font-medium text-gray-700",children:a}),w.jsx("input",{ref:c,className:d,...o}),l&&w.jsx("p",{className:"text-sm text-red-600",children:l}),r&&!l&&w.jsx("p",{className:"text-sm text-gray-500",children:r})]})}),Gv=A.forwardRef(({label:a,error:l,helperText:r,className:u="",...o},c)=>{const d=`
      block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm 
      placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 
      sm:text-sm resize-vertical ${l?"border-red-300 focus:ring-red-500 focus:border-red-500":""} 
      ${u}
    `;return w.jsxs("div",{className:"space-y-1",children:[a&&w.jsx("label",{className:"block text-sm font-medium text-gray-700",children:a}),w.jsx("textarea",{ref:c,className:d,rows:4,...o}),l&&w.jsx("p",{className:"text-sm text-red-600",children:l}),r&&!l&&w.jsx("p",{className:"text-sm text-gray-500",children:r})]})});function kn({children:a,className:l="",padding:r="md"}){const o=`
    bg-white rounded-lg shadow-sm border border-gray-200 
    ${{sm:"p-4",md:"p-6",lg:"p-8"}[r]} ${l}
  `;return w.jsx("div",{className:o,children:a})}function Xv({children:a,className:l=""}){return w.jsx("div",{className:`border-b border-gray-200 pb-4 mb-4 ${l}`,children:a})}function $v({children:a,className:l=""}){return w.jsx("h3",{className:`text-lg font-medium text-gray-900 ${l}`,children:a})}function Un({children:a,className:l=""}){return w.jsx("div",{className:l,children:a})}const X2="modulepreload",$2=function(a){return"/"+a},gp={},Cs=function(l,r,u){let o=Promise.resolve();if(r&&r.length>0){let m=function(y){return Promise.all(y.map(S=>Promise.resolve(S).then(b=>({status:"fulfilled",value:b}),b=>({status:"rejected",reason:b}))))};var d=m;document.getElementsByTagName("link");const p=document.querySelector("meta[property=csp-nonce]"),h=p?.nonce||p?.getAttribute("nonce");o=m(r.map(y=>{if(y=$2(y),y in gp)return;gp[y]=!0;const S=y.endsWith(".css"),b=S?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${y}"]${b}`))return;const x=document.createElement("link");if(x.rel=S?"stylesheet":X2,S||(x.as="script"),x.crossOrigin="",x.href=y,h&&x.setAttribute("nonce",h),document.head.appendChild(x),S)return new Promise((_,M)=>{x.addEventListener("load",_),x.addEventListener("error",()=>M(new Error(`Unable to preload CSS for ${y}`)))})}))}function c(p){const h=new Event("vite:preloadError",{cancelable:!0});if(h.payload=p,window.dispatchEvent(h),!h.defaultPrevented)throw p}return o.then(p=>{for(const h of p||[])h.status==="rejected"&&c(h.reason);return l().catch(c)})},K2=(a,l,r)=>{const u=a[l];return u?typeof u=="function"?u():Promise.resolve(u):new Promise((o,c)=>{(typeof queueMicrotask=="function"?queueMicrotask:setTimeout)(c.bind(null,new Error("Unknown variable dynamic import: "+l+(l.split("/").length!==r?". Note that variables only represent file names one level deep.":""))))})},Kv=A.createContext(void 0),I2=({children:a,defaultLocale:l="nb"})=>{const[r,u]=A.useState(l),[o,c]=A.useState({}),[d,p]=A.useState({});A.useEffect(()=>{(async()=>{try{const y=await Cs(()=>import("./nb-CTvkJfec.js"),[]);p(y.default)}catch(y){console.error("Failed to load Norwegian texts:",y)}})()},[]),A.useEffect(()=>{const m=async()=>{try{const S=(await K2(Object.assign({"../locales/da.json":()=>Cs(()=>import("./da-DVgc49uq.js"),[]),"../locales/nb.json":()=>Cs(()=>import("./nb-CTvkJfec.js"),[]),"../locales/sv.json":()=>Cs(()=>import("./sv-CrNeWErF.js"),[])}),`../locales/${r}.json`,3)).default,b={};Object.keys(d).forEach(x=>{const _=x;b[_]=S[_]||d[_]||""}),c(b)}catch(y){console.error(`Failed to load locale ${r}:`,y),c(d)}};Object.keys(d).length>0&&m()},[r,d]);const h={locale:r,texts:o,setLocale:u};return w.jsx(Kv.Provider,{value:h,children:a})},za=()=>{const a=A.useContext(Kv);if(a===void 0)throw new Error("useLocale must be used within a LocaleProvider");return a};function Uf({size:a="md",className:l=""}){const r={sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12"};return w.jsx("div",{className:`animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 ${r[a]} ${l}`})}function Uc({children:a}){const{isSignedIn:l}=hv(),{texts:r}=za();return w.jsxs("div",{className:"min-h-screen bg-gray-50",children:[w.jsx("header",{className:"bg-white shadow-sm border-b",children:w.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:w.jsxs("div",{className:"flex justify-between items-center h-16",children:[w.jsx("div",{className:"flex items-center",children:w.jsx(Jt,{to:"/",className:"text-xl font-bold text-gray-900",children:r.appTitle})}),w.jsx("div",{className:"flex items-center space-x-4",children:l?w.jsxs(w.Fragment,{children:[w.jsx(Jt,{to:"/create-project",className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:r.createProject}),w.jsx(dE,{afterSignOutUrl:"/"})]}):w.jsx(Jt,{to:"/sign-in",className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:r.signIn})})]})})}),w.jsx("main",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:w.jsx("div",{className:"px-4 py-6 sm:px-0",children:a})})]})}var fn=[],Gt=[],Z2=Uint8Array,Nc="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";for(var wi=0,W2=Nc.length;wi<W2;++wi)fn[wi]=Nc[wi],Gt[Nc.charCodeAt(wi)]=wi;Gt[45]=62;Gt[95]=63;function J2(a){var l=a.length;if(l%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=a.indexOf("=");r===-1&&(r=l);var u=r===l?0:4-r%4;return[r,u]}function F2(a,l,r){return(l+r)*3/4-r}function Xl(a){var l,r=J2(a),u=r[0],o=r[1],c=new Z2(F2(a,u,o)),d=0,p=o>0?u-4:u,h;for(h=0;h<p;h+=4)l=Gt[a.charCodeAt(h)]<<18|Gt[a.charCodeAt(h+1)]<<12|Gt[a.charCodeAt(h+2)]<<6|Gt[a.charCodeAt(h+3)],c[d++]=l>>16&255,c[d++]=l>>8&255,c[d++]=l&255;return o===2&&(l=Gt[a.charCodeAt(h)]<<2|Gt[a.charCodeAt(h+1)]>>4,c[d++]=l&255),o===1&&(l=Gt[a.charCodeAt(h)]<<10|Gt[a.charCodeAt(h+1)]<<4|Gt[a.charCodeAt(h+2)]>>2,c[d++]=l>>8&255,c[d++]=l&255),c}function ex(a){return fn[a>>18&63]+fn[a>>12&63]+fn[a>>6&63]+fn[a&63]}function tx(a,l,r){for(var u,o=[],c=l;c<r;c+=3)u=(a[c]<<16&16711680)+(a[c+1]<<8&65280)+(a[c+2]&255),o.push(ex(u));return o.join("")}function $l(a){for(var l,r=a.length,u=r%3,o=[],c=16383,d=0,p=r-u;d<p;d+=c)o.push(tx(a,d,d+c>p?p:d+c));return u===1?(l=a[r-1],o.push(fn[l>>2]+fn[l<<4&63]+"==")):u===2&&(l=(a[r-2]<<8)+a[r-1],o.push(fn[l>>10]+fn[l>>4&63]+fn[l<<2&63]+"=")),o.join("")}function Mn(a){if(a===void 0)return{};if(!Iv(a))throw new Error(`The arguments to a Convex function must be an object. Received: ${a}`);return a}function nx(a){if(typeof a>"u")throw new Error("Client created with undefined deployment address. If you used an environment variable, check that it's set.");if(typeof a!="string")throw new Error(`Invalid deployment address: found ${a}".`);if(!(a.startsWith("http:")||a.startsWith("https:")))throw new Error(`Invalid deployment address: Must start with "https://" or "http://". Found "${a}".`);try{new URL(a)}catch{throw new Error(`Invalid deployment address: "${a}" is not a valid URL. If you believe this URL is correct, use the \`skipConvexDeploymentUrlCheck\` option to bypass this.`)}if(a.endsWith(".convex.site"))throw new Error(`Invalid deployment address: "${a}" ends with .convex.site, which is used for HTTP Actions. Convex deployment URLs typically end with .convex.cloud? If you believe this URL is correct, use the \`skipConvexDeploymentUrlCheck\` option to bypass this.`)}function Iv(a){const l=typeof a=="object",r=Object.getPrototypeOf(a),u=r===null||r===Object.prototype||r?.constructor?.name==="Object";return l&&u}const Zv=!0,Mi=BigInt("-9223372036854775808"),Nf=BigInt("9223372036854775807"),pf=BigInt("0"),ax=BigInt("8"),ix=BigInt("256");function Wv(a){return Number.isNaN(a)||!Number.isFinite(a)||Object.is(a,-0)}function lx(a){a<pf&&(a-=Mi+Mi);let l=a.toString(16);l.length%2===1&&(l="0"+l);const r=new Uint8Array(new ArrayBuffer(8));let u=0;for(const o of l.match(/.{2}/g).reverse())r.set([parseInt(o,16)],u++),a>>=ax;return $l(r)}function rx(a){const l=Xl(a);if(l.byteLength!==8)throw new Error(`Received ${l.byteLength} bytes, expected 8 for $integer`);let r=pf,u=pf;for(const o of l)r+=BigInt(o)*ix**u,u++;return r>Nf&&(r+=Mi+Mi),r}function sx(a){if(a<Mi||Nf<a)throw new Error(`BigInt ${a} does not fit into a 64-bit signed integer.`);const l=new ArrayBuffer(8);return new DataView(l).setBigInt64(0,a,!0),$l(new Uint8Array(l))}function ux(a){const l=Xl(a);if(l.byteLength!==8)throw new Error(`Received ${l.byteLength} bytes, expected 8 for $integer`);return new DataView(l.buffer).getBigInt64(0,!0)}const ox=DataView.prototype.setBigInt64?sx:lx,cx=DataView.prototype.getBigInt64?ux:rx,pp=1024;function Jv(a){if(a.length>pp)throw new Error(`Field name ${a} exceeds maximum field name length ${pp}.`);if(a.startsWith("$"))throw new Error(`Field name ${a} starts with a '$', which is reserved.`);for(let l=0;l<a.length;l+=1){const r=a.charCodeAt(l);if(r<32||r>=127)throw new Error(`Field name ${a} has invalid character '${a[l]}': Field names can only contain non-control ASCII characters`)}}function ji(a){if(a===null||typeof a=="boolean"||typeof a=="number"||typeof a=="string")return a;if(Array.isArray(a))return a.map(u=>ji(u));if(typeof a!="object")throw new Error(`Unexpected type of ${a}`);const l=Object.entries(a);if(l.length===1){const u=l[0][0];if(u==="$bytes"){if(typeof a.$bytes!="string")throw new Error(`Malformed $bytes field on ${a}`);return Xl(a.$bytes).buffer}if(u==="$integer"){if(typeof a.$integer!="string")throw new Error(`Malformed $integer field on ${a}`);return cx(a.$integer)}if(u==="$float"){if(typeof a.$float!="string")throw new Error(`Malformed $float field on ${a}`);const o=Xl(a.$float);if(o.byteLength!==8)throw new Error(`Received ${o.byteLength} bytes, expected 8 for $float`);const d=new DataView(o.buffer).getFloat64(0,Zv);if(!Wv(d))throw new Error(`Float ${d} should be encoded as a number`);return d}if(u==="$set")throw new Error("Received a Set which is no longer supported as a Convex type.");if(u==="$map")throw new Error("Received a Map which is no longer supported as a Convex type.")}const r={};for(const[u,o]of Object.entries(a))Jv(u),r[u]=ji(o);return r}function Pl(a){return JSON.stringify(a,(l,r)=>r===void 0?"undefined":typeof r=="bigint"?`${r.toString()}n`:r)}function vf(a,l,r,u){if(a===void 0){const d=r&&` (present at path ${r} in original object ${Pl(l)})`;throw new Error(`undefined is not a valid Convex value${d}. To learn about Convex's supported types, see https://docs.convex.dev/using/types.`)}if(a===null)return a;if(typeof a=="bigint"){if(a<Mi||Nf<a)throw new Error(`BigInt ${a} does not fit into a 64-bit signed integer.`);return{$integer:ox(a)}}if(typeof a=="number")if(Wv(a)){const d=new ArrayBuffer(8);return new DataView(d).setFloat64(0,a,Zv),{$float:$l(new Uint8Array(d))}}else return a;if(typeof a=="boolean"||typeof a=="string")return a;if(a instanceof ArrayBuffer)return{$bytes:$l(new Uint8Array(a))};if(Array.isArray(a))return a.map((d,p)=>vf(d,l,r+`[${p}]`));if(a instanceof Set)throw new Error(Dc(r,"Set",[...a],l));if(a instanceof Map)throw new Error(Dc(r,"Map",[...a],l));if(!Iv(a)){const d=a?.constructor?.name,p=d?`${d} `:"";throw new Error(Dc(r,p,a,l))}const o={},c=Object.entries(a);c.sort(([d,p],[h,m])=>d===h?0:d<h?-1:1);for(const[d,p]of c)p!==void 0&&(Jv(d),o[d]=vf(p,l,r+`.${d}`));return o}function Dc(a,l,r,u){return a?`${l}${Pl(r)} is not a supported Convex type (present at path ${a} in original object ${Pl(u)}). To learn about Convex's supported types, see https://docs.convex.dev/using/types.`:`${l}${Pl(r)} is not a supported Convex type.`}function ha(a){return vf(a,a,"")}var fx=Object.defineProperty,dx=(a,l,r)=>l in a?fx(a,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[l]=r,Lc=(a,l,r)=>dx(a,typeof l!="symbol"?l+"":l,r),vp,yp;const hx=Symbol.for("ConvexError");class yf extends(yp=Error,vp=hx,yp){constructor(l){super(typeof l=="string"?l:Pl(l)),Lc(this,"name","ConvexError"),Lc(this,"data"),Lc(this,vp,!0),this.data=l}}const Fv=()=>Array.from({length:4},()=>0);Fv();Fv();const bp="1.25.0";var mx=Object.defineProperty,gx=(a,l,r)=>l in a?mx(a,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[l]=r,Sp=(a,l,r)=>gx(a,typeof l!="symbol"?l+"":l,r);const px="color:rgb(0, 145, 255)";function ey(a){switch(a){case"query":return"Q";case"mutation":return"M";case"action":return"A";case"any":return"?"}}class ty{constructor(l){Sp(this,"_onLogLineFuncs"),Sp(this,"_verbose"),this._onLogLineFuncs={},this._verbose=l.verbose}addLogLineListener(l){let r=Math.random().toString(36).substring(2,15);for(let u=0;u<10&&this._onLogLineFuncs[r]!==void 0;u++)r=Math.random().toString(36).substring(2,15);return this._onLogLineFuncs[r]=l,()=>{delete this._onLogLineFuncs[r]}}logVerbose(...l){if(this._verbose)for(const r of Object.values(this._onLogLineFuncs))r("debug",`${new Date().toISOString()}`,...l)}log(...l){for(const r of Object.values(this._onLogLineFuncs))r("info",...l)}warn(...l){for(const r of Object.values(this._onLogLineFuncs))r("warn",...l)}error(...l){for(const r of Object.values(this._onLogLineFuncs))r("error",...l)}}function ny(a){const l=new ty(a);return l.addLogLineListener((r,...u)=>{switch(r){case"debug":console.debug(...u);break;case"info":console.log(...u);break;case"warn":console.warn(...u);break;case"error":console.error(...u);break;default:console.log(...u)}}),l}function ay(a){return new ty(a)}function zs(a,l,r,u,o){const c=ey(r);if(typeof o=="object"&&(o=`ConvexError ${JSON.stringify(o.errorData,null,2)}`),l==="info"){const d=o.match(/^\[.*?\] /);if(d===null){a.error(`[CONVEX ${c}(${u})] Could not parse console.log`);return}const p=o.slice(1,d[0].length-2),h=o.slice(d[0].length);a.log(`%c[CONVEX ${c}(${u})] [${p}]`,px,h)}else a.error(`[CONVEX ${c}(${u})] ${o}`)}function vx(a,l){const r=`[CONVEX FATAL ERROR] ${l}`;return a.error(r),new Error(r)}function Ai(a,l,r){return`[CONVEX ${ey(a)}(${l})] ${r.errorMessage}
  Called by client`}function bf(a,l){return l.data=a.errorData,l}function qs(a){const l=a.split(":");let r,u;return l.length===1?(r=l[0],u="default"):(r=l.slice(0,l.length-1).join(":"),u=l[l.length-1]),r.endsWith(".js")&&(r=r.slice(0,-3)),`${r}:${u}`}function La(a,l){return JSON.stringify({udfPath:qs(a),args:ha(l)})}var yx=Object.defineProperty,bx=(a,l,r)=>l in a?yx(a,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[l]=r,cn=(a,l,r)=>bx(a,typeof l!="symbol"?l+"":l,r);class Sx{constructor(){cn(this,"nextQueryId"),cn(this,"querySetVersion"),cn(this,"querySet"),cn(this,"queryIdToToken"),cn(this,"identityVersion"),cn(this,"auth"),cn(this,"outstandingQueriesOlderThanRestart"),cn(this,"outstandingAuthOlderThanRestart"),cn(this,"paused"),cn(this,"pendingQuerySetModifications"),this.nextQueryId=0,this.querySetVersion=0,this.identityVersion=0,this.querySet=new Map,this.queryIdToToken=new Map,this.outstandingQueriesOlderThanRestart=new Set,this.outstandingAuthOlderThanRestart=!1,this.paused=!1,this.pendingQuerySetModifications=new Map}hasSyncedPastLastReconnect(){return this.outstandingQueriesOlderThanRestart.size===0&&!this.outstandingAuthOlderThanRestart}markAuthCompletion(){this.outstandingAuthOlderThanRestart=!1}subscribe(l,r,u,o){const c=qs(l),d=La(c,r),p=this.querySet.get(d);if(p!==void 0)return p.numSubscribers+=1,{queryToken:d,modification:null,unsubscribe:()=>this.removeSubscriber(d)};{const h=this.nextQueryId++,m={id:h,canonicalizedUdfPath:c,args:r,numSubscribers:1,journal:u,componentPath:o};this.querySet.set(d,m),this.queryIdToToken.set(h,d);const y=this.querySetVersion,S=this.querySetVersion+1,b={type:"Add",queryId:h,udfPath:c,args:[ha(r)],journal:u,componentPath:o};return this.paused?this.pendingQuerySetModifications.set(h,b):this.querySetVersion=S,{queryToken:d,modification:{type:"ModifyQuerySet",baseVersion:y,newVersion:S,modifications:[b]},unsubscribe:()=>this.removeSubscriber(d)}}}transition(l){for(const r of l.modifications)switch(r.type){case"QueryUpdated":case"QueryFailed":{this.outstandingQueriesOlderThanRestart.delete(r.queryId);const u=r.journal;if(u!==void 0){const o=this.queryIdToToken.get(r.queryId);o!==void 0&&(this.querySet.get(o).journal=u)}break}case"QueryRemoved":{this.outstandingQueriesOlderThanRestart.delete(r.queryId);break}default:throw new Error(`Invalid modification ${r.type}`)}}queryId(l,r){const u=qs(l),o=La(u,r),c=this.querySet.get(o);return c!==void 0?c.id:null}isCurrentOrNewerAuthVersion(l){return l>=this.identityVersion}setAuth(l){this.auth={tokenType:"User",value:l};const r=this.identityVersion;return this.paused||(this.identityVersion=r+1),{type:"Authenticate",baseVersion:r,...this.auth}}setAdminAuth(l,r){const u={tokenType:"Admin",value:l,impersonating:r};this.auth=u;const o=this.identityVersion;return this.paused||(this.identityVersion=o+1),{type:"Authenticate",baseVersion:o,...u}}clearAuth(){this.auth=void 0,this.markAuthCompletion();const l=this.identityVersion;return this.paused||(this.identityVersion=l+1),{type:"Authenticate",tokenType:"None",baseVersion:l}}hasAuth(){return!!this.auth}isNewAuth(l){return this.auth?.value!==l}queryPath(l){const r=this.queryIdToToken.get(l);return r?this.querySet.get(r).canonicalizedUdfPath:null}queryArgs(l){const r=this.queryIdToToken.get(l);return r?this.querySet.get(r).args:null}queryToken(l){return this.queryIdToToken.get(l)??null}queryJournal(l){return this.querySet.get(l)?.journal}restart(l){this.unpause(),this.outstandingQueriesOlderThanRestart.clear();const r=[];for(const c of this.querySet.values()){const d={type:"Add",queryId:c.id,udfPath:c.canonicalizedUdfPath,args:[ha(c.args)],journal:c.journal,componentPath:c.componentPath};r.push(d),l.has(c.id)||this.outstandingQueriesOlderThanRestart.add(c.id)}this.querySetVersion=1;const u={type:"ModifyQuerySet",baseVersion:0,newVersion:1,modifications:r};if(!this.auth)return this.identityVersion=0,[u,void 0];this.outstandingAuthOlderThanRestart=!0;const o={type:"Authenticate",baseVersion:0,...this.auth};return this.identityVersion=1,[u,o]}pause(){this.paused=!0}resume(){const l=this.pendingQuerySetModifications.size>0?{type:"ModifyQuerySet",baseVersion:this.querySetVersion,newVersion:++this.querySetVersion,modifications:Array.from(this.pendingQuerySetModifications.values())}:void 0,r=this.auth!==void 0?{type:"Authenticate",baseVersion:this.identityVersion++,...this.auth}:void 0;return this.unpause(),[l,r]}unpause(){this.paused=!1,this.pendingQuerySetModifications.clear()}removeSubscriber(l){const r=this.querySet.get(l);if(r.numSubscribers>1)return r.numSubscribers-=1,null;{this.querySet.delete(l),this.queryIdToToken.delete(r.id),this.outstandingQueriesOlderThanRestart.delete(r.id);const u=this.querySetVersion,o=this.querySetVersion+1,c={type:"Remove",queryId:r.id};return this.paused?this.pendingQuerySetModifications.has(r.id)?this.pendingQuerySetModifications.delete(r.id):this.pendingQuerySetModifications.set(r.id,c):this.querySetVersion=o,{type:"ModifyQuerySet",baseVersion:u,newVersion:o,modifications:[c]}}}}var Ex=Object.defineProperty,xx=(a,l,r)=>l in a?Ex(a,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[l]=r,As=(a,l,r)=>xx(a,typeof l!="symbol"?l+"":l,r);class _x{constructor(l){this.logger=l,As(this,"inflightRequests"),As(this,"requestsOlderThanRestart"),As(this,"inflightMutationsCount",0),As(this,"inflightActionsCount",0),this.inflightRequests=new Map,this.requestsOlderThanRestart=new Set}request(l,r){return new Promise(o=>{const c=r?"Requested":"NotSent";this.inflightRequests.set(l.requestId,{message:l,status:{status:c,requestedAt:new Date,onResult:o}}),l.type==="Mutation"?this.inflightMutationsCount++:l.type==="Action"&&this.inflightActionsCount++})}onResponse(l){const r=this.inflightRequests.get(l.requestId);if(r===void 0||r.status.status==="Completed")return null;const u=r.message.type==="Mutation"?"mutation":"action",o=r.message.udfPath;for(const h of l.logLines)zs(this.logger,"info",u,o,h);const c=r.status;let d,p;if(l.success)d={success:!0,logLines:l.logLines,value:ji(l.result)},p=()=>c.onResult(d);else{const h=l.result,{errorData:m}=l;zs(this.logger,"error",u,o,h),d={success:!1,errorMessage:h,errorData:m!==void 0?ji(m):void 0,logLines:l.logLines},p=()=>c.onResult(d)}return l.type==="ActionResponse"||!l.success?(p(),this.inflightRequests.delete(l.requestId),this.requestsOlderThanRestart.delete(l.requestId),r.message.type==="Action"?this.inflightActionsCount--:r.message.type==="Mutation"&&this.inflightMutationsCount--,{requestId:l.requestId,result:d}):(r.status={status:"Completed",result:d,ts:l.ts,onResolve:p},null)}removeCompleted(l){const r=new Map;for(const[u,o]of this.inflightRequests.entries()){const c=o.status;c.status==="Completed"&&c.ts.lessThanOrEqual(l)&&(c.onResolve(),r.set(u,c.result),o.message.type==="Mutation"?this.inflightMutationsCount--:o.message.type==="Action"&&this.inflightActionsCount--,this.inflightRequests.delete(u),this.requestsOlderThanRestart.delete(u))}return r}restart(){this.requestsOlderThanRestart=new Set(this.inflightRequests.keys());const l=[];for(const[r,u]of this.inflightRequests){if(u.status.status==="NotSent"){u.status.status="Requested",l.push(u.message);continue}if(u.message.type==="Mutation")l.push(u.message);else if(u.message.type==="Action"){if(this.inflightRequests.delete(r),this.requestsOlderThanRestart.delete(r),this.inflightActionsCount--,u.status.status==="Completed")throw new Error("Action should never be in 'Completed' state");u.status.onResult({success:!1,errorMessage:"Connection lost while action was in flight",logLines:[]})}}return l}resume(){const l=[];for(const[,r]of this.inflightRequests)if(r.status.status==="NotSent"){r.status.status="Requested",l.push(r.message);continue}return l}hasIncompleteRequests(){for(const l of this.inflightRequests.values())if(l.status.status==="Requested")return!0;return!1}hasInflightRequests(){return this.inflightRequests.size>0}hasSyncedPastLastReconnect(){return this.requestsOlderThanRestart.size===0}timeOfOldestInflightRequest(){if(this.inflightRequests.size===0)return null;let l=Date.now();for(const r of this.inflightRequests.values())r.status.status!=="Completed"&&r.status.requestedAt.getTime()<l&&(l=r.status.requestedAt.getTime());return new Date(l)}inflightMutations(){return this.inflightMutationsCount}inflightActions(){return this.inflightActionsCount}}const Kl=Symbol.for("functionName"),wx=Symbol.for("toReferencePath");function Rx(a){return a[wx]??null}function Tx(a){return a.startsWith("function://")}function Cx(a){let l;if(typeof a=="string")Tx(a)?l={functionHandle:a}:l={name:a};else if(a[Kl])l={name:a[Kl]};else{const r=Rx(a);if(!r)throw new Error(`${a} is not a functionReference`);l={reference:r}}return l}function Rt(a){const l=Cx(a);if(l.name===void 0)throw l.functionHandle!==void 0?new Error(`Expected function reference like "api.file.func" or "internal.file.func", but received function handle ${l.functionHandle}`):l.reference!==void 0?new Error(`Expected function reference in the current component like "api.file.func" or "internal.file.func", but received reference ${l.reference}`):new Error(`Expected function reference like "api.file.func" or "internal.file.func", but received ${JSON.stringify(l)}`);if(typeof a=="string")return a;const r=a[Kl];if(!r)throw new Error(`${a} is not a functionReference`);return r}function Df(a){return{[Kl]:a}}function iy(a=[]){const l={get(r,u){if(typeof u=="string"){const o=[...a,u];return iy(o)}else if(u===Kl){if(a.length<2){const d=["api",...a].join(".");throw new Error(`API path is expected to be of the form \`api.moduleName.functionName\`. Found: \`${d}\``)}const o=a.slice(0,-1).join("/"),c=a[a.length-1];return c==="default"?o:o+":"+c}else return u===Symbol.toStringTag?"FunctionReference":void 0}};return new Proxy({},l)}const Ax=iy();var Ox=Object.defineProperty,Mx=(a,l,r)=>l in a?Ox(a,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[l]=r,Bs=(a,l,r)=>Mx(a,typeof l!="symbol"?l+"":l,r);class Il{constructor(l){Bs(this,"queryResults"),Bs(this,"modifiedQueries"),this.queryResults=l,this.modifiedQueries=[]}getQuery(l,...r){const u=Mn(r[0]),o=Rt(l),c=this.queryResults.get(La(o,u));if(c!==void 0)return Il.queryValue(c.result)}getAllQueries(l){const r=[],u=Rt(l);for(const o of this.queryResults.values())o.udfPath===qs(u)&&r.push({args:o.args,value:Il.queryValue(o.result)});return r}setQuery(l,r,u){const o=Mn(r),c=Rt(l),d=La(c,o);let p;u===void 0?p=void 0:p={success:!0,value:u,logLines:[]};const h={udfPath:c,args:o,result:p};this.queryResults.set(d,h),this.modifiedQueries.push(d)}static queryValue(l){if(l!==void 0)return l.success?l.value:void 0}}class jx{constructor(){Bs(this,"queryResults"),Bs(this,"optimisticUpdates"),this.queryResults=new Map,this.optimisticUpdates=[]}ingestQueryResultsFromServer(l,r){this.optimisticUpdates=this.optimisticUpdates.filter(d=>!r.has(d.mutationId));const u=this.queryResults;this.queryResults=new Map(l);const o=new Il(this.queryResults);for(const d of this.optimisticUpdates)d.update(o);const c=[];for(const[d,p]of this.queryResults){const h=u.get(d);(h===void 0||h.result!==p.result)&&c.push(d)}return c}applyOptimisticUpdate(l,r){this.optimisticUpdates.push({update:l,mutationId:r});const u=new Il(this.queryResults);return l(u),u.modifiedQueries}rawQueryResult(l){return this.queryResults.get(l)}queryResult(l){const r=this.queryResults.get(l);if(r===void 0)return;const u=r.result;if(u!==void 0){if(u.success)return u.value;throw u.errorData!==void 0?bf(u,new yf(Ai("query",r.udfPath,u))):new Error(Ai("query",r.udfPath,u))}}hasQueryResult(l){return this.queryResults.get(l)!==void 0}queryLogs(l){return this.queryResults.get(l)?.result?.logLines}}var kx=Object.defineProperty,Ux=(a,l,r)=>l in a?kx(a,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[l]=r,zc=(a,l,r)=>Ux(a,typeof l!="symbol"?l+"":l,r);class Nt{constructor(l,r){zc(this,"low"),zc(this,"high"),zc(this,"__isUnsignedLong__"),this.low=l|0,this.high=r|0,this.__isUnsignedLong__=!0}static isLong(l){return(l&&l.__isUnsignedLong__)===!0}static fromBytesLE(l){return new Nt(l[0]|l[1]<<8|l[2]<<16|l[3]<<24,l[4]|l[5]<<8|l[6]<<16|l[7]<<24)}toBytesLE(){const l=this.high,r=this.low;return[r&255,r>>>8&255,r>>>16&255,r>>>24,l&255,l>>>8&255,l>>>16&255,l>>>24]}static fromNumber(l){return isNaN(l)||l<0?Ep:l>=Nx?Dx:new Nt(l%Ql|0,l/Ql|0)}toString(){return(BigInt(this.high)*BigInt(Ql)+BigInt(this.low)).toString()}equals(l){return Nt.isLong(l)||(l=Nt.fromValue(l)),this.high>>>31===1&&l.high>>>31===1?!1:this.high===l.high&&this.low===l.low}notEquals(l){return!this.equals(l)}comp(l){return Nt.isLong(l)||(l=Nt.fromValue(l)),this.equals(l)?0:l.high>>>0>this.high>>>0||l.high===this.high&&l.low>>>0>this.low>>>0?-1:1}lessThanOrEqual(l){return this.comp(l)<=0}static fromValue(l){return typeof l=="number"?Nt.fromNumber(l):new Nt(l.low,l.high)}}const Ep=new Nt(0,0),xp=65536,Ql=xp*xp,Nx=Ql*Ql,Dx=new Nt(-1,-1);var Lx=Object.defineProperty,zx=(a,l,r)=>l in a?Lx(a,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[l]=r,Os=(a,l,r)=>zx(a,typeof l!="symbol"?l+"":l,r);class _p{constructor(l,r){Os(this,"version"),Os(this,"remoteQuerySet"),Os(this,"queryPath"),Os(this,"logger"),this.version={querySet:0,ts:Nt.fromNumber(0),identity:0},this.remoteQuerySet=new Map,this.queryPath=l,this.logger=r}transition(l){const r=l.startVersion;if(this.version.querySet!==r.querySet||this.version.ts.notEquals(r.ts)||this.version.identity!==r.identity)throw new Error(`Invalid start version: ${r.ts.toString()}:${r.querySet}`);for(const u of l.modifications)switch(u.type){case"QueryUpdated":{const o=this.queryPath(u.queryId);if(o)for(const d of u.logLines)zs(this.logger,"info","query",o,d);const c=ji(u.value??null);this.remoteQuerySet.set(u.queryId,{success:!0,value:c,logLines:u.logLines});break}case"QueryFailed":{const o=this.queryPath(u.queryId);if(o)for(const d of u.logLines)zs(this.logger,"info","query",o,d);const{errorData:c}=u;this.remoteQuerySet.set(u.queryId,{success:!1,errorMessage:u.errorMessage,errorData:c!==void 0?ji(c):void 0,logLines:u.logLines});break}case"QueryRemoved":{this.remoteQuerySet.delete(u.queryId);break}default:throw new Error(`Invalid modification ${u.type}`)}this.version=l.endVersion}remoteQueryResults(){return this.remoteQuerySet}timestamp(){return this.version.ts}}function qc(a){const l=Xl(a);return Nt.fromBytesLE(Array.from(l))}function qx(a){const l=new Uint8Array(a.toBytesLE());return $l(l)}function Bx(a){switch(a.type){case"FatalError":case"AuthError":case"ActionResponse":case"Ping":return{...a};case"MutationResponse":return a.success?{...a,ts:qc(a.ts)}:{...a};case"Transition":return{...a,startVersion:{...a.startVersion,ts:qc(a.startVersion.ts)},endVersion:{...a.endVersion,ts:qc(a.endVersion.ts)}}}}function Hx(a){switch(a.type){case"Authenticate":case"ModifyQuerySet":case"Mutation":case"Action":case"Event":return{...a};case"Connect":return a.maxObservedTimestamp!==void 0?{...a,maxObservedTimestamp:qx(a.maxObservedTimestamp)}:{...a,maxObservedTimestamp:void 0}}}var Vx=Object.defineProperty,Px=(a,l,r)=>l in a?Vx(a,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[l]=r,dt=(a,l,r)=>Px(a,typeof l!="symbol"?l+"":l,r);const Qx=1e3,Yx=1001,Gx=1005,Xx=4040,ly={InternalServerError:{timeout:1e3},SubscriptionsWorkerFullError:{timeout:3e3},TooManyConcurrentRequests:{timeout:3e3},CommitterFullError:{timeout:3e3},AwsTooManyRequestsException:{timeout:3e3},ExecuteFullError:{timeout:3e3},SystemTimeoutError:{timeout:3e3},ExpiredInQueue:{timeout:3e3},VectorIndexesUnavailable:{timeout:1e3},SearchIndexesUnavailable:{timeout:1e3},VectorIndexTooLarge:{timeout:3e3},SearchIndexTooLarge:{timeout:3e3},TooManyWritesInTimePeriod:{timeout:3e3}};function $x(a){if(a===void 0)return"Unknown";for(const l of Object.keys(ly))if(a.startsWith(l))return l;return"Unknown"}class Kx{constructor(l,r,u,o){dt(this,"socket"),dt(this,"connectionCount"),dt(this,"_hasEverConnected",!1),dt(this,"lastCloseReason"),dt(this,"defaultInitialBackoff"),dt(this,"maxBackoff"),dt(this,"retries"),dt(this,"serverInactivityThreshold"),dt(this,"reconnectDueToServerInactivityTimeout"),dt(this,"uri"),dt(this,"onOpen"),dt(this,"onResume"),dt(this,"onMessage"),dt(this,"webSocketConstructor"),dt(this,"logger"),dt(this,"onServerDisconnectError"),this.webSocketConstructor=u,this.socket={state:"disconnected"},this.connectionCount=0,this.lastCloseReason="InitialConnect",this.defaultInitialBackoff=1e3,this.maxBackoff=16e3,this.retries=0,this.serverInactivityThreshold=3e4,this.reconnectDueToServerInactivityTimeout=null,this.uri=l,this.onOpen=r.onOpen,this.onResume=r.onResume,this.onMessage=r.onMessage,this.onServerDisconnectError=r.onServerDisconnectError,this.logger=o,this.connect()}setSocketState(l){this.socket=l,this._logVerbose(`socket state changed: ${this.socket.state}, paused: ${"paused"in this.socket?this.socket.paused:void 0}`)}connect(){if(this.socket.state==="terminated")return;if(this.socket.state!=="disconnected"&&this.socket.state!=="stopped")throw new Error("Didn't start connection from disconnected state: "+this.socket.state);const l=new this.webSocketConstructor(this.uri);this._logVerbose("constructed WebSocket"),this.setSocketState({state:"connecting",ws:l,paused:"no"}),this.resetServerInactivityTimeout(),l.onopen=()=>{if(this.logger.logVerbose("begin ws.onopen"),this.socket.state!=="connecting")throw new Error("onopen called with socket not in connecting state");this.setSocketState({state:"ready",ws:l,paused:this.socket.paused==="yes"?"uninitialized":"no"}),this.resetServerInactivityTimeout(),this.socket.paused==="no"&&(this._hasEverConnected=!0,this.onOpen({connectionCount:this.connectionCount,lastCloseReason:this.lastCloseReason})),this.lastCloseReason!=="InitialConnect"&&this.logger.log("WebSocket reconnected"),this.connectionCount+=1,this.lastCloseReason=null},l.onerror=r=>{const u=r.message;this.logger.log(`WebSocket error: ${u}`)},l.onmessage=r=>{this.resetServerInactivityTimeout();const u=Bx(JSON.parse(r.data));this._logVerbose(`received ws message with type ${u.type}`),this.onMessage(u).hasSyncedPastLastReconnect&&(this.retries=0)},l.onclose=r=>{if(this._logVerbose("begin ws.onclose"),this.lastCloseReason===null&&(this.lastCloseReason=r.reason??"OnCloseInvoked"),r.code!==Qx&&r.code!==Yx&&r.code!==Gx&&r.code!==Xx){let o=`WebSocket closed with code ${r.code}`;r.reason&&(o+=`: ${r.reason}`),this.logger.log(o),this.onServerDisconnectError&&r.reason&&this.onServerDisconnectError(o)}const u=$x(r.reason);this.scheduleReconnect(u)}}socketState(){return this.socket.state}sendMessage(l){const r={type:l.type,...l.type==="Authenticate"&&l.tokenType==="User"?{value:`...${l.value.slice(-7)}`}:{}};if(this.socket.state==="ready"&&this.socket.paused==="no"){const u=Hx(l),o=JSON.stringify(u);try{this.socket.ws.send(o)}catch(c){this.logger.log(`Failed to send message on WebSocket, reconnecting: ${c}`),this.closeAndReconnect("FailedToSendMessage")}return this._logVerbose(`sent message with type ${l.type}: ${JSON.stringify(r)}`),!0}return this._logVerbose(`message not sent (socket state: ${this.socket.state}, paused: ${"paused"in this.socket?this.socket.paused:void 0}): ${JSON.stringify(r)}`),!1}resetServerInactivityTimeout(){this.socket.state!=="terminated"&&(this.reconnectDueToServerInactivityTimeout!==null&&(clearTimeout(this.reconnectDueToServerInactivityTimeout),this.reconnectDueToServerInactivityTimeout=null),this.reconnectDueToServerInactivityTimeout=setTimeout(()=>{this.closeAndReconnect("InactiveServer")},this.serverInactivityThreshold))}scheduleReconnect(l){this.socket={state:"disconnected"};const r=this.nextBackoff(l);this.logger.log(`Attempting reconnect in ${r}ms`),setTimeout(()=>this.connect(),r)}closeAndReconnect(l){switch(this._logVerbose(`begin closeAndReconnect with reason ${l}`),this.socket.state){case"disconnected":case"terminated":case"stopped":return;case"connecting":case"ready":{this.lastCloseReason=l,this.close(),this.scheduleReconnect("client");return}default:this.socket}}close(){switch(this.socket.state){case"disconnected":case"terminated":case"stopped":return Promise.resolve();case"connecting":{const l=this.socket.ws;return new Promise(r=>{l.onclose=()=>{this._logVerbose("Closed after connecting"),r()},l.onopen=()=>{this._logVerbose("Opened after connecting"),l.close()}})}case"ready":{this._logVerbose("ws.close called");const l=this.socket.ws,r=new Promise(u=>{l.onclose=()=>{u()}});return l.close(),r}default:return this.socket,Promise.resolve()}}terminate(){switch(this.reconnectDueToServerInactivityTimeout&&clearTimeout(this.reconnectDueToServerInactivityTimeout),this.socket.state){case"terminated":case"stopped":case"disconnected":case"connecting":case"ready":{const l=this.close();return this.setSocketState({state:"terminated"}),l}default:throw this.socket,new Error(`Invalid websocket state: ${this.socket.state}`)}}stop(){switch(this.socket.state){case"terminated":return Promise.resolve();case"connecting":case"stopped":case"disconnected":case"ready":{const l=this.close();return this.socket={state:"stopped"},l}default:return this.socket,Promise.resolve()}}tryRestart(){switch(this.socket.state){case"stopped":break;case"terminated":case"connecting":case"ready":case"disconnected":this.logger.logVerbose("Restart called without stopping first");return;default:this.socket}this.connect()}pause(){switch(this.socket.state){case"disconnected":case"stopped":case"terminated":return;case"connecting":case"ready":{this.socket={...this.socket,paused:"yes"};return}default:{this.socket;return}}}resume(){switch(this.socket.state){case"connecting":this.socket={...this.socket,paused:"no"};return;case"ready":this.socket.paused==="uninitialized"?(this.socket={...this.socket,paused:"no"},this.onOpen({connectionCount:this.connectionCount,lastCloseReason:this.lastCloseReason})):this.socket.paused==="yes"&&(this.socket={...this.socket,paused:"no"},this.onResume());return;case"terminated":case"stopped":case"disconnected":return;default:this.socket}this.connect()}connectionState(){return{isConnected:this.socket.state==="ready",hasEverConnected:this._hasEverConnected,connectionCount:this.connectionCount,connectionRetries:this.retries}}_logVerbose(l){this.logger.logVerbose(l)}nextBackoff(l){const u=(l==="client"?100:l==="Unknown"?this.defaultInitialBackoff:ly[l].timeout)*Math.pow(2,this.retries);this.retries+=1;const o=Math.min(u,this.maxBackoff),c=o*(Math.random()-.5);return o+c}}function Ix(){return Zx()}function Zx(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,a=>{const l=Math.random()*16|0;return(a==="x"?l:l&3|8).toString(16)})}class ql extends Error{}ql.prototype.name="InvalidTokenError";function Wx(a){return decodeURIComponent(atob(a).replace(/(.)/g,(l,r)=>{let u=r.charCodeAt(0).toString(16).toUpperCase();return u.length<2&&(u="0"+u),"%"+u}))}function Jx(a){let l=a.replace(/-/g,"+").replace(/_/g,"/");switch(l.length%4){case 0:break;case 2:l+="==";break;case 3:l+="=";break;default:throw new Error("base64 string is not of the correct length")}try{return Wx(l)}catch{return atob(l)}}function Fx(a,l){if(typeof a!="string")throw new ql("Invalid token specified: must be a string");l||(l={});const r=l.header===!0?0:1,u=a.split(".")[r];if(typeof u!="string")throw new ql(`Invalid token specified: missing part #${r+1}`);let o;try{o=Jx(u)}catch(c){throw new ql(`Invalid token specified: invalid base64 for part #${r+1} (${c.message})`)}try{return JSON.parse(o)}catch(c){throw new ql(`Invalid token specified: invalid json for part #${r+1} (${c.message})`)}}var e_=Object.defineProperty,t_=(a,l,r)=>l in a?e_(a,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[l]=r,Yt=(a,l,r)=>t_(a,typeof l!="symbol"?l+"":l,r);const n_=20*24*60*60*1e3,wp=2;class a_{constructor(l,r,u){Yt(this,"authState",{state:"noAuth"}),Yt(this,"configVersion",0),Yt(this,"syncState"),Yt(this,"authenticate"),Yt(this,"stopSocket"),Yt(this,"tryRestartSocket"),Yt(this,"pauseSocket"),Yt(this,"resumeSocket"),Yt(this,"clearAuth"),Yt(this,"logger"),Yt(this,"refreshTokenLeewaySeconds"),Yt(this,"tokenConfirmationAttempts",0),this.syncState=l,this.authenticate=r.authenticate,this.stopSocket=r.stopSocket,this.tryRestartSocket=r.tryRestartSocket,this.pauseSocket=r.pauseSocket,this.resumeSocket=r.resumeSocket,this.clearAuth=r.clearAuth,this.logger=u.logger,this.refreshTokenLeewaySeconds=u.refreshTokenLeewaySeconds}async setConfig(l,r){this.resetAuthState(),this._logVerbose("pausing WS for auth token fetch"),this.pauseSocket();const u=await this.fetchTokenAndGuardAgainstRace(l,{forceRefreshToken:!1});u.isFromOutdatedConfig||(u.value?(this.setAuthState({state:"waitingForServerConfirmationOfCachedToken",config:{fetchToken:l,onAuthChange:r},hasRetried:!1}),this.authenticate(u.value)):(this.setAuthState({state:"initialRefetch",config:{fetchToken:l,onAuthChange:r}}),await this.refetchToken()),this._logVerbose("resuming WS after auth token fetch"),this.resumeSocket())}onTransition(l){if(this.syncState.isCurrentOrNewerAuthVersion(l.endVersion.identity)&&!(l.endVersion.identity<=l.startVersion.identity)){if(this.authState.state==="waitingForServerConfirmationOfCachedToken"){this._logVerbose("server confirmed auth token is valid"),this.refetchToken(),this.authState.config.onAuthChange(!0);return}this.authState.state==="waitingForServerConfirmationOfFreshToken"&&(this._logVerbose("server confirmed new auth token is valid"),this.scheduleTokenRefetch(this.authState.token),this.tokenConfirmationAttempts=0,this.authState.hadAuth||this.authState.config.onAuthChange(!0))}}onAuthError(l){if(l.authUpdateAttempted===!1&&(this.authState.state==="waitingForServerConfirmationOfFreshToken"||this.authState.state==="waitingForServerConfirmationOfCachedToken")){this._logVerbose("ignoring non-auth token expired error");return}const{baseVersion:r}=l;if(!this.syncState.isCurrentOrNewerAuthVersion(r+1)){this._logVerbose("ignoring auth error for previous auth attempt");return}this.tryToReauthenticate(l)}async tryToReauthenticate(l){if(this._logVerbose(`attempting to reauthenticate: ${l.error}`),this.authState.state==="noAuth"||this.authState.state==="waitingForServerConfirmationOfFreshToken"&&this.tokenConfirmationAttempts>=wp){this.logger.error(`Failed to authenticate: "${l.error}", check your server auth config`),this.syncState.hasAuth()&&this.syncState.clearAuth(),this.authState.state!=="noAuth"&&this.setAndReportAuthFailed(this.authState.config.onAuthChange);return}this.authState.state==="waitingForServerConfirmationOfFreshToken"&&(this.tokenConfirmationAttempts++,this._logVerbose(`retrying reauthentication, ${wp-this.tokenConfirmationAttempts} attempts remaining`)),await this.stopSocket();const r=await this.fetchTokenAndGuardAgainstRace(this.authState.config.fetchToken,{forceRefreshToken:!0});r.isFromOutdatedConfig||(r.value&&this.syncState.isNewAuth(r.value)?(this.authenticate(r.value),this.setAuthState({state:"waitingForServerConfirmationOfFreshToken",config:this.authState.config,token:r.value,hadAuth:this.authState.state==="notRefetching"||this.authState.state==="waitingForScheduledRefetch"})):(this._logVerbose("reauthentication failed, could not fetch a new token"),this.syncState.hasAuth()&&this.syncState.clearAuth(),this.setAndReportAuthFailed(this.authState.config.onAuthChange)),this.tryRestartSocket())}async refetchToken(){if(this.authState.state==="noAuth")return;this._logVerbose("refetching auth token");const l=await this.fetchTokenAndGuardAgainstRace(this.authState.config.fetchToken,{forceRefreshToken:!0});l.isFromOutdatedConfig||(l.value?this.syncState.isNewAuth(l.value)?(this.setAuthState({state:"waitingForServerConfirmationOfFreshToken",hadAuth:this.syncState.hasAuth(),token:l.value,config:this.authState.config}),this.authenticate(l.value)):this.setAuthState({state:"notRefetching",config:this.authState.config}):(this._logVerbose("refetching token failed"),this.syncState.hasAuth()&&this.clearAuth(),this.setAndReportAuthFailed(this.authState.config.onAuthChange)),this._logVerbose("restarting WS after auth token fetch (if currently stopped)"),this.tryRestartSocket())}scheduleTokenRefetch(l){if(this.authState.state==="noAuth")return;const r=this.decodeToken(l);if(!r){this.logger.error("Auth token is not a valid JWT, cannot refetch the token");return}const{iat:u,exp:o}=r;if(!u||!o){this.logger.error("Auth token does not have required fields, cannot refetch the token");return}const c=o-u;if(c<=2){this.logger.error("Auth token does not live long enough, cannot refetch the token");return}let d=Math.min(n_,(c-this.refreshTokenLeewaySeconds)*1e3);d<=0&&(this.logger.warn(`Refetching auth token immediately, configured leeway ${this.refreshTokenLeewaySeconds}s is larger than the token's lifetime ${c}s`),d=0);const p=setTimeout(()=>{this._logVerbose("running scheduled token refetch"),this.refetchToken()},d);this.setAuthState({state:"waitingForScheduledRefetch",refetchTokenTimeoutId:p,config:this.authState.config}),this._logVerbose(`scheduled preemptive auth token refetching in ${d}ms`)}async fetchTokenAndGuardAgainstRace(l,r){const u=++this.configVersion;this._logVerbose(`fetching token with config version ${u}`);const o=await l(r);return this.configVersion!==u?(this._logVerbose(`stale config version, expected ${u}, got ${this.configVersion}`),{isFromOutdatedConfig:!0}):{isFromOutdatedConfig:!1,value:o}}stop(){this.resetAuthState(),this.configVersion++,this._logVerbose(`config version bumped to ${this.configVersion}`)}setAndReportAuthFailed(l){l(!1),this.resetAuthState()}resetAuthState(){this.setAuthState({state:"noAuth"})}setAuthState(l){const r=l.state==="waitingForServerConfirmationOfFreshToken"?{hadAuth:l.hadAuth,state:l.state,token:`...${l.token.slice(-7)}`}:{state:l.state};switch(this._logVerbose(`setting auth state to ${JSON.stringify(r)}`),l.state){case"waitingForScheduledRefetch":case"notRefetching":case"noAuth":this.tokenConfirmationAttempts=0;break}this.authState.state==="waitingForScheduledRefetch"&&(clearTimeout(this.authState.refetchTokenTimeoutId),this.syncState.markAuthCompletion()),this.authState=l}decodeToken(l){try{return Fx(l)}catch(r){return this._logVerbose(`Error decoding token: ${r instanceof Error?r.message:"Unknown error"}`),null}}_logVerbose(l){this.logger.logVerbose(`${l} [v${this.configVersion}]`)}}const i_=["convexClientConstructed","convexWebSocketOpen","convexFirstMessageReceived"];function l_(a,l){const r={sessionId:l};typeof performance>"u"||!performance.mark||performance.mark(a,{detail:r})}function r_(a){let l=a.name.slice(6);return l=l.charAt(0).toLowerCase()+l.slice(1),{name:l,startTime:a.startTime}}function s_(a){if(typeof performance>"u"||!performance.getEntriesByName)return[];const l=[];for(const r of i_){const u=performance.getEntriesByName(r).filter(o=>o.entryType==="mark").filter(o=>o.detail.sessionId===a);l.push(...u)}return l.map(r_)}var u_=Object.defineProperty,o_=(a,l,r)=>l in a?u_(a,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[l]=r,ht=(a,l,r)=>o_(a,typeof l!="symbol"?l+"":l,r);class c_{constructor(l,r,u){if(ht(this,"address"),ht(this,"state"),ht(this,"requestManager"),ht(this,"webSocketManager"),ht(this,"authenticationManager"),ht(this,"remoteQuerySet"),ht(this,"optimisticQueryResults"),ht(this,"_transitionHandlerCounter",0),ht(this,"_nextRequestId"),ht(this,"_onTransitionFns",new Map),ht(this,"_sessionId"),ht(this,"firstMessageReceived",!1),ht(this,"debug"),ht(this,"logger"),ht(this,"maxObservedTimestamp"),ht(this,"mark",b=>{this.debug&&l_(b,this.sessionId)}),typeof l=="object")throw new Error("Passing a ClientConfig object is no longer supported. Pass the URL of the Convex deployment as a string directly.");u?.skipConvexDeploymentUrlCheck!==!0&&nx(l),u={...u};const o=u.authRefreshTokenLeewaySeconds??2;let c=u.webSocketConstructor;if(!c&&typeof WebSocket>"u")throw new Error("No WebSocket global variable defined! To use Convex in an environment without WebSocket try the HTTP client: https://docs.convex.dev/api/classes/browser.ConvexHttpClient");c=c||WebSocket,this.debug=u.reportDebugInfoToConvex??!1,this.address=l,this.logger=u.logger===!1?ay({verbose:u.verbose??!1}):u.logger!==!0&&u.logger?u.logger:ny({verbose:u.verbose??!1});const d=l.search("://");if(d===-1)throw new Error("Provided address was not an absolute URL.");const p=l.substring(d+3),h=l.substring(0,d);let m;if(h==="http")m="ws";else if(h==="https")m="wss";else throw new Error(`Unknown parent protocol ${h}`);const y=`${m}://${p}/api/${bp}/sync`;this.state=new Sx,this.remoteQuerySet=new _p(b=>this.state.queryPath(b),this.logger),this.requestManager=new _x(this.logger),this.authenticationManager=new a_(this.state,{authenticate:b=>{const x=this.state.setAuth(b);return this.webSocketManager.sendMessage(x),x.baseVersion},stopSocket:()=>this.webSocketManager.stop(),tryRestartSocket:()=>this.webSocketManager.tryRestart(),pauseSocket:()=>{this.webSocketManager.pause(),this.state.pause()},resumeSocket:()=>this.webSocketManager.resume(),clearAuth:()=>{this.clearAuth()}},{logger:this.logger,refreshTokenLeewaySeconds:o}),this.optimisticQueryResults=new jx,this.addOnTransitionHandler(b=>{r(b.queries.map(x=>x.token))}),this._nextRequestId=0,this._sessionId=Ix();const{unsavedChangesWarning:S}=u;if(typeof window>"u"||typeof window.addEventListener>"u"){if(S===!0)throw new Error("unsavedChangesWarning requested, but window.addEventListener not found! Remove {unsavedChangesWarning: true} from Convex client options.")}else S!==!1&&window.addEventListener("beforeunload",b=>{if(this.requestManager.hasIncompleteRequests()){b.preventDefault();const x="Are you sure you want to leave? Your changes may not be saved.";return(b||window.event).returnValue=x,x}});this.webSocketManager=new Kx(y,{onOpen:b=>{this.mark("convexWebSocketOpen"),this.webSocketManager.sendMessage({...b,type:"Connect",sessionId:this._sessionId,maxObservedTimestamp:this.maxObservedTimestamp});const x=new Set(this.remoteQuerySet.remoteQueryResults().keys());this.remoteQuerySet=new _p(L=>this.state.queryPath(L),this.logger);const[_,M]=this.state.restart(x);M&&this.webSocketManager.sendMessage(M),this.webSocketManager.sendMessage(_);for(const L of this.requestManager.restart())this.webSocketManager.sendMessage(L)},onResume:()=>{const[b,x]=this.state.resume();x&&this.webSocketManager.sendMessage(x),b&&this.webSocketManager.sendMessage(b);for(const _ of this.requestManager.resume())this.webSocketManager.sendMessage(_)},onMessage:b=>{switch(this.firstMessageReceived||(this.firstMessageReceived=!0,this.mark("convexFirstMessageReceived"),this.reportMarks()),b.type){case"Transition":{this.observedTimestamp(b.endVersion.ts),this.authenticationManager.onTransition(b),this.remoteQuerySet.transition(b),this.state.transition(b);const x=this.requestManager.removeCompleted(this.remoteQuerySet.timestamp());this.notifyOnQueryResultChanges(x);break}case"MutationResponse":{b.success&&this.observedTimestamp(b.ts);const x=this.requestManager.onResponse(b);x!==null&&this.notifyOnQueryResultChanges(new Map([[x.requestId,x.result]]));break}case"ActionResponse":{this.requestManager.onResponse(b);break}case"AuthError":{this.authenticationManager.onAuthError(b);break}case"FatalError":{const x=vx(this.logger,b.error);throw this.webSocketManager.terminate(),x}}return{hasSyncedPastLastReconnect:this.hasSyncedPastLastReconnect()}},onServerDisconnectError:u.onServerDisconnectError},c,this.logger),this.mark("convexClientConstructed")}hasSyncedPastLastReconnect(){return this.requestManager.hasSyncedPastLastReconnect()||this.state.hasSyncedPastLastReconnect()}observedTimestamp(l){(this.maxObservedTimestamp===void 0||this.maxObservedTimestamp.lessThanOrEqual(l))&&(this.maxObservedTimestamp=l)}getMaxObservedTimestamp(){return this.maxObservedTimestamp}notifyOnQueryResultChanges(l){const r=this.remoteQuerySet.remoteQueryResults(),u=new Map;for(const[c,d]of r){const p=this.state.queryToken(c);if(p!==null){const h={result:d,udfPath:this.state.queryPath(c),args:this.state.queryArgs(c)};u.set(p,h)}}const o=this.optimisticQueryResults.ingestQueryResultsFromServer(u,new Set(l.keys()));this.handleTransition({queries:o.map(c=>{const d=this.optimisticQueryResults.rawQueryResult(c);return{token:c,modification:{kind:"Updated",result:d.result}}}),reflectedMutations:Array.from(l).map(([c,d])=>({requestId:c,result:d})),timestamp:this.remoteQuerySet.timestamp()})}handleTransition(l){for(const r of this._onTransitionFns.values())r(l)}addOnTransitionHandler(l){const r=this._transitionHandlerCounter++;return this._onTransitionFns.set(r,l),()=>this._onTransitionFns.delete(r)}setAuth(l,r){this.authenticationManager.setConfig(l,r)}hasAuth(){return this.state.hasAuth()}setAdminAuth(l,r){const u=this.state.setAdminAuth(l,r);this.webSocketManager.sendMessage(u)}clearAuth(){const l=this.state.clearAuth();this.webSocketManager.sendMessage(l)}subscribe(l,r,u){const o=Mn(r),{modification:c,queryToken:d,unsubscribe:p}=this.state.subscribe(l,o,u?.journal,u?.componentPath);return c!==null&&this.webSocketManager.sendMessage(c),{queryToken:d,unsubscribe:()=>{const h=p();h&&this.webSocketManager.sendMessage(h)}}}localQueryResult(l,r){const u=Mn(r),o=La(l,u);return this.optimisticQueryResults.queryResult(o)}localQueryResultByToken(l){return this.optimisticQueryResults.queryResult(l)}hasLocalQueryResultByToken(l){return this.optimisticQueryResults.hasQueryResult(l)}localQueryLogs(l,r){const u=Mn(r),o=La(l,u);return this.optimisticQueryResults.queryLogs(o)}queryJournal(l,r){const u=Mn(r),o=La(l,u);return this.state.queryJournal(o)}connectionState(){const l=this.webSocketManager.connectionState();return{hasInflightRequests:this.requestManager.hasInflightRequests(),isWebSocketConnected:l.isConnected,hasEverConnected:l.hasEverConnected,connectionCount:l.connectionCount,connectionRetries:l.connectionRetries,timeOfOldestInflightRequest:this.requestManager.timeOfOldestInflightRequest(),inflightMutations:this.requestManager.inflightMutations(),inflightActions:this.requestManager.inflightActions()}}async mutation(l,r,u){const o=await this.mutationInternal(l,r,u);if(!o.success)throw o.errorData!==void 0?bf(o,new yf(Ai("mutation",l,o))):new Error(Ai("mutation",l,o));return o.value}async mutationInternal(l,r,u,o){const{mutationPromise:c}=this.enqueueMutation(l,r,u,o);return c}enqueueMutation(l,r,u,o){const c=Mn(r);this.tryReportLongDisconnect();const d=this.nextRequestId;if(this._nextRequestId++,u!==void 0){const y=u.optimisticUpdate;if(y!==void 0){const S=_=>{y(_,c)instanceof Promise&&this.logger.warn("Optimistic update handler returned a Promise. Optimistic updates should be synchronous.")},x=this.optimisticQueryResults.applyOptimisticUpdate(S,d).map(_=>{const M=this.localQueryResultByToken(_);return{token:_,modification:{kind:"Updated",result:M===void 0?void 0:{success:!0,value:M,logLines:[]}}}});this.handleTransition({queries:x,reflectedMutations:[],timestamp:this.remoteQuerySet.timestamp()})}}const p={type:"Mutation",requestId:d,udfPath:l,componentPath:o,args:[ha(c)]},h=this.webSocketManager.sendMessage(p),m=this.requestManager.request(p,h);return{requestId:d,mutationPromise:m}}async action(l,r){const u=await this.actionInternal(l,r);if(!u.success)throw u.errorData!==void 0?bf(u,new yf(Ai("action",l,u))):new Error(Ai("action",l,u));return u.value}async actionInternal(l,r,u){const o=Mn(r),c=this.nextRequestId;this._nextRequestId++,this.tryReportLongDisconnect();const d={type:"Action",requestId:c,udfPath:l,componentPath:u,args:[ha(o)]},p=this.webSocketManager.sendMessage(d);return this.requestManager.request(d,p)}async close(){return this.authenticationManager.stop(),this.webSocketManager.terminate()}get url(){return this.address}get nextRequestId(){return this._nextRequestId}get sessionId(){return this._sessionId}reportMarks(){if(this.debug){const l=s_(this.sessionId);this.webSocketManager.sendMessage({type:"Event",eventType:"ClientConnect",event:l})}}tryReportLongDisconnect(){if(!this.debug)return;const l=this.connectionState().timeOfOldestInflightRequest;if(l===null||Date.now()-l.getTime()<=60*1e3)return;const r=`${this.address}/api/debug_event`;fetch(r,{method:"POST",headers:{"Content-Type":"application/json","Convex-Client":`npm-${bp}`},body:JSON.stringify({event:"LongWebsocketDisconnect"})}).then(u=>{u.ok||this.logger.warn("Analytics request failed with response:",u.body)}).catch(u=>{this.logger.warn("Analytics response failed with error:",u)})}}var f_=Object.defineProperty,d_=(a,l,r)=>l in a?f_(a,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[l]=r,sa=(a,l,r)=>d_(a,typeof l!="symbol"?l+"":l,r);if(typeof q>"u")throw new Error("Required dependency 'react' not found");function ry(a,l,r){function u(o){return y_(o),l.mutation(a,o,{optimisticUpdate:r})}return u.withOptimisticUpdate=function(c){if(r!==void 0)throw new Error(`Already specified optimistic update for mutation ${Rt(a)}`);return ry(a,l,c)},u}function h_(a,l){return function(r){return l.action(a,r)}}class m_{constructor(l,r){if(sa(this,"address"),sa(this,"cachedSync"),sa(this,"listeners"),sa(this,"options"),sa(this,"closed",!1),sa(this,"_logger"),sa(this,"adminAuth"),sa(this,"fakeUserIdentity"),l===void 0)throw new Error("No address provided to ConvexReactClient.\nIf trying to deploy to production, make sure to follow all the instructions found at https://docs.convex.dev/production/hosting/\nIf running locally, make sure to run `convex dev` and ensure the .env.local file is populated.");if(typeof l!="string")throw new Error(`ConvexReactClient requires a URL like 'https://happy-otter-123.convex.cloud', received something of type ${typeof l} instead.`);if(!l.includes("://"))throw new Error("Provided address was not an absolute URL.");this.address=l,this.listeners=new Map,this._logger=r?.logger===!1?ay({verbose:r?.verbose??!1}):r?.logger!==!0&&r?.logger?r.logger:ny({verbose:r?.verbose??!1}),this.options={...r,logger:this._logger}}get url(){return this.address}get sync(){if(this.closed)throw new Error("ConvexReactClient has already been closed.");return this.cachedSync?this.cachedSync:(this.cachedSync=new c_(this.address,l=>this.transition(l),this.options),this.adminAuth&&this.cachedSync.setAdminAuth(this.adminAuth,this.fakeUserIdentity),this.cachedSync)}setAuth(l,r){if(typeof l=="string")throw new Error("Passing a string to ConvexReactClient.setAuth is no longer supported, please upgrade to passing in an async function to handle reauthentication.");this.sync.setAuth(l,r??(()=>{}))}clearAuth(){this.sync.clearAuth()}setAdminAuth(l,r){if(this.adminAuth=l,this.fakeUserIdentity=r,this.closed)throw new Error("ConvexReactClient has already been closed.");this.cachedSync&&this.sync.setAdminAuth(l,r)}watchQuery(l,...r){const[u,o]=r,c=Rt(l);return{onUpdate:d=>{const{queryToken:p,unsubscribe:h}=this.sync.subscribe(c,u,o),m=this.listeners.get(p);return m!==void 0?m.add(d):this.listeners.set(p,new Set([d])),()=>{if(this.closed)return;const y=this.listeners.get(p);y.delete(d),y.size===0&&this.listeners.delete(p),h()}},localQueryResult:()=>{if(this.cachedSync)return this.cachedSync.localQueryResult(c,u)},localQueryLogs:()=>{if(this.cachedSync)return this.cachedSync.localQueryLogs(c,u)},journal:()=>{if(this.cachedSync)return this.cachedSync.queryJournal(c,u)}}}mutation(l,...r){const[u,o]=r,c=Rt(l);return this.sync.mutation(c,u,o)}action(l,...r){const u=Rt(l);return this.sync.action(u,...r)}query(l,...r){const u=this.watchQuery(l,...r),o=u.localQueryResult();return o!==void 0?Promise.resolve(o):new Promise((c,d)=>{const p=u.onUpdate(()=>{p();try{c(u.localQueryResult())}catch(h){d(h)}})})}connectionState(){return this.sync.connectionState()}get logger(){return this._logger}async close(){if(this.closed=!0,this.listeners=new Map,this.cachedSync){const l=this.cachedSync;this.cachedSync=void 0,await l.close()}}transition(l){for(const r of l){const u=this.listeners.get(r);if(u)for(const o of u)o()}}}const Is=q.createContext(void 0);function g_(){return A.useContext(Is)}const p_=({client:a,children:l})=>q.createElement(Is.Provider,{value:a},l);function Zl(a,...l){const r=l[0]==="skip",u=l[0]==="skip"?{}:Mn(l[0]),o=typeof a=="string"?Df(a):a,c=Rt(o),d=A.useMemo(()=>r?{}:{query:{query:o,args:u}},[JSON.stringify(ha(u)),c,r]),h=__(d).query;if(h instanceof Error)throw h;return h}function sy(a){const l=typeof a=="string"?Df(a):a,r=A.useContext(Is);if(r===void 0)throw new Error("Could not find Convex client! `useMutation` must be used in the React component tree under `ConvexProvider`. Did you forget it? See https://docs.convex.dev/quick-start#set-up-convex-in-your-react-app");return A.useMemo(()=>ry(l,r),[r,Rt(l)])}function v_(a){const l=A.useContext(Is),r=typeof a=="string"?Df(a):a;if(l===void 0)throw new Error("Could not find Convex client! `useAction` must be used in the React component tree under `ConvexProvider`. Did you forget it? See https://docs.convex.dev/quick-start#set-up-convex-in-your-react-app");return A.useMemo(()=>h_(r,l),[l,Rt(r)])}function y_(a){if(typeof a=="object"&&a!==null&&"bubbles"in a&&"persist"in a&&"isDefaultPrevented"in a)throw new Error("Convex function called with SyntheticEvent object. Did you use a Convex function as an event handler directly? Event handlers like onClick receive an event object as their first argument. These SyntheticEvent objects are not valid Convex values. Try wrapping the function like `const handler = () => myMutation();` and using `handler` in the event handler.")}var b_=Object.defineProperty,S_=(a,l,r)=>l in a?b_(a,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[l]=r,Bc=(a,l,r)=>S_(a,typeof l!="symbol"?l+"":l,r);class E_{constructor(l){Bc(this,"createWatch"),Bc(this,"queries"),Bc(this,"listeners"),this.createWatch=l,this.queries={},this.listeners=new Set}setQueries(l){for(const r of Object.keys(l)){const{query:u,args:o}=l[r];if(Rt(u),this.queries[r]===void 0)this.addQuery(r,u,o);else{const c=this.queries[r];(Rt(u)!==Rt(c.query)||JSON.stringify(ha(o))!==JSON.stringify(ha(c.args)))&&(this.removeQuery(r),this.addQuery(r,u,o))}}for(const r of Object.keys(this.queries))l[r]===void 0&&this.removeQuery(r)}subscribe(l){return this.listeners.add(l),()=>{this.listeners.delete(l)}}getLocalResults(l){const r={};for(const u of Object.keys(l)){const{query:o,args:c}=l[u];Rt(o);const d=this.createWatch(o,c);let p;try{p=d.localQueryResult()}catch(h){if(h instanceof Error)p=h;else throw h}r[u]=p}return r}setCreateWatch(l){this.createWatch=l;for(const r of Object.keys(this.queries)){const{query:u,args:o,watch:c}=this.queries[r],d=c.journal();this.removeQuery(r),this.addQuery(r,u,o,d)}}destroy(){for(const l of Object.keys(this.queries))this.removeQuery(l);this.listeners=new Set}addQuery(l,r,u,o){if(this.queries[l]!==void 0)throw new Error(`Tried to add a new query with identifier ${l} when it already exists.`);const c=this.createWatch(r,u,o),d=c.onUpdate(()=>this.notifyListeners());this.queries[l]={query:r,args:u,watch:c,unsubscribe:d}}removeQuery(l){const r=this.queries[l];if(r===void 0)throw new Error(`No query found with identifier ${l}.`);r.unsubscribe(),delete this.queries[l]}notifyListeners(){for(const l of this.listeners)l()}}function x_({getCurrentValue:a,subscribe:l}){const[r,u]=A.useState(()=>({getCurrentValue:a,subscribe:l,value:a()}));let o=r.value;return(r.getCurrentValue!==a||r.subscribe!==l)&&(o=a(),u({getCurrentValue:a,subscribe:l,value:o})),A.useEffect(()=>{let c=!1;const d=()=>{c||u(h=>{if(h.getCurrentValue!==a||h.subscribe!==l)return h;const m=a();return h.value===m?h:{...h,value:m}})},p=l(d);return d(),()=>{c=!0,p()}},[a,l]),o}function __(a){const l=g_();if(l===void 0)throw new Error("Could not find Convex client! `useQuery` must be used in the React component tree under `ConvexProvider`. Did you forget it? See https://docs.convex.dev/quick-start#set-up-convex-in-your-react-app");const r=A.useMemo(()=>(u,o,c)=>l.watchQuery(u,o,{journal:c}),[l]);return w_(a,r)}function w_(a,l){const[r]=A.useState(()=>new E_(l));r.createWatch!==l&&r.setCreateWatch(l),A.useEffect(()=>()=>r.destroy(),[r]);const u=A.useMemo(()=>({getCurrentValue:()=>r.getLocalResults(a),subscribe:o=>(r.setQueries(a),r.subscribe(o))}),[r,a]);return x_(u)}const R_=new m_("https://adorable-marten-321.convex.cloud");function T_({children:a}){return w.jsx(p_,{client:R_,children:a})}function Hc({children:a}){const{isSignedIn:l,isLoaded:r}=hv();return r?l?w.jsx(w.Fragment,{children:a}):w.jsx(o2,{to:"/sign-in",replace:!0}):w.jsx("div",{className:"min-h-screen flex items-center justify-center",children:w.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})})}const da=Ax;function C_(){const{user:a}=ov(),{texts:l}=za(),r=Zl(da.projects.getByUser,a?{userId:a.id}:"skip");return r===void 0?w.jsx("div",{className:"flex justify-center items-center min-h-64",children:w.jsx(Uf,{size:"lg"})}):w.jsxs("div",{children:[w.jsxs("div",{className:"flex justify-between items-center mb-8",children:[w.jsxs("div",{children:[w.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:l.myProjects}),w.jsx("p",{className:"mt-2 text-gray-600",children:l.manageProjects})]}),w.jsx(Jt,{to:"/create-project",children:w.jsx(jn,{children:l.createProject})})]}),r.length===0?w.jsx(kn,{className:"text-center py-12",children:w.jsxs(Un,{children:[w.jsx("div",{className:"mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4",children:w.jsx("svg",{className:"w-12 h-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:w.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),w.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:l.noProjects}),w.jsx("p",{className:"text-gray-600 mb-6",children:l.getStartedMessage}),w.jsx(Jt,{to:"/create-project",children:w.jsx(jn,{children:l.createFirstProject})})]})}):w.jsx("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:r.map(u=>w.jsx(kn,{className:"hover:shadow-md transition-shadow",children:w.jsxs(Un,{children:[w.jsxs("div",{className:"flex justify-between items-start mb-4",children:[w.jsx("h3",{className:"text-lg font-semibold text-gray-900 truncate",children:u.name}),w.jsx("span",{className:"text-xs text-gray-500 ml-2",children:new Date(u.createdAt).toLocaleDateString()})]}),w.jsx("p",{className:"text-gray-600 text-sm mb-4 line-clamp-3",children:u.description}),w.jsxs("div",{className:"flex justify-between items-center",children:[w.jsxs(Jt,{to:`/project/${u._id}`,className:"text-blue-600 hover:text-blue-700 text-sm font-medium",children:[l.viewProject," →"]}),w.jsx(Jt,{to:`/v/${u.sharedId}`,className:"text-gray-500 hover:text-gray-700 text-sm",title:l.publicView,children:w.jsxs("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[w.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),w.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]})})]})]})},u._id))})]})}function A_(){const a=Af(),{user:l}=ov(),{texts:r}=za(),u=sy(da.projects.create),[o,c]=A.useState({name:"",description:""}),[d,p]=A.useState({}),[h,m]=A.useState(!1),y=()=>{const x={};return o.name.trim()?o.name.trim().length<3&&(x.name=r.projectNameMinLength):x.name=r.projectNameRequired,o.description.trim()?o.description.trim().length<10&&(x.description=r.projectDescriptionMinLength):x.description=r.projectDescriptionRequired,p(x),Object.keys(x).length===0},S=async x=>{if(x.preventDefault(),!(!y()||!l)){m(!0);try{const _=await u({name:o.name.trim(),description:o.description.trim(),userId:l.id});a(`/project/${_.projectId}`)}catch(_){console.error("Error creating project:",_),p({submit:r.failedToCreateProject})}finally{m(!1)}}},b=(x,_)=>{c(M=>({...M,[x]:_})),d[x]&&p(M=>({...M,[x]:""}))};return w.jsxs("div",{className:"max-w-2xl mx-auto",children:[w.jsxs("div",{className:"mb-8",children:[w.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:r.createProject}),w.jsx("p",{className:"mt-2 text-gray-600",children:r.startNewProject})]}),w.jsxs(kn,{children:[w.jsx(Xv,{children:w.jsx($v,{children:r.projectDetails})}),w.jsx(Un,{children:w.jsxs("form",{onSubmit:S,className:"space-y-6",children:[w.jsx(G2,{label:r.projectName,type:"text",value:o.name,onChange:x=>b("name",x.target.value),error:d.name,placeholder:r.enterProjectName,required:!0}),w.jsx(Gv,{label:r.description,value:o.description,onChange:x=>b("description",x.target.value),error:d.description,placeholder:r.describeProject,required:!0}),d.submit&&w.jsx("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:w.jsx("p",{className:"text-sm text-red-600",children:d.submit})}),w.jsxs("div",{className:"flex justify-end space-x-4",children:[w.jsx(jn,{type:"button",variant:"secondary",onClick:()=>a("/"),children:r.cancel}),w.jsx(jn,{type:"submit",loading:h,disabled:h,children:r.createProject})]})]})})]})]})}var Ri={},Vc={exports:{}},Pc,Rp;function O_(){if(Rp)return Pc;Rp=1;var a="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";return Pc=a,Pc}var Qc,Tp;function M_(){if(Tp)return Qc;Tp=1;var a=O_();function l(){}function r(){}return r.resetWarningCache=l,Qc=function(){function u(d,p,h,m,y,S){if(S!==a){var b=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw b.name="Invariant Violation",b}}u.isRequired=u;function o(){return u}var c={array:u,bigint:u,bool:u,func:u,number:u,object:u,string:u,symbol:u,any:u,arrayOf:o,element:u,elementType:u,instanceOf:o,node:u,objectOf:o,oneOf:o,oneOfType:o,shape:o,exact:o,checkPropTypes:r,resetWarningCache:l};return c.PropTypes=c,c},Qc}var Cp;function uy(){return Cp||(Cp=1,Vc.exports=M_()()),Vc.exports}var Yc,Ap;function oy(){return Ap||(Ap=1,Yc={L:1,M:0,Q:3,H:2}),Yc}var Gc,Op;function cy(){return Op||(Op=1,Gc={MODE_NUMBER:1,MODE_ALPHA_NUM:2,MODE_8BIT_BYTE:4,MODE_KANJI:8}),Gc}var Xc,Mp;function j_(){if(Mp)return Xc;Mp=1;var a=cy();function l(r){this.mode=a.MODE_8BIT_BYTE,this.data=r}return l.prototype={getLength:function(r){return this.data.length},write:function(r){for(var u=0;u<this.data.length;u++)r.put(this.data.charCodeAt(u),8)}},Xc=l,Xc}var $c,jp;function k_(){if(jp)return $c;jp=1;var a=oy();function l(r,u){this.totalCount=r,this.dataCount=u}return l.RS_BLOCK_TABLE=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]],l.getRSBlocks=function(r,u){var o=l.getRsBlockTable(r,u);if(o==null)throw new Error("bad rs block @ typeNumber:"+r+"/errorCorrectLevel:"+u);for(var c=o.length/3,d=new Array,p=0;p<c;p++)for(var h=o[p*3+0],m=o[p*3+1],y=o[p*3+2],S=0;S<h;S++)d.push(new l(m,y));return d},l.getRsBlockTable=function(r,u){switch(u){case a.L:return l.RS_BLOCK_TABLE[(r-1)*4+0];case a.M:return l.RS_BLOCK_TABLE[(r-1)*4+1];case a.Q:return l.RS_BLOCK_TABLE[(r-1)*4+2];case a.H:return l.RS_BLOCK_TABLE[(r-1)*4+3];default:return}},$c=l,$c}var Kc,kp;function U_(){if(kp)return Kc;kp=1;function a(){this.buffer=new Array,this.length=0}return a.prototype={get:function(l){var r=Math.floor(l/8);return(this.buffer[r]>>>7-l%8&1)==1},put:function(l,r){for(var u=0;u<r;u++)this.putBit((l>>>r-u-1&1)==1)},getLengthInBits:function(){return this.length},putBit:function(l){var r=Math.floor(this.length/8);this.buffer.length<=r&&this.buffer.push(0),l&&(this.buffer[r]|=128>>>this.length%8),this.length++}},Kc=a,Kc}var Ic,Up;function fy(){if(Up)return Ic;Up=1;for(var a={glog:function(r){if(r<1)throw new Error("glog("+r+")");return a.LOG_TABLE[r]},gexp:function(r){for(;r<0;)r+=255;for(;r>=256;)r-=255;return a.EXP_TABLE[r]},EXP_TABLE:new Array(256),LOG_TABLE:new Array(256)},l=0;l<8;l++)a.EXP_TABLE[l]=1<<l;for(var l=8;l<256;l++)a.EXP_TABLE[l]=a.EXP_TABLE[l-4]^a.EXP_TABLE[l-5]^a.EXP_TABLE[l-6]^a.EXP_TABLE[l-8];for(var l=0;l<255;l++)a.LOG_TABLE[a.EXP_TABLE[l]]=l;return Ic=a,Ic}var Zc,Np;function dy(){if(Np)return Zc;Np=1;var a=fy();function l(r,u){if(r.length==null)throw new Error(r.length+"/"+u);for(var o=0;o<r.length&&r[o]==0;)o++;this.num=new Array(r.length-o+u);for(var c=0;c<r.length-o;c++)this.num[c]=r[c+o]}return l.prototype={get:function(r){return this.num[r]},getLength:function(){return this.num.length},multiply:function(r){for(var u=new Array(this.getLength()+r.getLength()-1),o=0;o<this.getLength();o++)for(var c=0;c<r.getLength();c++)u[o+c]^=a.gexp(a.glog(this.get(o))+a.glog(r.get(c)));return new l(u,0)},mod:function(r){if(this.getLength()-r.getLength()<0)return this;for(var u=a.glog(this.get(0))-a.glog(r.get(0)),o=new Array(this.getLength()),c=0;c<this.getLength();c++)o[c]=this.get(c);for(var c=0;c<r.getLength();c++)o[c]^=a.gexp(a.glog(r.get(c))+u);return new l(o,0).mod(r)}},Zc=l,Zc}var Wc,Dp;function N_(){if(Dp)return Wc;Dp=1;var a=cy(),l=dy(),r=fy(),u={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7},o={PATTERN_POSITION_TABLE:[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],G15:1335,G18:7973,G15_MASK:21522,getBCHTypeInfo:function(c){for(var d=c<<10;o.getBCHDigit(d)-o.getBCHDigit(o.G15)>=0;)d^=o.G15<<o.getBCHDigit(d)-o.getBCHDigit(o.G15);return(c<<10|d)^o.G15_MASK},getBCHTypeNumber:function(c){for(var d=c<<12;o.getBCHDigit(d)-o.getBCHDigit(o.G18)>=0;)d^=o.G18<<o.getBCHDigit(d)-o.getBCHDigit(o.G18);return c<<12|d},getBCHDigit:function(c){for(var d=0;c!=0;)d++,c>>>=1;return d},getPatternPosition:function(c){return o.PATTERN_POSITION_TABLE[c-1]},getMask:function(c,d,p){switch(c){case u.PATTERN000:return(d+p)%2==0;case u.PATTERN001:return d%2==0;case u.PATTERN010:return p%3==0;case u.PATTERN011:return(d+p)%3==0;case u.PATTERN100:return(Math.floor(d/2)+Math.floor(p/3))%2==0;case u.PATTERN101:return d*p%2+d*p%3==0;case u.PATTERN110:return(d*p%2+d*p%3)%2==0;case u.PATTERN111:return(d*p%3+(d+p)%2)%2==0;default:throw new Error("bad maskPattern:"+c)}},getErrorCorrectPolynomial:function(c){for(var d=new l([1],0),p=0;p<c;p++)d=d.multiply(new l([1,r.gexp(p)],0));return d},getLengthInBits:function(c,d){if(1<=d&&d<10)switch(c){case a.MODE_NUMBER:return 10;case a.MODE_ALPHA_NUM:return 9;case a.MODE_8BIT_BYTE:return 8;case a.MODE_KANJI:return 8;default:throw new Error("mode:"+c)}else if(d<27)switch(c){case a.MODE_NUMBER:return 12;case a.MODE_ALPHA_NUM:return 11;case a.MODE_8BIT_BYTE:return 16;case a.MODE_KANJI:return 10;default:throw new Error("mode:"+c)}else if(d<41)switch(c){case a.MODE_NUMBER:return 14;case a.MODE_ALPHA_NUM:return 13;case a.MODE_8BIT_BYTE:return 16;case a.MODE_KANJI:return 12;default:throw new Error("mode:"+c)}else throw new Error("type:"+d)},getLostPoint:function(c){for(var d=c.getModuleCount(),p=0,h=0;h<d;h++)for(var m=0;m<d;m++){for(var y=0,S=c.isDark(h,m),b=-1;b<=1;b++)if(!(h+b<0||d<=h+b))for(var x=-1;x<=1;x++)m+x<0||d<=m+x||b==0&&x==0||S==c.isDark(h+b,m+x)&&y++;y>5&&(p+=3+y-5)}for(var h=0;h<d-1;h++)for(var m=0;m<d-1;m++){var _=0;c.isDark(h,m)&&_++,c.isDark(h+1,m)&&_++,c.isDark(h,m+1)&&_++,c.isDark(h+1,m+1)&&_++,(_==0||_==4)&&(p+=3)}for(var h=0;h<d;h++)for(var m=0;m<d-6;m++)c.isDark(h,m)&&!c.isDark(h,m+1)&&c.isDark(h,m+2)&&c.isDark(h,m+3)&&c.isDark(h,m+4)&&!c.isDark(h,m+5)&&c.isDark(h,m+6)&&(p+=40);for(var m=0;m<d;m++)for(var h=0;h<d-6;h++)c.isDark(h,m)&&!c.isDark(h+1,m)&&c.isDark(h+2,m)&&c.isDark(h+3,m)&&c.isDark(h+4,m)&&!c.isDark(h+5,m)&&c.isDark(h+6,m)&&(p+=40);for(var M=0,m=0;m<d;m++)for(var h=0;h<d;h++)c.isDark(h,m)&&M++;var L=Math.abs(100*M/d/d-50)/5;return p+=L*10,p}};return Wc=o,Wc}var Jc,Lp;function D_(){if(Lp)return Jc;Lp=1;var a=j_(),l=k_(),r=U_(),u=N_(),o=dy();function c(p,h){this.typeNumber=p,this.errorCorrectLevel=h,this.modules=null,this.moduleCount=0,this.dataCache=null,this.dataList=[]}var d=c.prototype;return d.addData=function(p){var h=new a(p);this.dataList.push(h),this.dataCache=null},d.isDark=function(p,h){if(p<0||this.moduleCount<=p||h<0||this.moduleCount<=h)throw new Error(p+","+h);return this.modules[p][h]},d.getModuleCount=function(){return this.moduleCount},d.make=function(){if(this.typeNumber<1){var p=1;for(p=1;p<40;p++){for(var h=l.getRSBlocks(p,this.errorCorrectLevel),m=new r,y=0,S=0;S<h.length;S++)y+=h[S].dataCount;for(var S=0;S<this.dataList.length;S++){var b=this.dataList[S];m.put(b.mode,4),m.put(b.getLength(),u.getLengthInBits(b.mode,p)),b.write(m)}if(m.getLengthInBits()<=y*8)break}this.typeNumber=p}this.makeImpl(!1,this.getBestMaskPattern())},d.makeImpl=function(p,h){this.moduleCount=this.typeNumber*4+17,this.modules=new Array(this.moduleCount);for(var m=0;m<this.moduleCount;m++){this.modules[m]=new Array(this.moduleCount);for(var y=0;y<this.moduleCount;y++)this.modules[m][y]=null}this.setupPositionProbePattern(0,0),this.setupPositionProbePattern(this.moduleCount-7,0),this.setupPositionProbePattern(0,this.moduleCount-7),this.setupPositionAdjustPattern(),this.setupTimingPattern(),this.setupTypeInfo(p,h),this.typeNumber>=7&&this.setupTypeNumber(p),this.dataCache==null&&(this.dataCache=c.createData(this.typeNumber,this.errorCorrectLevel,this.dataList)),this.mapData(this.dataCache,h)},d.setupPositionProbePattern=function(p,h){for(var m=-1;m<=7;m++)if(!(p+m<=-1||this.moduleCount<=p+m))for(var y=-1;y<=7;y++)h+y<=-1||this.moduleCount<=h+y||(0<=m&&m<=6&&(y==0||y==6)||0<=y&&y<=6&&(m==0||m==6)||2<=m&&m<=4&&2<=y&&y<=4?this.modules[p+m][h+y]=!0:this.modules[p+m][h+y]=!1)},d.getBestMaskPattern=function(){for(var p=0,h=0,m=0;m<8;m++){this.makeImpl(!0,m);var y=u.getLostPoint(this);(m==0||p>y)&&(p=y,h=m)}return h},d.createMovieClip=function(p,h,m){var y=p.createEmptyMovieClip(h,m),S=1;this.make();for(var b=0;b<this.modules.length;b++)for(var x=b*S,_=0;_<this.modules[b].length;_++){var M=_*S,L=this.modules[b][_];L&&(y.beginFill(0,100),y.moveTo(M,x),y.lineTo(M+S,x),y.lineTo(M+S,x+S),y.lineTo(M,x+S),y.endFill())}return y},d.setupTimingPattern=function(){for(var p=8;p<this.moduleCount-8;p++)this.modules[p][6]==null&&(this.modules[p][6]=p%2==0);for(var h=8;h<this.moduleCount-8;h++)this.modules[6][h]==null&&(this.modules[6][h]=h%2==0)},d.setupPositionAdjustPattern=function(){for(var p=u.getPatternPosition(this.typeNumber),h=0;h<p.length;h++)for(var m=0;m<p.length;m++){var y=p[h],S=p[m];if(this.modules[y][S]==null)for(var b=-2;b<=2;b++)for(var x=-2;x<=2;x++)b==-2||b==2||x==-2||x==2||b==0&&x==0?this.modules[y+b][S+x]=!0:this.modules[y+b][S+x]=!1}},d.setupTypeNumber=function(p){for(var h=u.getBCHTypeNumber(this.typeNumber),m=0;m<18;m++){var y=!p&&(h>>m&1)==1;this.modules[Math.floor(m/3)][m%3+this.moduleCount-8-3]=y}for(var m=0;m<18;m++){var y=!p&&(h>>m&1)==1;this.modules[m%3+this.moduleCount-8-3][Math.floor(m/3)]=y}},d.setupTypeInfo=function(p,h){for(var m=this.errorCorrectLevel<<3|h,y=u.getBCHTypeInfo(m),S=0;S<15;S++){var b=!p&&(y>>S&1)==1;S<6?this.modules[S][8]=b:S<8?this.modules[S+1][8]=b:this.modules[this.moduleCount-15+S][8]=b}for(var S=0;S<15;S++){var b=!p&&(y>>S&1)==1;S<8?this.modules[8][this.moduleCount-S-1]=b:S<9?this.modules[8][15-S-1+1]=b:this.modules[8][15-S-1]=b}this.modules[this.moduleCount-8][8]=!p},d.mapData=function(p,h){for(var m=-1,y=this.moduleCount-1,S=7,b=0,x=this.moduleCount-1;x>0;x-=2)for(x==6&&x--;;){for(var _=0;_<2;_++)if(this.modules[y][x-_]==null){var M=!1;b<p.length&&(M=(p[b]>>>S&1)==1);var L=u.getMask(h,y,x-_);L&&(M=!M),this.modules[y][x-_]=M,S--,S==-1&&(b++,S=7)}if(y+=m,y<0||this.moduleCount<=y){y-=m,m=-m;break}}},c.PAD0=236,c.PAD1=17,c.createData=function(p,h,m){for(var y=l.getRSBlocks(p,h),S=new r,b=0;b<m.length;b++){var x=m[b];S.put(x.mode,4),S.put(x.getLength(),u.getLengthInBits(x.mode,p)),x.write(S)}for(var _=0,b=0;b<y.length;b++)_+=y[b].dataCount;if(S.getLengthInBits()>_*8)throw new Error("code length overflow. ("+S.getLengthInBits()+">"+_*8+")");for(S.getLengthInBits()+4<=_*8&&S.put(0,4);S.getLengthInBits()%8!=0;)S.putBit(!1);for(;!(S.getLengthInBits()>=_*8||(S.put(c.PAD0,8),S.getLengthInBits()>=_*8));)S.put(c.PAD1,8);return c.createBytes(S,y)},c.createBytes=function(p,h){for(var m=0,y=0,S=0,b=new Array(h.length),x=new Array(h.length),_=0;_<h.length;_++){var M=h[_].dataCount,L=h[_].totalCount-M;y=Math.max(y,M),S=Math.max(S,L),b[_]=new Array(M);for(var T=0;T<b[_].length;T++)b[_][T]=255&p.buffer[T+m];m+=M;var P=u.getErrorCorrectPolynomial(L),G=new o(b[_],P.getLength()-1),J=G.mod(P);x[_]=new Array(P.getLength()-1);for(var T=0;T<x[_].length;T++){var Q=T+J.getLength()-x[_].length;x[_][T]=Q>=0?J.get(Q):0}}for(var F=0,T=0;T<h.length;T++)F+=h[T].totalCount;for(var I=new Array(F),$=0,T=0;T<y;T++)for(var _=0;_<h.length;_++)T<b[_].length&&(I[$++]=b[_][T]);for(var T=0;T<S;T++)for(var _=0;_<h.length;_++)T<x[_].length&&(I[$++]=x[_][T]);return I},Jc=c,Jc}var Ms={},zp;function L_(){if(zp)return Ms;zp=1,Object.defineProperty(Ms,"__esModule",{value:!0});var a=Object.assign||function(m){for(var y=1;y<arguments.length;y++){var S=arguments[y];for(var b in S)Object.prototype.hasOwnProperty.call(S,b)&&(m[b]=S[b])}return m},l=uy(),r=c(l),u=ki(),o=c(u);function c(m){return m&&m.__esModule?m:{default:m}}function d(m,y){var S={};for(var b in m)y.indexOf(b)>=0||Object.prototype.hasOwnProperty.call(m,b)&&(S[b]=m[b]);return S}var p={bgColor:r.default.oneOfType([r.default.object,r.default.string]).isRequired,bgD:r.default.string.isRequired,fgColor:r.default.oneOfType([r.default.object,r.default.string]).isRequired,fgD:r.default.string.isRequired,size:r.default.number.isRequired,title:r.default.string,viewBoxSize:r.default.number.isRequired,xmlns:r.default.string},h=(0,u.forwardRef)(function(m,y){var S=m.bgColor,b=m.bgD,x=m.fgD,_=m.fgColor,M=m.size,L=m.title,T=m.viewBoxSize,P=m.xmlns,G=P===void 0?"http://www.w3.org/2000/svg":P,J=d(m,["bgColor","bgD","fgD","fgColor","size","title","viewBoxSize","xmlns"]);return o.default.createElement("svg",a({},J,{height:M,ref:y,viewBox:"0 0 "+T+" "+T,width:M,xmlns:G}),L?o.default.createElement("title",null,L):null,o.default.createElement("path",{d:b,fill:S}),o.default.createElement("path",{d:x,fill:_}))});return h.displayName="QRCodeSvg",h.propTypes=p,Ms.default=h,Ms}var qp;function z_(){if(qp)return Ri;qp=1,Object.defineProperty(Ri,"__esModule",{value:!0}),Ri.QRCode=void 0;var a=Object.assign||function(M){for(var L=1;L<arguments.length;L++){var T=arguments[L];for(var P in T)Object.prototype.hasOwnProperty.call(T,P)&&(M[P]=T[P])}return M},l=uy(),r=S(l),u=oy(),o=S(u),c=D_(),d=S(c),p=ki(),h=S(p),m=L_(),y=S(m);function S(M){return M&&M.__esModule?M:{default:M}}function b(M,L){var T={};for(var P in M)L.indexOf(P)>=0||Object.prototype.hasOwnProperty.call(M,P)&&(T[P]=M[P]);return T}var x={bgColor:r.default.oneOfType([r.default.object,r.default.string]),fgColor:r.default.oneOfType([r.default.object,r.default.string]),level:r.default.string,size:r.default.number,value:r.default.string.isRequired},_=(0,p.forwardRef)(function(M,L){var T=M.bgColor,P=T===void 0?"#FFFFFF":T,G=M.fgColor,J=G===void 0?"#000000":G,Q=M.level,F=Q===void 0?"L":Q,I=M.size,$=I===void 0?256:I,re=M.value,ne=b(M,["bgColor","fgColor","level","size","value"]),pe=new d.default(-1,o.default[F]);pe.addData(re),pe.make();var ge=pe.modules;return h.default.createElement(y.default,a({},ne,{bgColor:P,bgD:ge.map(function(Te,Me){return Te.map(function(Se,D){return Se?"":"M "+D+" "+Me+" l 1 0 0 1 -1 0 Z"}).join(" ")}).join(" "),fgColor:J,fgD:ge.map(function(Te,Me){return Te.map(function(Se,D){return Se?"M "+D+" "+Me+" l 1 0 0 1 -1 0 Z":""}).join(" ")}).join(" "),ref:L,size:$,viewBoxSize:ge.length}))});return Ri.QRCode=_,_.displayName="QRCode",_.propTypes=x,Ri.default=_,Ri}var q_=z_();const B_=Bp(q_);function H_(){const{projectId:a}=zv(),{texts:l}=za(),r=A.useRef(null),u=Zl(da.projects.getById,a?{projectId:a}:"skip"),o=Zl(da.logs.getByProject,a?{projectId:a}:"skip"),c=sy(da.logs.create),d=v_(da.ai.generateImageCaption),[p,h]=A.useState({text:"",imageUrl:""}),[m,y]=A.useState(!1),[S,b]=A.useState(!1),[x,_]=A.useState(!1),[M,L]=A.useState(null),[T,P]=A.useState(""),G=I=>{const $=I.target.files?.[0];if($){L($);const re=new FileReader;re.onload=ne=>{P(ne.target?.result)},re.readAsDataURL($)}},J=async()=>{if(T){b(!0);try{const I=await d({imageBase64:T});h($=>({...$,text:$.text?`${$.text}

${I.caption}`:I.caption}))}catch(I){console.error("Error generating caption:",I),alert(l.failedToGenerateCaption)}finally{b(!1)}}},Q=async I=>{if(I.preventDefault(),!(!p.text.trim()||!a)){y(!0);try{await c({projectId:a,text:p.text.trim(),imageUrl:T||void 0}),h({text:"",imageUrl:""}),L(null),P(""),r.current&&(r.current.value="")}catch($){console.error("Error creating log:",$)}finally{y(!1)}}};if(u===void 0||o===void 0)return w.jsx("div",{className:"flex justify-center items-center min-h-64",children:w.jsx(Uf,{size:"lg"})});if(!u)return w.jsxs("div",{className:"text-center py-12",children:[w.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:l.projectNotFound}),w.jsx(Jt,{to:"/",children:w.jsx(jn,{children:l.backToDashboard})})]});const F=`${window.location.origin}/v/${u.sharedId}`;return w.jsxs("div",{className:"max-w-4xl mx-auto",children:[w.jsxs("div",{className:"mb-8",children:[w.jsxs("div",{className:"flex justify-between items-start mb-4",children:[w.jsxs("div",{children:[w.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:u.name}),w.jsx("p",{className:"mt-2 text-gray-600",children:u.description})]}),w.jsxs("div",{className:"flex space-x-2",children:[w.jsx(jn,{variant:"secondary",onClick:()=>_(!x),children:x?l.hideQR:l.showQR}),w.jsx(Jt,{to:F,target:"_blank",children:w.jsx(jn,{variant:"secondary",children:l.publicView})})]})]}),x&&w.jsx(kn,{className:"mb-6",children:w.jsxs(Un,{className:"text-center",children:[w.jsx("div",{className:"inline-block p-4 bg-white",children:w.jsx(B_,{value:F,size:200})}),w.jsx("p",{className:"mt-2 text-sm text-gray-600",children:l.scanToView})]})})]}),w.jsxs(kn,{className:"mb-8",children:[w.jsx(Xv,{children:w.jsx($v,{children:l.addNewLogEntry})}),w.jsx(Un,{children:w.jsxs("form",{onSubmit:Q,className:"space-y-4",children:[w.jsx(Gv,{label:l.logEntry,value:p.text,onChange:I=>h($=>({...$,text:I.target.value})),placeholder:l.describeWork,required:!0}),w.jsxs("div",{children:[w.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:l.attachImage}),w.jsx("input",{ref:r,type:"file",accept:"image/*",onChange:G,className:"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"})]}),T&&w.jsx("div",{className:"mt-4",children:w.jsx("img",{src:T,alt:l.preview,className:"max-w-xs h-auto rounded-md border"})}),w.jsxs("div",{className:"flex justify-end space-x-2",children:[w.jsx(jn,{type:"button",variant:"secondary",onClick:J,loading:S,disabled:!M||S,children:l.generateCaption}),w.jsx(jn,{type:"submit",loading:m,disabled:m||!p.text.trim(),children:l.addLogEntry})]})]})})]}),w.jsxs("div",{className:"space-y-6",children:[w.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:l.logEntries}),o.length===0?w.jsx(kn,{children:w.jsx(Un,{className:"text-center py-8",children:w.jsx("p",{className:"text-gray-600",children:l.noLogEntries})})}):o.map(I=>w.jsx(kn,{children:w.jsxs(Un,{children:[w.jsx("div",{className:"flex justify-between items-start mb-3",children:w.jsx("span",{className:"text-sm text-gray-500",children:new Date(I.createdAt).toLocaleString()})}),w.jsx("p",{className:"text-gray-900 mb-4 whitespace-pre-wrap",children:I.text}),I.imageUrl&&w.jsx("img",{src:I.imageUrl,alt:l.logAttachment,className:"max-w-md h-auto rounded-md border"})]})},I._id))]})]})}function V_(){const{sharedId:a}=zv(),{texts:l}=za(),r=Zl(da.projects.getBySharedId,a?{sharedId:a}:"skip"),u=Zl(da.logs.getByProjectSharedId,a?{sharedId:a}:"skip");return r===void 0||u===void 0?w.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:w.jsx(Uf,{size:"lg"})}):r?w.jsxs("div",{className:"min-h-screen bg-gray-50",children:[w.jsx("header",{className:"bg-white shadow-sm border-b",children:w.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:w.jsxs("div",{className:"flex justify-between items-center h-16",children:[w.jsxs("div",{className:"flex items-center",children:[w.jsx("span",{className:"text-xl font-bold text-gray-900",children:l.appTitle}),w.jsx("span",{className:"ml-2 text-gray-400",children:"•"}),w.jsx("span",{className:"ml-2 text-gray-600",children:l.publicView})]}),w.jsx("div",{className:"text-sm text-gray-500",children:l.readOnlyView})]})})}),w.jsxs("main",{className:"max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8",children:[w.jsxs("div",{className:"mb-8",children:[w.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:r.name}),w.jsx("p",{className:"text-gray-600 text-lg",children:r.description}),w.jsxs("div",{className:"mt-4 text-sm text-gray-500",children:[l.createdOn," ",new Date(r.createdAt).toLocaleDateString()]})]}),w.jsxs("div",{className:"space-y-6",children:[w.jsxs("div",{className:"flex items-center justify-between",children:[w.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:l.projectLog}),w.jsxs("span",{className:"text-sm text-gray-500",children:[u.length," ",u.length===1?l.entry:l.entries]})]}),u.length===0?w.jsx(kn,{children:w.jsxs(Un,{className:"text-center py-12",children:[w.jsx("div",{className:"mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4",children:w.jsx("svg",{className:"w-8 h-8 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:w.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),w.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:l.noLogEntries}),w.jsx("p",{className:"text-gray-600",children:l.noLogEntriesPublic})]})}):u.map(o=>w.jsx(kn,{children:w.jsxs(Un,{children:[w.jsx("div",{className:"flex justify-between items-start mb-4",children:w.jsx("span",{className:"text-sm text-gray-500",children:new Date(o.createdAt).toLocaleString()})}),w.jsx("div",{className:"prose prose-sm max-w-none",children:w.jsx("p",{className:"text-gray-900 whitespace-pre-wrap leading-relaxed",children:o.text})}),o.imageUrl&&w.jsx("div",{className:"mt-4",children:w.jsx("img",{src:o.imageUrl,alt:l.logAttachment,className:"max-w-full h-auto rounded-lg border shadow-sm"})})]})},o._id))]}),w.jsx("div",{className:"mt-12 pt-8 border-t border-gray-200 text-center",children:w.jsxs("p",{className:"text-sm text-gray-500",children:[l.poweredBy," ",w.jsx("span",{className:"font-medium",children:l.appTitle})," - ",l.professionalJobLogging]})})]})]}):w.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:w.jsxs("div",{className:"text-center",children:[w.jsx("div",{className:"mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4",children:w.jsx("svg",{className:"w-12 h-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:w.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),w.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:l.projectNotFound}),w.jsx("p",{className:"text-gray-600",children:l.projectNotFoundMessage})]})})}function P_(){const{texts:a}=za();return w.jsxs("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[w.jsx("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:w.jsxs("div",{className:"text-center",children:[w.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:a.appTitle}),w.jsx("h2",{className:"text-xl text-gray-600",children:a.signInToAccount})]})}),w.jsx("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:w.jsx("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:w.jsx(sE,{routing:"path",path:"/sign-in",signUpUrl:"/sign-up",redirectUrl:"/"})})})]})}function Q_(){const{texts:a}=za();return w.jsxs("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[w.jsx("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:w.jsxs("div",{className:"text-center",children:[w.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:a.appTitle}),w.jsx("h2",{className:"text-xl text-gray-600",children:a.createAccount})]})}),w.jsx("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:w.jsx("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:w.jsx(uE,{routing:"path",path:"/sign-up",signInUrl:"/sign-in",redirectUrl:"/"})})})]})}function Y_(){return w.jsx(T_,{children:w.jsx(I2,{defaultLocale:"nb",children:w.jsx(D2,{children:w.jsxs(f2,{children:[w.jsx(Da,{path:"/sign-in/*",element:w.jsx(P_,{})}),w.jsx(Da,{path:"/sign-up/*",element:w.jsx(Q_,{})}),w.jsx(Da,{path:"/v/:sharedId",element:w.jsx(V_,{})}),w.jsx(Da,{path:"/",element:w.jsx(Hc,{children:w.jsx(Uc,{children:w.jsx(C_,{})})})}),w.jsx(Da,{path:"/create-project",element:w.jsx(Hc,{children:w.jsx(Uc,{children:w.jsx(A_,{})})})}),w.jsx(Da,{path:"/project/:projectId",element:w.jsx(Hc,{children:w.jsx(Uc,{children:w.jsx(H_,{})})})})]})})})})}const G_="pk_test_bG92ZWQtZG9yeS04Ni5jbGVyay5hY2NvdW50cy5kZXYk";z1.createRoot(document.getElementById("root")).render(w.jsx(q.StrictMode,{children:w.jsx(Ov,{publishableKey:G_,afterSignOutUrl:"/",children:w.jsx(Y_,{})})}));

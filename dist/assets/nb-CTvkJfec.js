const e="JobbLogg.no",t="Dashbord",o="Opprett nytt prosjekt",n="Prosjektnavn",r="Beskrivelse (valgfritt)",s="Lagre",g="Av<PERSON>ry<PERSON>",i="Legg til logg",c="Loggtekst",a="Last opp bilde",p="Generer bildetekst",l="Vis prosjekt",d="Del prosjekt",k="Skann QR-kode",j="Offentlig visning",v="Laster...",b="Noe gikk galt, prøv igjen.",f="Logg inn",L="Registrer deg",P="Logg ut",u="Mine prosjekter",m="Du har ingen prosjekter ennå",h="Opprett ditt første prosjekt",y="Prosjekt opprettet",D="Logg lagt til",w="Bilde lastet opp",N="<PERSON>rerer bildetekst…",R="Skriv inn loggtekst her…",S="Skriv inn prosjektnavn",T="Skriv inn beskrivelse (valgfritt)",O="Administrer dine jobbloggprosjekter og følg fremgangen din.",A="Kom i gang ved å opprette ditt første jobbloggprosjekt.",C="Start et nytt jobbloggprosjekt for å følge arbeidsframgangen din.",E="Prosjektdetaljer",Q="Prosjektnavn er påkrevd",B="Prosjektnavn må være minst 3 tegn",I="Prosjektbeskrivelse er påkrevd",M="Prosjektbeskrivelse må være minst 10 tegn",V="Kunne ikke opprette prosjekt. Prøv igjen.",F="Beskriv prosjektet ditt og hva du skal logge",x="Prosjekt ikke funnet",G="Tilbake til dashbord",K="Skjul QR",q="Vis QR",J="Skann for å se prosjekt offentlig",U="Legg til ny loggoppføring",W="Loggoppføring",z="Beskriv hva du jobbet med...",H="Legg ved bilde (valgfritt)",X="Legg til loggoppføring",Y="Loggoppføringer",Z="Ingen loggoppføringer ennå. Legg til din første oppføring ovenfor!",_="Loggvedlegg",$="Kunne ikke generere bildetekst. Prøv igjen.",ee="Forhåndsvisning",te="Prosjektet du leter etter finnes ikke eller har blitt fjernet.",oe="Skrivebeskyttet visning",ne="Opprettet",re="Prosjektlogg",se="oppføring",ge="oppføringer",ie="Dette prosjektet har ingen loggoppføringer å vise.",ce="Drevet av",ae="Profesjonell jobblogging",pe="Logg inn på kontoen din",le="Opprett din konto",de={appTitle:e,dashboard:t,createProject:o,projectName:n,description:r,save:s,cancel:g,addLog:i,logText:c,uploadImage:a,generateCaption:p,viewProject:l,shareProject:d,scanQRCode:k,publicView:j,loading:v,error:b,signIn:f,signUp:L,signOut:P,myProjects:u,noProjects:m,createFirstProject:h,projectCreated:y,logAdded:D,imageUploaded:w,generatingCaption:N,enterLogText:R,enterProjectName:S,enterDescription:T,manageProjects:O,getStartedMessage:A,startNewProject:C,projectDetails:E,projectNameRequired:Q,projectNameMinLength:B,projectDescriptionRequired:I,projectDescriptionMinLength:M,failedToCreateProject:V,describeProject:F,projectNotFound:x,backToDashboard:G,hideQR:K,showQR:q,scanToView:J,addNewLogEntry:U,logEntry:W,describeWork:z,attachImage:H,addLogEntry:X,logEntries:Y,noLogEntries:Z,logAttachment:_,failedToGenerateCaption:$,preview:ee,projectNotFoundMessage:te,readOnlyView:oe,createdOn:ne,projectLog:re,entry:se,entries:ge,noLogEntriesPublic:ie,poweredBy:ce,professionalJobLogging:ae,signInToAccount:pe,createAccount:le};export{i as addLog,X as addLogEntry,U as addNewLogEntry,e as appTitle,H as attachImage,G as backToDashboard,g as cancel,le as createAccount,h as createFirstProject,o as createProject,ne as createdOn,t as dashboard,de as default,F as describeProject,z as describeWork,r as description,T as enterDescription,R as enterLogText,S as enterProjectName,ge as entries,se as entry,b as error,V as failedToCreateProject,$ as failedToGenerateCaption,p as generateCaption,N as generatingCaption,A as getStartedMessage,K as hideQR,w as imageUploaded,v as loading,D as logAdded,_ as logAttachment,Y as logEntries,W as logEntry,c as logText,O as manageProjects,u as myProjects,Z as noLogEntries,ie as noLogEntriesPublic,m as noProjects,ce as poweredBy,ee as preview,ae as professionalJobLogging,y as projectCreated,M as projectDescriptionMinLength,I as projectDescriptionRequired,E as projectDetails,re as projectLog,n as projectName,B as projectNameMinLength,Q as projectNameRequired,x as projectNotFound,te as projectNotFoundMessage,j as publicView,oe as readOnlyView,s as save,k as scanQRCode,J as scanToView,d as shareProject,q as showQR,f as signIn,pe as signInToAccount,P as signOut,L as signUp,C as startNewProject,a as uploadImage,l as viewProject};

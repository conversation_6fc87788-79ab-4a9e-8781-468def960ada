const t="",o="",n="",c="",e="",s="",r="",a="",i="",d="",g="",p="",j="",l="",P="",u="",m="",L="",h="",w="",b="",N="",T="",y="",C="",E="",D="",R="",f="",v="",A="",I="",M="",x="",F="",O="",Q="",V="",k="",q="",U="",B="",G="",J="",S="",W="",z="",H="",K="",X="",Y="",Z="",_="",$="",tt="",ot="",nt="",ct="",et="",st="",rt="",at="",it="",dt="",gt="",pt="",jt={appTitle:t,dashboard:o,createProject:n,projectName:c,description:e,save:s,cancel:r,addLog:a,logText:i,uploadImage:d,generateCaption:g,viewProject:p,shareProject:j,scanQRCode:l,publicView:P,loading:u,error:m,signIn:L,signUp:h,signOut:w,myProjects:b,noProjects:N,createFirstProject:T,projectCreated:y,logAdded:C,imageUploaded:E,generatingCaption:D,enterLogText:R,enterProjectName:f,enterDescription:v,manageProjects:A,getStartedMessage:I,startNewProject:M,projectDetails:x,projectNameRequired:F,projectNameMinLength:O,projectDescriptionRequired:Q,projectDescriptionMinLength:V,failedToCreateProject:k,describeProject:q,projectNotFound:U,backToDashboard:B,hideQR:G,showQR:J,scanToView:S,addNewLogEntry:W,logEntry:z,describeWork:H,attachImage:K,addLogEntry:X,logEntries:Y,noLogEntries:Z,logAttachment:_,failedToGenerateCaption:$,preview:tt,projectNotFoundMessage:ot,readOnlyView:nt,createdOn:ct,projectLog:et,entry:st,entries:rt,noLogEntriesPublic:at,poweredBy:it,professionalJobLogging:dt,signInToAccount:gt,createAccount:pt};export{a as addLog,X as addLogEntry,W as addNewLogEntry,t as appTitle,K as attachImage,B as backToDashboard,r as cancel,pt as createAccount,T as createFirstProject,n as createProject,ct as createdOn,o as dashboard,jt as default,q as describeProject,H as describeWork,e as description,v as enterDescription,R as enterLogText,f as enterProjectName,rt as entries,st as entry,m as error,k as failedToCreateProject,$ as failedToGenerateCaption,g as generateCaption,D as generatingCaption,I as getStartedMessage,G as hideQR,E as imageUploaded,u as loading,C as logAdded,_ as logAttachment,Y as logEntries,z as logEntry,i as logText,A as manageProjects,b as myProjects,Z as noLogEntries,at as noLogEntriesPublic,N as noProjects,it as poweredBy,tt as preview,dt as professionalJobLogging,y as projectCreated,V as projectDescriptionMinLength,Q as projectDescriptionRequired,x as projectDetails,et as projectLog,c as projectName,O as projectNameMinLength,F as projectNameRequired,U as projectNotFound,ot as projectNotFoundMessage,P as publicView,nt as readOnlyView,s as save,l as scanQRCode,S as scanToView,j as shareProject,J as showQR,L as signIn,gt as signInToAccount,w as signOut,h as signUp,M as startNewProject,d as uploadImage,p as viewProject};
